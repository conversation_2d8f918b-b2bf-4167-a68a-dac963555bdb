<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品Excel导入功能示例</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            color: #666;
            margin-top: 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        input[type="file"] {
            margin: 10px 0;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background-color: #007bff;
            transition: width 0.3s ease;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>商品Excel导入功能示例</h1>
        
        <!-- 模板下载区域 -->
        <div class="section">
            <h3>1. 下载导入模板</h3>
            <p>首先下载Excel模板，模板包含所有字段说明和示例数据。</p>
            <button onclick="downloadTemplate()">下载Excel模板</button>
            <div id="downloadResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- 文件上传区域 -->
        <div class="section">
            <h3>2. 上传Excel文件</h3>
            <p>选择填写好的Excel文件进行上传导入。支持.xlsx和.xls格式，最大10MB。</p>
            <input type="file" id="fileInput" accept=".xlsx,.xls" onchange="handleFileSelect()">
            <br>
            <button id="uploadBtn" onclick="uploadFile()" disabled>开始导入</button>
            <button onclick="clearResults()">清空结果</button>
            
            <div id="uploadProgress" class="progress" style="display: none;">
                <div id="progressBar" class="progress-bar" style="width: 0%;"></div>
            </div>
            
            <div id="uploadResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- 导入结果区域 -->
        <div class="section">
            <h3>3. 导入结果</h3>
            <div id="importSummary"></div>
            <div id="importDetails"></div>
        </div>
    </div>

    <script>
        // API基础URL，根据实际部署情况修改
        const API_BASE_URL = '/adminapi/product/product';
        
        // 下载模板
        function downloadTemplate() {
            const resultDiv = document.getElementById('downloadResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在生成模板...';
            
            // 创建下载链接
            const link = document.createElement('a');
            link.href = API_BASE_URL + '/import/template';
            link.download = '商品导入模板.xlsx';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            resultDiv.className = 'result success';
            resultDiv.textContent = '模板下载已开始，请查看浏览器下载文件夹。';
        }
        
        // 处理文件选择
        function handleFileSelect() {
            const fileInput = document.getElementById('fileInput');
            const uploadBtn = document.getElementById('uploadBtn');
            
            if (fileInput.files.length > 0) {
                const file = fileInput.files[0];
                const fileName = file.name;
                const fileSize = (file.size / 1024 / 1024).toFixed(2);
                
                // 检查文件类型
                if (!fileName.endsWith('.xlsx') && !fileName.endsWith('.xls')) {
                    alert('请选择Excel文件（.xlsx或.xls格式）');
                    fileInput.value = '';
                    uploadBtn.disabled = true;
                    return;
                }
                
                // 检查文件大小
                if (file.size > 10 * 1024 * 1024) {
                    alert('文件大小不能超过10MB');
                    fileInput.value = '';
                    uploadBtn.disabled = true;
                    return;
                }
                
                uploadBtn.disabled = false;
                uploadBtn.textContent = `导入文件: ${fileName} (${fileSize}MB)`;
            } else {
                uploadBtn.disabled = true;
                uploadBtn.textContent = '开始导入';
            }
        }
        
        // 上传文件
        function uploadFile() {
            const fileInput = document.getElementById('fileInput');
            const uploadBtn = document.getElementById('uploadBtn');
            const progressDiv = document.getElementById('uploadProgress');
            const progressBar = document.getElementById('progressBar');
            const resultDiv = document.getElementById('uploadResult');
            
            if (fileInput.files.length === 0) {
                alert('请先选择文件');
                return;
            }
            
            const file = fileInput.files[0];
            const formData = new FormData();
            formData.append('file', file);
            
            // 显示进度条
            progressDiv.style.display = 'block';
            progressBar.style.width = '0%';
            uploadBtn.disabled = true;
            uploadBtn.textContent = '导入中...';
            
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在上传文件并处理数据...';
            
            // 模拟进度更新
            let progress = 0;
            const progressInterval = setInterval(() => {
                progress += Math.random() * 30;
                if (progress > 90) progress = 90;
                progressBar.style.width = progress + '%';
            }, 200);
            
            // 发送请求
            fetch(API_BASE_URL + '/import', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                clearInterval(progressInterval);
                progressBar.style.width = '100%';
                
                setTimeout(() => {
                    progressDiv.style.display = 'none';
                    uploadBtn.disabled = false;
                    uploadBtn.textContent = '开始导入';
                    
                    if (data.code === 200) {
                        showImportResult(data.data);
                        resultDiv.className = 'result success';
                        resultDiv.textContent = '导入完成！请查看下方详细结果。';
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.textContent = '导入失败：' + (data.message || '未知错误');
                    }
                }, 500);
            })
            .catch(error => {
                clearInterval(progressInterval);
                progressDiv.style.display = 'none';
                uploadBtn.disabled = false;
                uploadBtn.textContent = '开始导入';
                
                resultDiv.className = 'result error';
                resultDiv.textContent = '网络错误：' + error.message;
                console.error('导入失败:', error);
            });
        }
        
        // 显示导入结果
        function showImportResult(result) {
            const summaryDiv = document.getElementById('importSummary');
            const detailsDiv = document.getElementById('importDetails');
            
            // 显示统计信息
            const duration = result.duration ? (result.duration / 1000).toFixed(2) + '秒' : '未知';
            summaryDiv.innerHTML = `
                <h4>导入统计</h4>
                <table>
                    <tr><td>总行数</td><td>${result.totalRows}</td></tr>
                    <tr><td>成功导入</td><td style="color: green;">${result.successCount}</td></tr>
                    <tr><td>导入失败</td><td style="color: red;">${result.failureCount}</td></tr>
                    <tr><td>跳过行数</td><td style="color: orange;">${result.skipCount}</td></tr>
                    <tr><td>处理耗时</td><td>${duration}</td></tr>
                    <tr><td>全部成功</td><td>${result.allSuccess ? '是' : '否'}</td></tr>
                </table>
            `;
            
            // 显示详细信息
            let detailsHtml = '';
            
            if (result.failureList && result.failureList.length > 0) {
                detailsHtml += '<h4 style="color: red;">失败记录</h4>';
                detailsHtml += '<table><tr><th>行号</th><th>商品名称</th><th>错误信息</th></tr>';
                result.failureList.forEach(item => {
                    detailsHtml += `<tr>
                        <td>${item.rowNumber}</td>
                        <td>${item.name || '未知'}</td>
                        <td style="color: red;">${item.errorMessage}</td>
                    </tr>`;
                });
                detailsHtml += '</table>';
            }
            
            if (result.successList && result.successList.length > 0) {
                detailsHtml += '<h4 style="color: green;">成功记录（前10条）</h4>';
                detailsHtml += '<table><tr><th>行号</th><th>商品名称</th><th>分类</th><th>价格</th><th>库存</th></tr>';
                result.successList.slice(0, 10).forEach(item => {
                    detailsHtml += `<tr>
                        <td>${item.rowNumber}</td>
                        <td>${item.name}</td>
                        <td>${item.categoryName || item.categoryId}</td>
                        <td>¥${item.price}</td>
                        <td>${item.stock}</td>
                    </tr>`;
                });
                detailsHtml += '</table>';
                
                if (result.successList.length > 10) {
                    detailsHtml += `<p>还有 ${result.successList.length - 10} 条成功记录未显示...</p>`;
                }
            }
            
            detailsDiv.innerHTML = detailsHtml;
        }
        
        // 清空结果
        function clearResults() {
            document.getElementById('uploadResult').style.display = 'none';
            document.getElementById('importSummary').innerHTML = '';
            document.getElementById('importDetails').innerHTML = '';
            document.getElementById('fileInput').value = '';
            document.getElementById('uploadBtn').disabled = true;
            document.getElementById('uploadBtn').textContent = '开始导入';
        }
    </script>
</body>
</html>
