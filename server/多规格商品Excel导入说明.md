# 多规格商品Excel导入详细说明

## 概述

本文档详细说明如何通过Excel导入多规格商品。多规格商品是指具有多个规格维度（如颜色、尺寸、型号等）的商品，每个规格组合对应一个独立的SKU。

## 单规格 vs 多规格

### 单规格商品（规格类型=0）
- 商品只有一个SKU
- 直接填写价格、库存等字段
- 规格定义和SKU列表字段留空

### 多规格商品（规格类型=1）
- 商品有多个SKU，每个SKU对应不同的规格组合
- 需要填写规格定义和SKU列表（JSON格式）
- 单规格的价格、库存字段可以留空或填0

## 多规格商品字段说明

### 1. 规格定义字段（specItems）

**字段名：** `规格定义(JSON格式-多规格)`

**作用：** 定义商品有哪些规格类型和规格值

**格式：** JSON数组，每个元素包含规格类型和规格值列表

**结构：**
```json
[
  {
    "value": "规格类型名称",
    "detail": [
      {"value": "规格值1", "pic": "规格图片URL1"},
      {"value": "规格值2", "pic": "规格图片URL2"}
    ]
  }
]
```

**示例：**
```json
[
  {
    "value": "颜色",
    "detail": [
      {"value": "红色", "pic": "http://example.com/red.jpg"},
      {"value": "蓝色", "pic": "http://example.com/blue.jpg"},
      {"value": "绿色", "pic": ""}
    ]
  },
  {
    "value": "尺寸",
    "detail": [
      {"value": "S", "pic": ""},
      {"value": "M", "pic": ""},
      {"value": "L", "pic": ""}
    ]
  }
]
```

### 2. SKU列表字段（skuAttrs）

**字段名：** `SKU列表(JSON格式-多规格)`

**作用：** 定义每个SKU的具体信息（价格、库存、规格组合等）

**格式：** JSON数组，每个元素代表一个SKU

**结构：**
```json
[
  {
    "detail": {
      "规格类型1": "规格值1",
      "规格类型2": "规格值2"
    },
    "price": 价格,
    "cost": 成本价,
    "ot_price": 划线价,
    "stock": 库存数量,
    "bar_code": "商品编码",
    "weight": 重量,
    "volume": 体积,
    "pic": "规格图片URL",
    "brokerage": 一级佣金,
    "brokerage_two": 二级佣金,
    "quota": 限购数量,
    "quota_show": 是否显示限购
  }
]
```

**示例：**
```json
[
  {
    "detail": {"颜色": "红色", "尺寸": "S"},
    "price": 99.00,
    "cost": 60.00,
    "ot_price": 120.00,
    "stock": 50,
    "bar_code": "TS001",
    "weight": 0.3,
    "volume": 0.002,
    "pic": "http://example.com/red-s.jpg",
    "brokerage": 10.00,
    "brokerage_two": 5.00,
    "quota": 5,
    "quota_show": true
  },
  {
    "detail": {"颜色": "红色", "尺寸": "M"},
    "price": 99.00,
    "cost": 60.00,
    "ot_price": 120.00,
    "stock": 30,
    "bar_code": "TS002",
    "weight": 0.35,
    "volume": 0.0025,
    "pic": "http://example.com/red-m.jpg",
    "brokerage": 10.00,
    "brokerage_two": 5.00,
    "quota": 5,
    "quota_show": true
  },
  {
    "detail": {"颜色": "蓝色", "尺寸": "S"},
    "price": 109.00,
    "cost": 65.00,
    "ot_price": 130.00,
    "stock": 40,
    "bar_code": "TS003",
    "weight": 0.3,
    "volume": 0.002,
    "pic": "http://example.com/blue-s.jpg",
    "brokerage": 12.00,
    "brokerage_two": 6.00,
    "quota": 3,
    "quota_show": true
  }
]
```

## 完整示例

### 示例1：服装类多规格商品

**商品信息：**
- 商品名称：时尚T恤
- 规格：颜色（红色、蓝色）× 尺寸（S、M、L）
- 共6个SKU

**Excel填写示例：**

| 字段 | 值 |
|------|-----|
| 商品名称* | 时尚T恤 |
| 商品分类ID* | 1002 |
| 规格类型 | 1 |
| 规格定义(JSON格式-多规格) | `[{"value":"颜色","detail":[{"value":"红色","pic":""},{"value":"蓝色","pic":""}]},{"value":"尺寸","detail":[{"value":"S","pic":""},{"value":"M","pic":""},{"value":"L","pic":""}]}]` |
| SKU列表(JSON格式-多规格) | `[{"detail":{"颜色":"红色","尺寸":"S"},"price":99,"cost":60,"stock":50,"bar_code":"TS001"},{"detail":{"颜色":"红色","尺寸":"M"},"price":99,"cost":60,"stock":30,"bar_code":"TS002"},{"detail":{"颜色":"红色","尺寸":"L"},"price":99,"cost":60,"stock":20,"bar_code":"TS003"},{"detail":{"颜色":"蓝色","尺寸":"S"},"price":109,"cost":65,"stock":40,"bar_code":"TS004"},{"detail":{"颜色":"蓝色","尺寸":"M"},"price":109,"cost":65,"stock":25,"bar_code":"TS005"},{"detail":{"颜色":"蓝色","尺寸":"L"},"price":109,"cost":65,"stock":15,"bar_code":"TS006"}]` |

### 示例2：电子产品多规格商品

**商品信息：**
- 商品名称：智能手机
- 规格：内存（64GB、128GB、256GB）× 颜色（黑色、白色）
- 共6个SKU

**规格定义：**
```json
[
  {
    "value": "内存",
    "detail": [
      {"value": "64GB", "pic": ""},
      {"value": "128GB", "pic": ""},
      {"value": "256GB", "pic": ""}
    ]
  },
  {
    "value": "颜色",
    "detail": [
      {"value": "黑色", "pic": "http://example.com/black.jpg"},
      {"value": "白色", "pic": "http://example.com/white.jpg"}
    ]
  }
]
```

**SKU列表：**
```json
[
  {"detail":{"内存":"64GB","颜色":"黑色"},"price":2999,"cost":2200,"stock":100,"bar_code":"PHONE001"},
  {"detail":{"内存":"64GB","颜色":"白色"},"price":2999,"cost":2200,"stock":80,"bar_code":"PHONE002"},
  {"detail":{"内存":"128GB","颜色":"黑色"},"price":3499,"cost":2600,"stock":60,"bar_code":"PHONE003"},
  {"detail":{"内存":"128GB","颜色":"白色"},"price":3499,"cost":2600,"stock":50,"bar_code":"PHONE004"},
  {"detail":{"内存":"256GB","颜色":"黑色"},"price":3999,"cost":3000,"stock":40,"bar_code":"PHONE005"},
  {"detail":{"内存":"256GB","颜色":"白色"},"price":3999,"cost":3000,"stock":30,"bar_code":"PHONE006"}
]
```

## 注意事项

### 1. JSON格式要求
- 必须是有效的JSON格式
- 字符串值必须用双引号包围
- 数值类型不需要引号
- 布尔值使用true/false

### 2. 规格值匹配
- SKU列表中的规格值必须与规格定义中的规格值完全匹配
- 规格类型名称必须一致（区分大小写）

### 3. 必填字段
- 每个SKU必须包含price（价格）和stock（库存）
- detail字段必须包含所有规格类型的值

### 4. 数据验证
- 系统会验证JSON格式的正确性
- 会检查规格值的匹配性
- 会验证价格和库存的合理性

## 常见错误

### 1. JSON格式错误
```
错误：{"value":"颜色","detail":[{"value":"红色","pic":""}]}  // 缺少外层数组
正确：[{"value":"颜色","detail":[{"value":"红色","pic":""}]}]
```

### 2. 规格值不匹配
```
规格定义：{"value":"颜色","detail":[{"value":"红色","pic":""}]}
SKU错误：{"detail":{"颜色":"红"},"price":99}  // "红"与"红色"不匹配
SKU正确：{"detail":{"颜色":"红色"},"price":99}
```

### 3. 缺少必要字段
```
错误：{"detail":{"颜色":"红色"},"stock":50}  // 缺少price字段
正确：{"detail":{"颜色":"红色"},"price":99,"stock":50}
```

## 工具推荐

### JSON格式化工具
- 在线JSON格式化：https://jsonformatter.org/
- VS Code插件：JSON Tools
- 浏览器开发者工具的Console

### Excel技巧
- 使用较宽的列来容纳JSON数据
- 可以先在文本编辑器中编写JSON，再复制到Excel
- 建议使用Excel的"自动换行"功能来查看长JSON

## 测试建议

1. **先测试简单规格**：从2个规格值开始测试
2. **逐步增加复杂度**：确认简单规格正常后再增加规格数量
3. **验证JSON格式**：使用在线工具验证JSON格式正确性
4. **小批量测试**：先导入1-2个商品验证功能正常
