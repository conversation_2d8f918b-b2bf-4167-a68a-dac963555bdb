# 定时订单导出功能说明

## 功能概述

本功能实现了每隔两小时自动导出订单信息到Excel文件的定时任务。导出的订单按时间段进行分组和颜色标记：
- **上半场（前一日14点-24点）**：绿色背景
- **下半场（当日0点-14点）**：红色背景

## 实现组件

### 1. 主应用类修改
**文件**: `WjsyApplication.java`
- 添加了 `@EnableScheduling` 注解启用Spring定时任务功能

### 2. 订单导出服务类
**文件**: `OrderExportService.java`
- 负责具体的订单导出逻辑
- 包含Excel文件生成、样式设置、数据过滤等功能
- 主要方法：`exportAllOrdersToExcel()`

### 3. 定时任务调度类
**文件**: `TaskSchedule.java`
- 添加了 `scheduledOrderExport()` 方法
- 使用 `@Scheduled(fixedRate = 2 * 60 * 60 * 1000)` 注解，每2小时执行一次
- 调用 `OrderExportService` 的导出方法

## 定时任务配置

### 执行频率
- **间隔时间**: 每2小时执行一次
- **执行方式**: 固定间隔（fixedRate）
- **启动后**: 应用启动后立即开始计时

### 时间配置说明
```java
@Scheduled(fixedRate = 2 * 60 * 60 * 1000) // 2小时 = 2 * 60 * 60 * 1000 毫秒
```

如需修改执行频率，可以调整这个数值：
- 1小时: `1 * 60 * 60 * 1000`
- 30分钟: `30 * 60 * 1000`
- 4小时: `4 * 60 * 60 * 1000`

## 导出功能特性

### 数据过滤
- 只导出已支付的订单（payStatus = 2）
- 时间范围：从昨天14点开始的订单
- 按创建时间升序排序

### Excel格式
- **文件名格式**: `订单导出_yyyyMMddHHmmss.xlsx`
- **保存位置**: 项目根目录
- **表头**: 日期时间段、订单编号、收货人、收货地址、收货电话、商品信息(含规格)、创建时间、备注

### 颜色标记
- **上半场（14-24点）**: 浅绿色背景
- **下半场（0-14点）**: 珊瑚色背景
- **时间列**: 根据时间段使用对应的背景色

## 使用方法

### 1. 启动应用
确保Spring Boot应用正常启动，定时任务会自动开始运行。

### 2. 查看日志
定时任务执行时会输出相关日志：
```
开始执行定时订单导出任务...
开始执行订单导出任务...
找到订单总数: xxx
过滤后订单数量: xxx
订单导出完成！
导出文件路径: /path/to/订单导出_20250727123456.xlsx
导出订单数量: xxx
定时订单导出任务执行完成
```

### 3. 查看导出文件
导出的Excel文件会保存在项目根目录下，文件名包含时间戳。

## 测试方法

### 1. 单元测试
运行测试类 `ScheduledTaskTest.java`：
```bash
mvn test -Dtest=ScheduledTaskTest
```

### 2. 手动测试
可以直接调用服务方法进行测试：
```java
@Autowired
private OrderExportService orderExportService;

// 手动执行导出
orderExportService.exportAllOrdersToExcel();
```

## 注意事项

### 1. 文件权限
确保应用有权限在项目根目录创建和写入文件。

### 2. 磁盘空间
定时导出会持续生成Excel文件，注意定期清理旧文件以避免磁盘空间不足。

### 3. 性能考虑
- 如果订单数量很大，导出过程可能耗时较长
- 建议在业务低峰期执行
- 可以考虑添加分页或限制导出数量

### 4. 错误处理
- 导出过程中的异常会被捕获并记录日志
- 不会影响应用的正常运行
- 建议监控日志以及时发现问题

## 自定义配置

### 修改执行时间
如需修改定时任务的执行时间，可以：

1. **固定间隔**（当前使用）:
```java
@Scheduled(fixedRate = 2 * 60 * 60 * 1000) // 每2小时
```

2. **Cron表达式**:
```java
@Scheduled(cron = "0 0 */2 * * ?") // 每2小时的整点执行
@Scheduled(cron = "0 0 14 * * ?") // 每天14:00执行
```

### 修改导出条件
在 `OrderExportService.exportAllOrdersToExcel()` 方法中修改过滤条件：
```java
.filter(order -> {
    // 自定义过滤逻辑
    return order.getPayStatus().getValue() == 2; // 只导出已支付订单
})
```

## 故障排除

### 1. 定时任务不执行
- 检查 `@EnableScheduling` 注解是否添加
- 检查应用是否正常启动
- 查看日志是否有相关错误信息

### 2. 导出文件为空
- 检查数据库中是否有符合条件的订单
- 检查过滤条件是否过于严格
- 查看日志中的订单数量统计

### 3. 文件生成失败
- 检查文件路径权限
- 检查磁盘空间是否充足
- 查看异常日志信息
