#!/bin/bash

echo "========================================"
echo "   Spring Boot 热部署开发模式启动"
echo "========================================"
echo

# 设置环境变量
export SPRING_PROFILES_ACTIVE=dev
export JAVA_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC"

echo "当前配置:"
echo "- 环境: $SPRING_PROFILES_ACTIVE"
echo "- JVM参数: $JAVA_OPTS"
echo "- 端口: 9999"
echo "- 热部署: 启用"
echo "- LiveReload: 启用 (端口35729)"
echo

echo "启动Spring Boot应用..."
echo "提示: 修改Java文件后会自动重启服务"
echo "提示: 按 Ctrl+C 停止服务"
echo

# 启动应用
mvn spring-boot:run -Dspring-boot.run.profiles=dev -Dspring-boot.run.jvmArguments="$JAVA_OPTS"
