# 定时销量更新功能说明

## 功能概述

本功能实现了每小时自动随机增加商品销量的定时任务。系统会随机选择部分在售商品，为每个选中的商品随机增加1-9的销量，模拟真实的销售情况。

## 实现组件

### 1. 销量更新服务类
**文件**: `SalesVolumeUpdateService.java`
- 负责具体的销量更新逻辑
- 随机选择在售商品
- 为每个选中商品随机增加1-9的销量
- 主要方法：`randomUpdateSalesVolume()`

### 2. 定时任务调度类
**文件**: `TaskSchedule.java`
- 添加了 `scheduledSalesVolumeUpdate()` 方法
- 使用 `@Scheduled(fixedRate = 60 * 60 * 1000)` 注解，每1小时执行一次
- 调用 `SalesVolumeUpdateService` 的更新方法

### 3. 测试类
**文件**: `SalesVolumeUpdateTest.java`
- 提供单元测试方法
- 可以手动测试销量更新功能
- 验证定时任务是否正常工作

## 定时任务配置

### 执行频率
- **间隔时间**: 每1小时执行一次
- **执行方式**: 固定间隔（fixedRate）
- **启动后**: 应用启动后立即开始计时

### 时间配置说明
```java
@Scheduled(fixedRate = 60 * 60 * 1000) // 1小时 = 60 * 60 * 1000 毫秒
```

如需修改执行频率，可以调整这个数值：
- 30分钟: `30 * 60 * 1000`
- 2小时: `2 * 60 * 60 * 1000`
- 6小时: `6 * 60 * 60 * 1000`

## 销量更新逻辑

### 商品筛选条件
- 只选择在售商品（`isOnSale = true`）
- 排除已删除商品（`isDelete = false`）
- 如果没有符合条件的商品，跳过本次更新

### 随机选择策略
- 每次随机选择30%-70%的在售商品进行更新
- 使用随机排序确保每次选择的商品不同
- 最少更新1个商品，最多不超过在售商品总数的70%

### 销量增加规则
- 每个选中商品随机增加1-9的销量
- 如果商品当前销量为null，则从0开始计算
- 更新后的销量 = 当前销量 + 随机增加量（1-9）

## 使用方法

### 1. 启动应用
确保Spring Boot应用正常启动，定时任务会自动开始运行。

### 2. 查看日志
定时任务执行时会输出相关日志：
```
开始执行定时销量更新任务...
开始执行随机销量更新...
找到在售商品总数: xxx
本次将更新 xxx 个商品的销量
销量更新完成！成功更新 xxx 个商品，总计增加销量 xxx
定时销量更新任务执行完成
```

### 3. 手动测试
可以通过测试类手动触发销量更新：
```java
@Autowired
private SalesVolumeUpdateService salesVolumeUpdateService;

// 手动执行销量更新
salesVolumeUpdateService.manualUpdateSalesVolume();
```

## 测试方法

### 1. 单元测试
运行测试类 `SalesVolumeUpdateTest.java`：
```bash
mvn test -Dtest=SalesVolumeUpdateTest
```

### 2. 测试方法说明
- `testSalesVolumeUpdateService()`: 测试销量更新服务
- `testScheduledSalesVolumeUpdate()`: 测试定时任务调度
- `testManualSalesVolumeUpdate()`: 测试手动触发更新

## 日志记录

### 执行日志
- 任务开始和结束时间
- 在售商品总数统计
- 本次更新的商品数量
- 总计增加的销量
- 每个商品的详细更新信息（debug级别）

### 示例日志
```
开始执行定时销量更新任务...
开始执行随机销量更新...
找到在售商品总数: 150
本次将更新 75 个商品的销量
销量更新完成！成功更新 75 个商品，总计增加销量 375
定时销量更新任务执行完成
```

## 错误处理

### 异常捕获
- 单个商品更新异常不会影响其他商品
- 整体任务异常会被记录但不会影响应用运行
- 详细的错误日志便于问题排查

### 数据验证
- 检查商品是否存在
- 验证商品状态（在售、未删除）
- 确保销量数值的合理性

## 注意事项

### 1. 数据库性能
- 每次更新会涉及多个商品记录的修改
- 建议在业务低峰期执行
- 如果商品数量很大，可以考虑分批处理

### 2. 销量真实性
- 增加的销量是模拟数据，用于展示目的
- 不会影响实际的库存和订单数据
- 建议在生产环境中谨慎使用

### 3. 配置建议
- 可以根据业务需求调整执行频率
- 可以调整每次选择商品的比例（30%-70%）
- 可以调整销量增加的范围（1-9）

## 自定义配置

### 修改执行时间
如需修改定时任务的执行时间，可以：

1. **固定间隔**（当前使用）:
```java
@Scheduled(fixedRate = 60 * 60 * 1000) // 每1小时
```

2. **Cron表达式**:
```java
@Scheduled(cron = "0 0 * * * ?") // 每小时的整点执行
@Scheduled(cron = "0 0 */2 * * ?") // 每2小时的整点执行
```

### 修改选择比例
在 `SalesVolumeUpdateService.java` 中修改：
```java
// 当前：随机选择30%-70%的商品
int minSelectCount = Math.max(1, (int) (onSaleGoods.size() * 0.3));
int maxSelectCount = Math.max(minSelectCount, (int) (onSaleGoods.size() * 0.7));

// 可以调整为其他比例，例如20%-50%：
int minSelectCount = Math.max(1, (int) (onSaleGoods.size() * 0.2));
int maxSelectCount = Math.max(minSelectCount, (int) (onSaleGoods.size() * 0.5));
```

### 修改销量增加范围
在 `SalesVolumeUpdateService.java` 中修改：
```java
// 当前：随机增加1-9的销量
int increaseAmount = random.nextInt(9) + 1;

// 可以调整为其他范围，例如1-5：
int increaseAmount = random.nextInt(5) + 1;
```

## 监控建议

### 1. 日志监控
- 监控定时任务的执行日志
- 关注异常和错误信息
- 统计每次更新的商品数量和销量增加

### 2. 数据监控
- 定期检查商品销量的变化趋势
- 确保销量增长符合预期
- 避免销量数据异常增长

### 3. 性能监控
- 监控定时任务的执行时间
- 关注数据库的性能影响
- 必要时优化查询和更新逻辑
