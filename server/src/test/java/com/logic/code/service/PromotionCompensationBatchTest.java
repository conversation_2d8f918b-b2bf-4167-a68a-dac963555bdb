package com.logic.code.service;

import com.logic.code.WjsyApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Map;

/**
 * 推广补偿批量处理测试类
 */
@Slf4j
@SpringBootTest(classes = WjsyApplication.class)
public class PromotionCompensationBatchTest {

    @Autowired
    private PromotionCompensationService promotionCompensationService;

    /**
     * 测试批量补偿功能
     */
    @Test
    public void testBatchCompensation() {
        try {
            log.info("开始测试批量推广收益数据补偿...");
            
            // 执行小批量补偿测试（每批10条）
            Map<String, Object> result = promotionCompensationService.compensateMissingEarningsBatch(10);
            
            log.info("批量补偿测试结果: {}", result);
            
            // 验证结果
            Boolean success = (Boolean) result.get("success");
            if (success != null && success) {
                log.info("批量补偿测试成功完成");
            } else {
                log.error("批量补偿测试失败: {}", result.get("message"));
            }
            
        } catch (Exception e) {
            log.error("批量补偿测试执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 测试统计信息功能
     */
    @Test
    public void testStatistics() {
        try {
            log.info("开始测试统计信息功能...");
            
            // 获取统计信息
            Map<String, Object> stats = promotionCompensationService.getMigrationStats();
            
            log.info("统计信息: {}", stats);
            
            // 验证关键统计字段
            Long earningsRecordCount = (Long) stats.get("earningsRecordCount");
            Long ordersWithoutEarningsCount = (Long) stats.get("ordersWithoutEarningsCount");
            Long ordersWithUserPromoterOnlyCount = (Long) stats.get("ordersWithUserPromoterOnlyCount");
            
            log.info("推广收益记录数: {}", earningsRecordCount);
            log.info("缺少推广收益的订单数: {}", ordersWithoutEarningsCount);
            log.info("仅用户表有推广关系的订单数: {}", ordersWithUserPromoterOnlyCount);
            
        } catch (Exception e) {
            log.error("统计信息测试执行失败: {}", e.getMessage(), e);
        }
    }
}