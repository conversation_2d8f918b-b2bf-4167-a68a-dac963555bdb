package com.logic.code.service;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * StatisticService 优化测试
 * 验证 generateTrendData 方法从造假数据改为真实业务处理逻辑后的功能
 * 
 * <AUTHOR>
 * @date 2025/8/24
 */
@SpringBootTest
@ActiveProfiles("test")
public class StatisticServiceTest {

    @Autowired
    private StatisticService statisticService;

    /**
     * 测试商品趋势统计 - 基本功能
     */
    @Test
    public void testGetProductTrendStatistics_Basic() {
        // 测试最近7天的趋势数据
        String dateRange = "2024/01/01-2024/01/07";
        
        Map<String, Object> result = statisticService.getProductTrendStatistics(dateRange);
        
        // 验证返回结果结构
        assertNotNull(result);
        assertTrue(result.containsKey("xAxis"));
        assertTrue(result.containsKey("series"));
        
        // 验证日期轴数据
        assertNotNull(result.get("xAxis"));
        assertTrue(result.get("xAxis") instanceof java.util.List);
        
        // 验证系列数据
        assertNotNull(result.get("series"));
        assertTrue(result.get("series") instanceof java.util.List);
        
        @SuppressWarnings("unchecked")
        java.util.List<Map<String, Object>> series = (java.util.List<Map<String, Object>>) result.get("series");
        assertEquals(4, series.size()); // 应该有4个系列：浏览量、访客数、支付金额、退款金额
        
        // 验证每个系列的基本结构
        for (Map<String, Object> seriesItem : series) {
            assertTrue(seriesItem.containsKey("name"));
            assertTrue(seriesItem.containsKey("type"));
            assertTrue(seriesItem.containsKey("data"));
            assertEquals("line", seriesItem.get("type"));
        }
    }

    /**
     * 测试商品趋势统计 - 数据合理性
     */
    @Test
    public void testGetProductTrendStatistics_DataReasonableness() {
        String dateRange = "2024/01/01-2024/01/03"; // 3天数据
        
        Map<String, Object> result = statisticService.getProductTrendStatistics(dateRange);
        
        @SuppressWarnings("unchecked")
        java.util.List<String> xAxis = (java.util.List<String>) result.get("xAxis");
        assertEquals(3, xAxis.size()); // 应该有3个日期点
        
        @SuppressWarnings("unchecked")
        java.util.List<Map<String, Object>> series = (java.util.List<Map<String, Object>>) result.get("series");
        
        for (Map<String, Object> seriesItem : series) {
            @SuppressWarnings("unchecked")
            java.util.List<?> data = (java.util.List<?>) seriesItem.get("data");
            assertEquals(3, data.size()); // 每个系列应该有3个数据点
            
            // 验证数据都是非负数
            for (Object value : data) {
                if (value instanceof Number) {
                    assertTrue(((Number) value).doubleValue() >= 0, 
                        "数据值应该为非负数: " + value);
                }
            }
        }
    }

    /**
     * 测试商品趋势统计 - 空时间范围
     */
    @Test
    public void testGetProductTrendStatistics_EmptyDateRange() {
        // 测试空时间范围是否能正常处理
        String dateRange = "";
        
        Map<String, Object> result = statisticService.getProductTrendStatistics(dateRange);
        
        // 应该使用默认时间范围（最近7天）
        assertNotNull(result);
        assertTrue(result.containsKey("xAxis"));
        assertTrue(result.containsKey("series"));
    }

    /**
     * 测试商品趋势统计 - 无效时间格式
     */
    @Test
    public void testGetProductTrendStatistics_InvalidDateFormat() {
        // 测试无效时间格式是否能降级处理
        String dateRange = "invalid-date-format";
        
        try {
            Map<String, Object> result = statisticService.getProductTrendStatistics(dateRange);
            
            // 应该能正常返回结果（使用默认时间范围）
            assertNotNull(result);
            assertTrue(result.containsKey("xAxis"));
            assertTrue(result.containsKey("series"));
        } catch (Exception e) {
            fail("无效日期格式应该被优雅处理，而不应该抛出异常: " + e.getMessage());
        }
    }

    /**
     * 测试商品趋势统计 - 长时间范围性能
     */
    @Test
    public void testGetProductTrendStatistics_LongTimeRange() {
        // 测试较长时间范围的性能
        String dateRange = "2024/01/01-2024/01/31"; // 31天
        
        long startTime = System.currentTimeMillis();
        Map<String, Object> result = statisticService.getProductTrendStatistics(dateRange);
        long endTime = System.currentTimeMillis();
        
        long executionTime = endTime - startTime;
        
        // 验证返回结果
        assertNotNull(result);
        
        @SuppressWarnings("unchecked")
        java.util.List<String> xAxis = (java.util.List<String>) result.get("xAxis");
        assertEquals(31, xAxis.size()); // 应该有31个日期点
        
        // 性能验证：应该在5秒内完成（可根据实际情况调整）
        assertTrue(executionTime < 5000, 
            "31天数据查询执行时间过长: " + executionTime + "ms");
        
        System.out.println("31天趋势数据查询执行时间: " + executionTime + "ms");
    }

    /**
     * 测试数据类型一致性
     */
    @Test
    public void testGetProductTrendStatistics_DataTypes() {
        String dateRange = "2024/01/01-2024/01/02";
        
        Map<String, Object> result = statisticService.getProductTrendStatistics(dateRange);
        
        @SuppressWarnings("unchecked")
        java.util.List<Map<String, Object>> series = (java.util.List<Map<String, Object>>) result.get("series");
        
        for (Map<String, Object> seriesItem : series) {
            String name = (String) seriesItem.get("name");
            @SuppressWarnings("unchecked")
            java.util.List<?> data = (java.util.List<?>) seriesItem.get("data");
            
            for (Object value : data) {
                // 验证所有数值都是Number类型
                assertTrue(value instanceof Number, 
                    "系列 " + name + " 的数据应该是数值类型，实际类型: " + value.getClass().getSimpleName());
            }
        }
    }

    /**
     * 测试业务逻辑关联性 
     */
    @Test
    public void testGetProductTrendStatistics_BusinessLogic() {
        String dateRange = "2024/01/01-2024/01/02";
        
        Map<String, Object> result = statisticService.getProductTrendStatistics(dateRange);
        
        @SuppressWarnings("unchecked")
        java.util.List<Map<String, Object>> series = (java.util.List<Map<String, Object>>) result.get("series");
        
        // 找到各个系列的数据
        java.util.List<Number> browseData = null;
        java.util.List<Number> userData = null;
        java.util.List<Number> payPriceData = null;
        java.util.List<Number> refundPriceData = null;
        
        for (Map<String, Object> seriesItem : series) {
            String name = (String) seriesItem.get("name");
            @SuppressWarnings("unchecked")
            java.util.List<Number> data = (java.util.List<Number>) seriesItem.get("data");
            
            switch (name) {
                case "商品浏览量":
                    browseData = data;
                    break;
                case "商品访客数":
                    userData = data;
                    break;
                case "支付金额":
                    payPriceData = data;
                    break;
                case "退款金额":
                    refundPriceData = data;
                    break;
            }
        }
        
        // 验证业务逻辑关系
        assertNotNull(browseData);
        assertNotNull(userData);
        assertNotNull(payPriceData);
        assertNotNull(refundPriceData);
        
        // 验证退款金额应该小于等于支付金额
        for (int i = 0; i < payPriceData.size(); i++) {
            double payAmount = payPriceData.get(i).doubleValue();
            double refundAmount = refundPriceData.get(i).doubleValue();
            
            assertTrue(refundAmount <= payAmount, 
                String.format("第%d天退款金额(%.2f)不应大于支付金额(%.2f)", i+1, refundAmount, payAmount));
        }
        
        System.out.println("业务逻辑验证通过：退款金额始终不大于支付金额");
    }
}