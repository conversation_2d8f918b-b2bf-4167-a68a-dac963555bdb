package com.logic.code.service;

import com.logic.code.common.utils.GoodsExcelUtils;
import com.logic.code.model.dto.GoodsImportDTO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * 商品Excel导入功能测试
 * <AUTHOR>
 * @date 2025/7/16
 */
@SpringBootTest
public class GoodsImportTest {

    /**
     * 测试生成Excel模板
     */
    @Test
    public void testGenerateTemplate() {
        try {
            String filePath = System.getProperty("user.dir") + "/商品导入模板测试_" + System.currentTimeMillis() + ".xlsx";
            GoodsExcelUtils.generateTemplate(filePath);
            
            File file = new File(filePath);
            System.out.println("模板文件生成成功！");
            System.out.println("文件路径: " + filePath);
            System.out.println("文件大小: " + file.length() + " bytes");
            System.out.println("文件存在: " + file.exists());
            
        } catch (IOException e) {
            System.err.println("生成模板失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试读取Excel数据
     */
    @Test
    public void testReadExcelData() {
        try {
            // 首先生成一个测试模板
            String templatePath = System.getProperty("user.dir") + "/测试数据.xlsx";
            GoodsExcelUtils.generateTemplate(templatePath);
            
            // 读取Excel数据
            List<GoodsImportDTO> goodsList = GoodsExcelUtils.readGoodsFromExcel(templatePath);
            
            System.out.println("成功读取Excel数据！");
            System.out.println("读取到 " + goodsList.size() + " 条商品数据");
            
            for (GoodsImportDTO goods : goodsList) {
                System.out.println("第" + goods.getRowNumber() + "行: " + goods.getName() + 
                    " - 分类ID: " + goods.getCategoryId() + 
                    " - 价格: " + goods.getPrice() + 
                    " - 库存: " + goods.getStock());
            }
            
        } catch (IOException e) {
            System.err.println("读取Excel数据失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试数据验证
     */
    @Test
    public void testDataValidation() {
        try {
            // 生成测试模板
            String templatePath = System.getProperty("user.dir") + "/验证测试.xlsx";
            GoodsExcelUtils.generateTemplate(templatePath);
            
            // 读取数据
            List<GoodsImportDTO> goodsList = GoodsExcelUtils.readGoodsFromExcel(templatePath);
            
            System.out.println("数据验证测试：");
            for (GoodsImportDTO goods : goodsList) {
                System.out.println("商品: " + goods.getName());
                System.out.println("  - 是否有效: " + goods.getIsValid());
                System.out.println("  - 错误信息: " + goods.getErrorMessage());
                System.out.println("  - 处理状态: " + goods.getProcessStatus());
                System.out.println();
            }
            
        } catch (IOException e) {
            System.err.println("数据验证测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
