package com.logic.code.service;

import com.logic.code.common.enmus.OrderStatusEnum;
import com.logic.code.common.enmus.PayStatusEnum;
import com.logic.code.entity.order.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * PayService 测试类
 * 测试订单支付时更新交易单号和支付时间的功能
 */
public class PayServiceTest {

    @Mock
    private OrderService orderService;

    @Mock
    private UserService userService;

    @Mock
    private PointsService pointsService;

    @InjectMocks
    private PayService payService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testNotifyWithOrderId_ShouldUpdatePayTimeAndStatus() {
        // 准备测试数据
        Integer orderId = 1;
        Order mockOrder = new Order();
        mockOrder.setId(orderId);
        mockOrder.setOrderSn("TEST_ORDER_001");
        mockOrder.setPayStatus(PayStatusEnum.PENDING_PAYMENT);
        mockOrder.setOrderStatus(OrderStatusEnum.WAIT_PAY);

        // 模拟 orderService 行为
        when(orderService.queryById(orderId)).thenReturn(mockOrder);
        when(orderService.updateNotNull(any(Order.class))).thenReturn(1);

        // 执行测试
        boolean result = payService.notify(orderId);

        // 验证结果
        assertTrue(result);
        
        // 验证 orderService.updateNotNull 被调用，并且传入的订单对象包含正确的状态
        verify(orderService, times(1)).updateNotNull(argThat(order -> 
            order.getPayStatus() == PayStatusEnum.PAID &&
            order.getOrderStatus() == OrderStatusEnum.WAIT_SEND &&
            order.getPayTime() != null
        ));
    }

    @Test
    void testPrepayWithZeroAmount_ShouldSetPayTimeDirectly() {
        // 准备测试数据 - 0元订单
        Integer orderId = 2;
        Order mockOrder = new Order();
        mockOrder.setId(orderId);
        mockOrder.setOrderSn("TEST_ORDER_002");
        mockOrder.setActualPrice(BigDecimal.ZERO);
        mockOrder.setPayStatus(PayStatusEnum.PENDING_PAYMENT);
        mockOrder.setOrderStatus(OrderStatusEnum.WAIT_PAY);

        // 模拟 orderService 行为
        when(orderService.queryById(orderId)).thenReturn(mockOrder);
        when(orderService.updateNotNull(any(Order.class))).thenReturn(1);

        // 执行测试
        var result = payService.prepay(orderId);

        // 验证结果 - 0元订单应该返回null
        assertNull(result);
        
        // 验证 orderService.updateNotNull 被调用，并且传入的订单对象包含正确的状态和支付时间
        verify(orderService, times(1)).updateNotNull(argThat(order -> 
            order.getPayStatus() == PayStatusEnum.PAID &&
            order.getOrderStatus() == OrderStatusEnum.WAIT_SEND &&
            order.getPayTime() != null
        ));
    }
}
