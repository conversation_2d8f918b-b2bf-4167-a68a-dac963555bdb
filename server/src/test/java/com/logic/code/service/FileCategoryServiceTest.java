package com.logic.code.service;

import com.logic.code.entity.file.FileCategory;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 文件分类服务测试类
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class FileCategoryServiceTest {

    @Autowired
    private FileCategoryService fileCategoryService;

    @Test
    public void testCreateRootCategory() {
        // 创建根分类
        FileCategory category = new FileCategory();
        category.setTitle("测试根分类");
        category.setDescription("这是一个测试根分类");
        category.setParentId(0);

        FileCategory created = fileCategoryService.createCategory(category);

        assertNotNull(created);
        assertNotNull(created.getId());
        assertEquals("测试根分类", created.getTitle());
        assertEquals(0, created.getParentId().intValue());
        assertEquals(0, created.getLevel().intValue());
        assertEquals("0", created.getPath());
    }

    @Test
    public void testCreateSubCategory() {
        // 先创建父分类
        FileCategory parent = new FileCategory();
        parent.setTitle("父分类");
        parent.setParentId(0);
        FileCategory createdParent = fileCategoryService.createCategory(parent);

        // 创建子分类
        FileCategory child = new FileCategory();
        child.setTitle("子分类");
        child.setParentId(createdParent.getId());
        FileCategory createdChild = fileCategoryService.createCategory(child);

        assertNotNull(createdChild);
        assertEquals("子分类", createdChild.getTitle());
        assertEquals(createdParent.getId(), createdChild.getParentId());
        assertEquals(1, createdChild.getLevel().intValue());
        assertEquals("0," + createdParent.getId(), createdChild.getPath());
    }

    @Test
    public void testGetCategoryTree() {
        // 创建测试数据
        FileCategory root1 = new FileCategory();
        root1.setTitle("根分类1");
        root1.setParentId(0);
        FileCategory createdRoot1 = fileCategoryService.createCategory(root1);

        FileCategory child1 = new FileCategory();
        child1.setTitle("子分类1");
        child1.setParentId(createdRoot1.getId());
        fileCategoryService.createCategory(child1);

        FileCategory root2 = new FileCategory();
        root2.setTitle("根分类2");
        root2.setParentId(0);
        fileCategoryService.createCategory(root2);

        // 获取分类树
        List<FileCategory> tree = fileCategoryService.getCategoryTree();

        assertNotNull(tree);
        assertTrue(tree.size() >= 2);

        // 验证树结构
        FileCategory foundRoot1 = tree.stream()
                .filter(c -> "根分类1".equals(c.getTitle()))
                .findFirst()
                .orElse(null);

        assertNotNull(foundRoot1);
        assertNotNull(foundRoot1.getChildren());
        assertEquals(1, foundRoot1.getChildren().size());
        assertEquals("子分类1", foundRoot1.getChildren().get(0).getTitle());
    }

    @Test
    public void testUpdateCategory() {
        // 创建分类
        FileCategory category = new FileCategory();
        category.setTitle("原始标题");
        category.setParentId(0);
        FileCategory created = fileCategoryService.createCategory(category);

        // 更新分类
        created.setTitle("更新后的标题");
        created.setDescription("更新后的描述");
        boolean updated = fileCategoryService.updateCategory(created);

        assertTrue(updated);

        // 验证更新结果
        FileCategory found = fileCategoryService.getById(created.getId());
        assertEquals("更新后的标题", found.getTitle());
        assertEquals("更新后的描述", found.getDescription());
    }

    @Test
    public void testDeleteCategory() {
        // 创建分类
        FileCategory category = new FileCategory();
        category.setTitle("待删除分类");
        category.setParentId(0);
        FileCategory created = fileCategoryService.createCategory(category);

        // 删除分类
        boolean deleted = fileCategoryService.deleteCategory(created.getId());
        assertTrue(deleted);

        // 验证删除结果
        FileCategory found = fileCategoryService.getById(created.getId());
        assertNull(found);
    }

    @Test
    public void testDeleteCategoryWithChildren() {
        // 创建父分类
        FileCategory parent = new FileCategory();
        parent.setTitle("父分类");
        parent.setParentId(0);
        FileCategory createdParent = fileCategoryService.createCategory(parent);

        // 创建子分类
        FileCategory child = new FileCategory();
        child.setTitle("子分类");
        child.setParentId(createdParent.getId());
        fileCategoryService.createCategory(child);

        // 尝试删除有子分类的父分类，应该抛出异常
        assertThrows(RuntimeException.class, () -> {
            fileCategoryService.deleteCategory(createdParent.getId());
        });
    }

    @Test
    public void testMoveCategory() {
        // 创建测试数据
        FileCategory root1 = new FileCategory();
        root1.setTitle("根分类1");
        root1.setParentId(0);
        FileCategory createdRoot1 = fileCategoryService.createCategory(root1);

        FileCategory root2 = new FileCategory();
        root2.setTitle("根分类2");
        root2.setParentId(0);
        FileCategory createdRoot2 = fileCategoryService.createCategory(root2);

        FileCategory child = new FileCategory();
        child.setTitle("子分类");
        child.setParentId(createdRoot1.getId());
        FileCategory createdChild = fileCategoryService.createCategory(child);

        // 移动分类
        boolean moved = fileCategoryService.moveCategory(createdChild.getId(), createdRoot2.getId());
        assertTrue(moved);

        // 验证移动结果
        FileCategory movedCategory = fileCategoryService.getById(createdChild.getId());
        assertEquals(createdRoot2.getId(), movedCategory.getParentId());
        assertEquals("0," + createdRoot2.getId(), movedCategory.getPath());
    }

    @Test
    public void testSearchCategories() {
        // 创建测试数据
        FileCategory category1 = new FileCategory();
        category1.setTitle("Java编程");
        category1.setParentId(0);
        fileCategoryService.createCategory(category1);

        FileCategory category2 = new FileCategory();
        category2.setTitle("Python编程");
        category2.setParentId(0);
        fileCategoryService.createCategory(category2);

        FileCategory category3 = new FileCategory();
        category3.setTitle("数据库设计");
        category3.setParentId(0);
        fileCategoryService.createCategory(category3);

        // 搜索分类
        List<FileCategory> results = fileCategoryService.searchCategories("编程");

        assertNotNull(results);
        assertEquals(2, results.size());
        assertTrue(results.stream().anyMatch(c -> "Java编程".equals(c.getTitle())));
        assertTrue(results.stream().anyMatch(c -> "Python编程".equals(c.getTitle())));
    }

    @Test
    public void testGetCategoryPath() {
        // 创建多级分类
        FileCategory root = new FileCategory();
        root.setTitle("根分类");
        root.setParentId(0);
        FileCategory createdRoot = fileCategoryService.createCategory(root);

        FileCategory level1 = new FileCategory();
        level1.setTitle("一级分类");
        level1.setParentId(createdRoot.getId());
        FileCategory createdLevel1 = fileCategoryService.createCategory(level1);

        FileCategory level2 = new FileCategory();
        level2.setTitle("二级分类");
        level2.setParentId(createdLevel1.getId());
        FileCategory createdLevel2 = fileCategoryService.createCategory(level2);

        // 获取分类路径
        List<FileCategory> path = fileCategoryService.getCategoryPath(createdLevel2.getId());

        assertNotNull(path);
        assertEquals(3, path.size());
        assertEquals("根分类", path.get(0).getTitle());
        assertEquals("一级分类", path.get(1).getTitle());
        assertEquals("二级分类", path.get(2).getTitle());
    }

    @Test
    public void testExistsByTitleAndParentId() {
        // 创建分类
        FileCategory category = new FileCategory();
        category.setTitle("测试分类");
        category.setParentId(0);
        FileCategory created = fileCategoryService.createCategory(category);

        // 测试存在性检查
        assertTrue(fileCategoryService.existsByTitleAndParentId("测试分类", 0, null));
        assertFalse(fileCategoryService.existsByTitleAndParentId("不存在的分类", 0, null));

        // 测试排除自身的检查
        assertFalse(fileCategoryService.existsByTitleAndParentId("测试分类", 0, created.getId()));
    }
}
