package com.logic.code.controller.admin;

import com.logic.code.common.response.Result;
import com.logic.code.entity.goods.Goods;
import com.logic.code.model.vo.admin.GoodsVO;
import com.logic.code.service.GoodsService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * Test class for ProductController
 */
public class ProductControllerTest {

    @InjectMocks
    private ProductController productController;

    @Mock
    private GoodsService goodsService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testSaveProduct() {
        // Prepare test data
        GoodsVO goodsVO = new GoodsVO();
        goodsVO.setStoreName("Test Product");
        goodsVO.setUnitName("piece");
        
        List<Integer> categoryIds = new ArrayList<>();
        categoryIds.add(1);
        goodsVO.setCategoryIds(categoryIds);
        
        List<Map<String, Object>> attrs = new ArrayList<>();
        Map<String, Object> attr = new HashMap<>();
        attr.put("price", "100");
        attr.put("stock", "10");
        attrs.add(attr);
        goodsVO.setAttrs(attrs);
        
        // Mock service response
        Goods goods = new Goods();
        goods.setId(1);
        goods.setName("Test Product");
        goods.setGoodsUnit("piece");
        goods.setRetailPrice(new BigDecimal("100"));
        goods.setGoodsNumber(10);
        
        when(goodsService.createGoods(any(GoodsVO.class))).thenReturn(goods);
        
        // Call controller method
        Result<Goods> result = productController.saveProduct(0, goodsVO);
        
        // Verify result
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertNotNull(result.getData());
        assertEquals(1, result.getData().getId());
        assertEquals("Test Product", result.getData().getName());
    }
    
    @Test
    public void testProductCache() {
        // Mock service response
        Map<String, Object> cache = new HashMap<>();
        cache.put("info", new HashMap<>());
        
        when(goodsService.getProductCache()).thenReturn(cache);
        
        // Call controller method
        Result<Map<String, Object>> result = productController.productCache();
        
        // Verify result
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertNotNull(result.getData());
        assertNotNull(result.getData().get("info"));
    }
    
    @Test
    public void testCacheDelete() {
        // Call controller method
        Result<Boolean> result = productController.cacheDelete();
        
        // Verify result
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertEquals(true, result.getData());
    }
} 