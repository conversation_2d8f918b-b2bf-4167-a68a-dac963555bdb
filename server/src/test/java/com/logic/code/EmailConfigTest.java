package com.logic.code;

import com.logic.code.common.Cache;
import com.logic.code.util.EmailConfigDiagnostic;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 邮件配置测试类
 * 
 * <AUTHOR>
 * @date 2025/7/15
 */
@SpringBootTest
public class EmailConfigTest {
    
    @Autowired
    private Cache cache;
    
    @Autowired
    private EmailConfigDiagnostic emailConfigDiagnostic;
    
    /**
     * 诊断邮件配置
     */
    @Test
    public void testDiagnoseEmailConfig() {
        cache.init();
        
        System.out.println("开始诊断邮件配置...");
        String diagnostic = emailConfigDiagnostic.diagnoseEmailConfig();
        System.out.println(diagnostic);
    }
    
    /**
     * 测试发送邮件
     */
    @Test
    public void testSendEmail() {
        cache.init();
        
        String testEmail = "<EMAIL>"; // 替换为实际的测试邮箱
        System.out.println("开始发送测试邮件到：" + testEmail);
        
        boolean success = emailConfigDiagnostic.sendTestEmail(testEmail);
        System.out.println("测试邮件发送结果：" + (success ? "成功" : "失败"));
    }
    
    /**
     * 获取QQ邮箱配置信息
     */
    @Test
    public void testGetQQEmailConfig() {
        cache.init();
        
        String config = emailConfigDiagnostic.getEmailServerConfig("qq");
        System.out.println(config);
    }
}
