package com.logic.code;

import com.logic.code.common.Cache;
import com.logic.code.service.EmailService;
import com.logic.code.common.utils.ExcelUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.Sheet;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.io.IOException;
import java.util.Date;

/**
 * 邮件服务测试类
 *
 * <AUTHOR>
 * @date 2025/7/13
 */
@SpringBootTest
public class EmailServiceTest {

    @Autowired
    private Cache cache;

    @Autowired
    private EmailService emailService;

    /**
     * 测试发送简单文本邮件
     */
    @Test
    public void testSendSimpleEmail() {
        cache.init();

        String to = "<EMAIL>"; // 替换为实际的测试邮箱
        String subject = "测试邮件 - 简单文本";
        String content = "这是一封测试邮件，用于验证简单文本邮件发送功能。\n\n发送时间：" + new Date();

        boolean success = emailService.sendSimpleEmail(to, subject, content);
        System.out.println("简单邮件发送结果：" + (success ? "成功" : "失败"));
    }

    /**
     * 测试发送HTML邮件
     */
    @Test
    public void testSendHtmlEmail() {
        cache.init();

        String to = "<EMAIL>"; // 替换为实际的测试邮箱
        String subject = "测试邮件 - HTML格式";
        String content = "<p>这是一封<strong>HTML格式</strong>的测试邮件。</p>" +
                        "<ul>" +
                        "<li>功能：HTML邮件发送</li>" +
                        "<li>时间：" + new Date() + "</li>" +
                        "<li>状态：<span style='color: green;'>测试中</span></li>" +
                        "</ul>";

        String htmlContent = emailService.buildHtmlContent(subject, content);
        boolean success = emailService.sendHtmlEmail(to, subject, htmlContent);
        System.out.println("HTML邮件发送结果：" + (success ? "成功" : "失败"));
    }

    /**
     * 测试发送带附件的邮件
     */
    @Test
    public void testSendEmailWithAttachment() {
        cache.init();

        try {
            // 创建一个测试Excel文件
            String fileName = "测试附件_" + System.currentTimeMillis() + ".xlsx";
            String filePath = System.getProperty("user.dir") + "/" + fileName;

            createTestExcelFile(filePath);

            String to = "<EMAIL>"; // 替换为实际的测试邮箱
            String subject = "测试邮件 - 带附件";
            String content = "<p>这是一封带附件的测试邮件。</p>" +
                            "<p>附件包含测试数据，请查看。</p>" +
                            "<p>发送时间：" + new Date() + "</p>";

            String htmlContent = emailService.buildHtmlContent(subject, content);
            boolean success = emailService.sendEmailWithAttachment(to, subject, htmlContent, filePath);

            System.out.println("带附件邮件发送结果：" + (success ? "成功" : "失败"));
            System.out.println("测试文件路径：" + filePath);

            // 清理测试文件
            new File(filePath).delete();

        } catch (Exception e) {
            System.err.println("测试带附件邮件发送失败：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试发送生成的文件
     */
    @Test
    public void testSendGeneratedFile() {
        cache.init();

        try {
            // 创建一个测试报表文件
            String fileName = "订单报表_" + System.currentTimeMillis() + ".xlsx";
            String filePath = System.getProperty("user.dir") + "/" + fileName;

            createTestOrderReportFile(filePath);

            String[] recipients = {"<EMAIL>"}; // 替换为实际的测试邮箱
            String fileType = "订单报表";
            String description = "这是一个测试生成的订单报表，包含模拟数据。";

            boolean success = emailService.sendGeneratedFile(recipients, fileType, filePath, description);

            System.out.println("生成文件邮件发送结果：" + (success ? "成功" : "失败"));
            System.out.println("报表文件路径：" + filePath);

            // 清理测试文件
            new File(filePath).delete();

        } catch (Exception e) {
            System.err.println("测试发送生成文件失败：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试发送订单报表
     */
    @Test
    public void testSendOrderReport() {
        cache.init();

        try {
            // 创建订单报表文件
            String fileName = "订单报表_" + System.currentTimeMillis() + ".xlsx";
            String filePath = System.getProperty("user.dir") + "/" + fileName;

            createTestOrderReportFile(filePath);

            String[] recipients = {"<EMAIL>"}; // 替换为实际的测试邮箱
            String dateRange = "2025-07-01 至 2025-07-13";
            int recordCount = 150;

            boolean success = emailService.sendOrderReport(recipients, filePath, dateRange, recordCount);

            System.out.println("订单报表邮件发送结果：" + (success ? "成功" : "失败"));
            System.out.println("报表文件路径：" + filePath);

            // 清理测试文件
            new File(filePath).delete();

        } catch (Exception e) {
            System.err.println("测试发送订单报表失败：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试发送系统通知
     */
    @Test
    public void testSendSystemNotification() {
        cache.init();

        String subject = "系统通知 - 测试";
        String content = "<h3>系统状态通知</h3>" +
                        "<p>这是一条系统通知测试消息。</p>" +
                        "<p><strong>通知类型：</strong>测试通知</p>" +
                        "<p><strong>发送时间：</strong>" + new Date() + "</p>" +
                        "<p><strong>系统状态：</strong><span style='color: green;'>正常运行</span></p>";

        boolean success = emailService.sendSystemNotification(subject, content);
        System.out.println("系统通知发送结果：" + (success ? "成功" : "失败"));
    }

    /**
     * 创建测试Excel文件
     */
    private void createTestExcelFile(String filePath) throws IOException {
        Workbook workbook = ExcelUtils.createWorkbook();
        Sheet sheet = workbook.createSheet("测试数据");

        // 创建表头
     /*   String[] headers = {"ID", "名称", "数量", "价格", "创建时间"};
        ExcelUtils.createHeaderRow(sheet, headers);

        // 添加测试数据
        for (int i = 1; i <= 10; i++) {
            Object[] rowData = {
                i,
                "测试商品" + i,
                (int)(Math.random() * 100) + 1,
                Math.round(Math.random() * 1000 * 100) / 100.0,
                new Date()
            };
            ExcelUtils.createDataRow(sheet, i, rowData);
        }
        */
       /* // 自动调整列宽
        ExcelUtils.autoSizeColumns(sheet, headers.length);*/

        // 保存文件
        ExcelUtils.saveWorkbook(workbook, filePath);
        workbook.close();
    }

    /**
     * 创建测试订单报表文件
     */
    private void createTestOrderReportFile(String filePath) throws IOException {
        Workbook workbook = ExcelUtils.createWorkbook();
        Sheet sheet = workbook.createSheet("订单报表");

        // 创建表头
        String[] headers = {"订单ID", "订单号", "用户ID", "商品名称", "数量", "金额", "状态", "创建时间"};
    /*    ExcelUtils.createHeaderRow(sheet, headers);

        // 添加测试订单数据
        String[] statuses = {"已完成", "待付款", "已发货", "已取消"};
        for (int i = 1; i <= 50; i++) {
            Object[] rowData = {
                i,
                "ORD" + System.currentTimeMillis() + i,
                (int)(Math.random() * 1000) + 1,
                "测试商品" + ((int)(Math.random() * 20) + 1),
                (int)(Math.random() * 5) + 1,
                Math.round(Math.random() * 500 * 100) / 100.0,
                statuses[(int)(Math.random() * statuses.length)],
                new Date()
            };
            ExcelUtils.createDataRow(sheet, i, rowData);
        }

        // 自动调整列宽
        ExcelUtils.autoSizeColumns(sheet, headers.length);

        // 保存文件*/
        ExcelUtils.saveWorkbook(workbook, filePath);
        workbook.close();
    }
}
