package com.logic.code;

import com.logic.code.common.Cache;
import com.logic.code.schedule.TaskSchedule;
import com.logic.code.service.WxOrderShippingService;
import com.logic.code.service.WxShippingInfoService;
import com.logic.code.service.OrderService;
import com.logic.code.service.OrderExpressService;
import com.logic.code.service.UserService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 微信发货信息同步定时任务测试类
 * <AUTHOR>
 * @date 2025/8/10
 */
@SpringBootTest
public class WxShippingSyncTaskTest {

    @Resource
    private TaskSchedule taskSchedule;

    @Resource
    private WxOrderShippingService wxOrderShippingService;

    @Resource
    private WxShippingInfoService wxShippingInfoService;

    @Resource
    private OrderService orderService;

    @Resource
    private OrderExpressService orderExpressService;

    @Resource
    private UserService userService;

    @Resource
    private Cache cache;

    /**
     * 测试微信发货信息同步定时任务
     */
    @Test
    public void testSyncShippingOrdersToWechat() {
        try {
            cache.init();
            System.out.println("开始测试微信发货信息同步定时任务...");
            
            // 执行定时任务
            taskSchedule.syncShippingOrdersToWechat();
            
            System.out.println("微信发货信息同步定时任务测试完成");
        } catch (Exception e) {
            System.err.println("微信发货信息同步定时任务测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试微信发货服务状态检查
     */
    @Test
    public void testWxShippingServiceStatus() {
        try {
            cache.init();
            System.out.println("开始测试微信发货服务状态检查...");
            
            // 检查微信小程序是否已开通发货信息管理服务
            Boolean isTradeManaged = wxOrderShippingService.isTradeManaged();
            System.out.println("微信发货信息管理服务开通状态: " + isTradeManaged);
            
            if (isTradeManaged == null) {
                System.out.println("⚠️ 无法获取服务状态，请检查微信配置");
            } else if (isTradeManaged) {
                System.out.println("✅ 微信发货信息管理服务已开通");
            } else {
                System.out.println("❌ 微信发货信息管理服务未开通，请先在微信小程序后台开通此服务");
            }
            
            System.out.println("微信发货服务状态检查完成");
        } catch (Exception e) {
            System.err.println("微信发货服务状态检查失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试查询待同步的订单
     */
    @Test
    public void testQueryPendingSyncOrders() {
        try {
            cache.init();
            System.out.println("开始测试查询待同步的订单...");
            
            // 这里可以添加查询逻辑，检查有多少订单需要同步
            // 由于涉及具体的业务数据，这里只做框架性测试
            
            System.out.println("查询待同步订单测试完成");
        } catch (Exception e) {
            System.err.println("查询待同步订单测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试手机号掩码功能
     */
    @Test
    public void testPhoneNumberMasking() {
        try {
            System.out.println("开始测试手机号掩码功能...");
            
            // 测试不同长度的手机号掩码
            String[] testPhones = {
                "13812345678",
                "1381234567",
                "138123",
                "138",
                "12",
                "",
                null
            };
            
            for (String phone : testPhones) {
                String masked = maskPhoneNumber(phone);
                System.out.println("原始手机号: " + phone + " -> 掩码后: " + masked);
            }
            
            System.out.println("手机号掩码功能测试完成");
        } catch (Exception e) {
            System.err.println("手机号掩码功能测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 对手机号进行掩码处理（测试用）
     * @param phoneNumber 原始手机号
     * @return 掩码后的手机号
     */
    private String maskPhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.trim().isEmpty() || phoneNumber.length() < 4) {
            return phoneNumber;
        }
        
        // 保留后4位，其他位用*替换
        int length = phoneNumber.length();
        StringBuilder masked = new StringBuilder();
        for (int i = 0; i < length - 4; i++) {
            masked.append("*");
        }
        masked.append(phoneNumber.substring(length - 4));
        
        return masked.toString();
    }

    /**
     * 测试定时任务的时间间隔设置
     */
    @Test
    public void testScheduleInterval() {
        try {
            System.out.println("开始测试定时任务时间间隔设置...");
            
            // 30分钟的毫秒数
            long thirtyMinutes = 30 * 60 * 1000;
            System.out.println("定时任务间隔设置: " + thirtyMinutes + " 毫秒 (30分钟)");
            
            // 转换为分钟显示
            long minutes = thirtyMinutes / (60 * 1000);
            System.out.println("定时任务间隔: " + minutes + " 分钟");
            
            System.out.println("定时任务时间间隔设置测试完成");
        } catch (Exception e) {
            System.err.println("定时任务时间间隔设置测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
