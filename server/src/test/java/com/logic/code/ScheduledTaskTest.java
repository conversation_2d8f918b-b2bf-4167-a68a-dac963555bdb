package com.logic.code;

import com.logic.code.service.OrderExportService;
import com.logic.code.schedule.TaskSchedule;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 定时任务测试类
 * <AUTHOR>
 * @date 2025/7/27
 */
@SpringBootTest
public class ScheduledTaskTest {

    @Resource
    private OrderExportService orderExportService;

    @Resource
    private TaskSchedule taskSchedule;

    /**
     * 测试订单导出服务
     */
    @Test
    public void testOrderExportService() {
        try {
            System.out.println("开始测试订单导出服务...");
            orderExportService.exportAllOrdersToExcel();
            System.out.println("订单导出服务测试完成");
        } catch (Exception e) {
            System.err.println("订单导出服务测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
