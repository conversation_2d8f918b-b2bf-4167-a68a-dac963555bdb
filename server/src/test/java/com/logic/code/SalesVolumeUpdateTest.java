package com.logic.code;

import com.logic.code.schedule.TaskSchedule;
import com.logic.code.service.SalesVolumeUpdateService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 销量更新功能测试类
 * <AUTHOR>
 * @date 2025/7/27
 */
@SpringBootTest
public class SalesVolumeUpdateTest {

    @Resource
    private SalesVolumeUpdateService salesVolumeUpdateService;

    @Resource
    private TaskSchedule taskSchedule;

    /**
     * 测试销量更新服务
     */
    @Test
    public void testSalesVolumeUpdateService() {
        try {
            System.out.println("开始测试销量更新服务...");
            salesVolumeUpdateService.randomUpdateSalesVolume();
            System.out.println("销量更新服务测试完成");
        } catch (Exception e) {
            System.err.println("销量更新服务测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试定时任务调度
     */
    @Test
    public void testScheduledSalesVolumeUpdate() {
        try {
            System.out.println("开始测试定时销量更新任务...");
            taskSchedule.scheduledSalesVolumeUpdate();
            System.out.println("定时销量更新任务测试完成");
        } catch (Exception e) {
            System.err.println("定时销量更新任务测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试手动触发销量更新
     */
    @Test
    public void testManualSalesVolumeUpdate() {
        try {
            System.out.println("开始测试手动销量更新...");
            salesVolumeUpdateService.manualUpdateSalesVolume();
            System.out.println("手动销量更新测试完成");
        } catch (Exception e) {
            System.err.println("手动销量更新测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
