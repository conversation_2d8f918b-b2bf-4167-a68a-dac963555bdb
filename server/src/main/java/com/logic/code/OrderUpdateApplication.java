package com.logic.code;

import com.logic.code.service.OrderUpdateService;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;

import java.util.Scanner;

//@SpringBootApplication
public class OrderUpdateApplication {

    public static void main(String[] args) {
        ConfigurableApplicationContext context = SpringApplication.run(OrderUpdateApplication.class, args);
        
        // 如果有命令行参数，直接处理文件
        if (args.length > 0) {
            String filePath = args[0];
            OrderUpdateService orderUpdateService = context.getBean(OrderUpdateService.class);
            orderUpdateService.updateOrdersFromExcel(filePath);
            System.out.println("订单信息更新完成: " + filePath);
            // 处理完后退出程序
            SpringApplication.exit(context, () -> 0);
            return;
        }
        
        // 如果没有命令行参数，进入交互模式
        Scanner scanner = new Scanner(System.in);
        OrderUpdateService orderUpdateService = context.getBean(OrderUpdateService.class);
        
        System.out.println("订单信息更新工具");
        System.out.println("请输入Excel文件路径（输入 'exit' 退出）:");
        
        while (true) {
            System.out.print("> ");
            String input = scanner.nextLine().trim();
            
            if ("exit".equalsIgnoreCase(input)) {
                break;
            }
            
            if (input.isEmpty()) {
                System.out.println("请输入有效的文件路径");
                continue;
            }
            
            try {
                orderUpdateService.updateOrdersFromExcel(input);
                System.out.println("订单信息更新完成: " + input);
            } catch (Exception e) {
                System.err.println("处理文件时出错: " + e.getMessage());
            }
            
            System.out.println("\n请输入Excel文件路径（输入 'exit' 退出）:");
        }
        
        System.out.println("程序退出");
        SpringApplication.exit(context, () -> 0);
    }
}