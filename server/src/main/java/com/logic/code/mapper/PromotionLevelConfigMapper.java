package com.logic.code.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.logic.code.entity.PromotionLevelConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 推广等级配置Mapper
 */
@Mapper
public interface PromotionLevelConfigMapper extends BaseMapper<PromotionLevelConfig> {
    
    /**
     * 获取所有启用的推广等级配置，按等级排序
     */
    @Select("SELECT * FROM weshop_promotion_level_config WHERE is_enabled = 1 ORDER BY level")
    List<PromotionLevelConfig> getEnabledConfigs();
    
    /**
     * 根据推广人数获取对应的等级配置
     */
    @Select("SELECT * FROM weshop_promotion_level_config " +
            "WHERE is_enabled = 1 " +
            "AND min_promotion_count <= #{promotionCount} " +
            "AND (max_promotion_count IS NULL OR max_promotion_count >= #{promotionCount}) " +
            "ORDER BY level DESC LIMIT 1")
    PromotionLevelConfig getConfigByPromotionCount(Integer promotionCount);
}