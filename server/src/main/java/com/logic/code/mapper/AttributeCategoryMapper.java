package com.logic.code.mapper;

import com.logic.code.entity.goods.AttributeCategory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/5
 * @desc 属性分类映射器
 */
@Mapper
public interface AttributeCategoryMapper extends CommonMapper<AttributeCategory> {

    /**
     * 查询所有启用的属性分类
     */
    @Select("SELECT * FROM weshop_attribute_category WHERE enabled = true ORDER BY id ASC")
    List<AttributeCategory> selectAllEnabled();

    /**
     * 根据名称模糊查询属性分类
     */
    @Select("SELECT * FROM weshop_attribute_category WHERE name LIKE CONCAT('%', #{name}, '%') ORDER BY id ASC")
    List<AttributeCategory> selectByNameLike(String name);
}
