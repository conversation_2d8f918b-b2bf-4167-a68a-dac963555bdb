package com.logic.code.mapper;

import com.logic.code.entity.Keywords;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/8 9:49
 * @desc
 */
@Mapper
public interface KeywordsMapper extends CommonMapper<Keywords>{

    @Select("select keyword from weshop_keywords where keyword like concat('%',#{keyword},'%')")
    List<String> selectByKeywordLike(String keyword);
}
