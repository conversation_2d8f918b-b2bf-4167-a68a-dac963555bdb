package com.logic.code.mapper;

import com.logic.code.entity.goods.GoodsSpecification;
import com.logic.code.model.dto.GoodsSpecificationDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/8 11:20
 * @desc
 */
@Mapper
public interface GoodsSpecificationMapper extends CommonMapper<GoodsSpecification> {

    List<GoodsSpecificationDTO> selectGoodsDetailSpecificationByGoodsId(Integer goodsId);

    List<String> selectValueByGoodsIdAndIdIn(Integer goodsId, @Param("list") List<Integer> goodsSpecificationIds);
}
