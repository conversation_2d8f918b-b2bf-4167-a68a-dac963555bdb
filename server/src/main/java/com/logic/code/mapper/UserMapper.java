package com.logic.code.mapper;

import com.logic.code.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * <AUTHOR>
 * @date 2025/5/7 21:10
 * @desc
 */

@Mapper
public interface UserMapper extends CommonMapper<User> {

    /**
     * 原子性地扣减用户积分
     * @param userId 用户ID
     * @param deductPoints 要扣减的积分数量
     * @return 影响的行数，如果为0表示积分不足或用户不存在
     */
    @Update("UPDATE weshop_user SET points = points - #{deductPoints} WHERE id = #{userId} AND points >= #{deductPoints}")
    int deductUserPoints(@Param("userId") Integer userId, @Param("deductPoints") Integer deductPoints);
}
