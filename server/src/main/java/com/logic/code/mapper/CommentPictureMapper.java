package com.logic.code.mapper;

import com.logic.code.entity.CommentPicture;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/8 10:20
 * @desc
 */
@Mapper
public interface CommentPictureMapper extends CommonMapper<CommentPicture> {

    @Select("SELECT pic_url FROM weshop_comment_picture WHERE comment_id = #{commentId}")
    List<String> selectPicUrlByCommentId(Integer commentId);
}
