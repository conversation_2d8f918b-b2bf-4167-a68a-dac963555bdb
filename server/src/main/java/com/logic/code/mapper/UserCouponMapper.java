package com.logic.code.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.logic.code.entity.UserCoupon;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 用户优惠券Mapper接口
 * <AUTHOR>
 * @date 2025/7/25
 */
@Mapper
public interface UserCouponMapper extends BaseMapper<UserCoupon> {
    
    /**
     * 获取用户优惠券统计
     * @param userId 用户ID
     * @return 统计信息
     */
    Map<String, Object> getCouponStats(@Param("userId") Integer userId);
}