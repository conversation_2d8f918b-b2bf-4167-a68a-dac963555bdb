package com.logic.code.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logic.code.entity.goods.SpecificationTemplate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 规格模板Mapper接口
 */
@Mapper
public interface SpecificationTemplateMapper extends CommonMapper<SpecificationTemplate> {

    /**
     * 分页查询规格模板
     */
    Page<SpecificationTemplate> selectPage(Page<SpecificationTemplate> page, @Param("ruleName") String ruleName);

    /**
     * 查询所有启用的规格模板
     */
    @Select("SELECT * FROM weshop_specification_template WHERE status = 1 ORDER BY create_time DESC")
    List<SpecificationTemplate> selectAllEnabled();

    /**
     * 根据规格模板名称查询
     */
    @Select("SELECT * FROM weshop_specification_template WHERE rule_name = #{ruleName} AND status = 1")
    SpecificationTemplate selectByRuleName(@Param("ruleName") String ruleName);
}
