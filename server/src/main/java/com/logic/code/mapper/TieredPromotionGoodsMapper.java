package com.logic.code.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.logic.code.entity.TieredPromotionGoods;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 阶梯推广商品配置Mapper
 */
@Mapper
public interface TieredPromotionGoodsMapper extends BaseMapper<TieredPromotionGoods>, CommonMapper<TieredPromotionGoods> {
    
    /**
     * 根据商品ID查询阶梯推广配置
     * @param goodsId 商品ID
     * @return 阶梯推广配置
     */
    @Select("SELECT * FROM weshop_tiered_promotion_goods WHERE goods_id = #{goodsId} AND is_active = 1")
    TieredPromotionGoods selectByGoodsId(@Param("goodsId") Integer goodsId);
    
    /**
     * 检查商品是否为阶梯推广商品
     * @param goodsId 商品ID
     * @return 是否为阶梯推广商品
     */
    @Select("SELECT COUNT(*) > 0 FROM weshop_tiered_promotion_goods WHERE goods_id = #{goodsId} AND is_active = 1")
    boolean isTieredPromotionGoods(@Param("goodsId") Integer goodsId);
    
    /**
     * 批量查询商品的活动信息
     * @param goodsIds 商品ID列表
     * @return 活动信息列表
     */
    @Select("<script>" +
            "SELECT goods_id, name, 'tiered_promotion' as activity_type " +
            "FROM weshop_tiered_promotion_goods " +
            "WHERE is_active = 1 AND goods_id IN " +
            "<foreach collection='goodsIds' item='goodsId' open='(' separator=',' close=')'>" +
            "#{goodsId}" +
            "</foreach>" +
            "</script>")
    java.util.List<java.util.Map<String, Object>> selectActivityInfoByGoodsIds(@Param("goodsIds") java.util.List<Integer> goodsIds);
    
    /**
     * 查询所有活跃的阶梯推广商品ID
     * @return 阶梯推广商品ID列表
     */
    @Select("SELECT goods_id FROM weshop_tiered_promotion_goods WHERE is_active = 1")
    java.util.List<Integer> selectActiveTieredGoodsIds();
}