package com.logic.code.mapper;

import com.logic.code.entity.Admin;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * 管理员Mapper接口
 * 
 * <AUTHOR>
 * @date 2025/1/27
 */
@Mapper
public interface AdminMapper extends CommonMapper<Admin> {
    
    /**
     * 根据用户名查询管理员
     */
    @Select("SELECT * FROM weshop_admin WHERE username = #{username} LIMIT 1")
    Admin selectByUsername(String username);
    
    /**
     * 根据用户名和密码查询管理员
     */
    @Select("SELECT * FROM weshop_admin WHERE username = #{username} AND password = #{password} LIMIT 1")
    Admin selectByUsernameAndPassword(String username, String password);
}