package com.logic.code.mapper;

import com.logic.code.entity.system.MenuPermission;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 菜单权限Mapper接口
 * 
 * <AUTHOR>
 * @date 2025/7/7
 */
@Mapper
public interface MenuPermissionMapper extends CommonMapper<MenuPermission> {
    
    /**
     * 根据菜单ID查询权限列表
     */
    @Select("SELECT permission FROM weshop_menu_permission WHERE menu_id = #{menuId}")
    List<String> selectPermissionsByMenuId(Integer menuId);
    
    /**
     * 查询所有菜单的权限信息
     */
    @Select("SELECT * FROM weshop_menu_permission ORDER BY menu_id ASC")
    List<MenuPermission> selectAllPermissions();
}
