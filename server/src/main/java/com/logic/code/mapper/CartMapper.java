package com.logic.code.mapper;

import com.logic.code.entity.order.Cart;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Update;

/**
 * <AUTHOR>
 * @date 2025/5/7 22:26
 * @desc
 */
@Mapper
public interface CartMapper extends CommonMapper<Cart>{

    @Update("update weshop_cart set `number`=`number`+#{number} where id=#{id}")
    int updateNumberById(Integer number, Integer id);
}
