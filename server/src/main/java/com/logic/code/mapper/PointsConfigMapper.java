package com.logic.code.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.logic.code.entity.PointsConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * 积分配置Mapper
 */
@Mapper
public interface PointsConfigMapper extends BaseMapper<PointsConfig>, CommonMapper<PointsConfig> {
    
    /**
     * 获取当前积分配置
     */
    @Select("SELECT * FROM weshop_points_config WHERE is_enabled = 1 ORDER BY id DESC LIMIT 1")
    PointsConfig getCurrentConfig();
}