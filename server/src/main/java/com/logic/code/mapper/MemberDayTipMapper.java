package com.logic.code.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.logic.code.entity.MemberDayTip;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 会员日提示信息Mapper
 * <AUTHOR>
 */
@Mapper
public interface MemberDayTipMapper extends BaseMapper<MemberDayTip> {
    
    /**
     * 根据显示位置获取启用的提示信息
     * @param displayPosition 显示位置
     * @return 提示信息列表
     */
    @Select("SELECT * FROM member_day_tip " +
            "WHERE is_enabled = 1 " +
            "AND (display_position = #{displayPosition} OR display_position = 'all') " +
            "AND (start_time IS NULL OR start_time <= NOW()) " +
            "AND (end_time IS NULL OR end_time >= NOW()) " +
            "ORDER BY priority DESC, create_time DESC " +
            "LIMIT 1")
    MemberDayTip getActiveByPosition(String displayPosition);
    
    /**
     * 获取所有启用的提示信息
     * @return 提示信息列表
     */
    @Select("SELECT * FROM member_day_tip " +
            "WHERE is_enabled = 1 " +
            "AND (start_time IS NULL OR start_time <= NOW()) " +
            "AND (end_time IS NULL OR end_time >= NOW()) " +
            "ORDER BY priority DESC, create_time DESC")
    List<MemberDayTip> getAllActive();
}