package com.logic.code.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.logic.code.entity.PromotionPointsRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 推广积分记录Mapper
 */
@Mapper
public interface PromotionPointsRecordMapper extends BaseMapper<PromotionPointsRecord> {
    
    /**
     * 获取推广者的积分记录
     */
    @Select("SELECT * FROM weshop_promotion_points_record " +
            "WHERE promoter_id = #{promoterId} " +
            "ORDER BY promotion_time DESC " +
            "LIMIT #{offset}, #{size}")
    List<PromotionPointsRecord> getPromoterRecords(Integer promoterId, Integer offset, Integer size);
    
    /**
     * 获取推广者的积分记录总数
     */
    @Select("SELECT COUNT(*) FROM weshop_promotion_points_record WHERE promoter_id = #{promoterId}")
    Integer getPromoterRecordsCount(Integer promoterId);
    
    /**
     * 获取推广者的总积分
     */
    @Select("SELECT COALESCE(SUM(reward_points), 0) FROM weshop_promotion_points_record WHERE promoter_id = #{promoterId}")
    Integer getPromoterTotalPoints(Integer promoterId);
}