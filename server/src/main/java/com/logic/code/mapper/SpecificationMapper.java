package com.logic.code.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.logic.code.entity.goods.Specification;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 规格Mapper接口
 */
@Mapper
public interface SpecificationMapper extends BaseMapper<Specification> {

    /**
     * 根据规格名称查询规格
     * @param name 规格名称
     * @return 规格信息
     */
    Specification selectByName(@Param("name") String name);

    /**
     * 查询所有启用的规格列表
     * @return 规格列表
     */
    List<Specification> selectAllEnabled();

    /**
     * 根据ID列表查询规格列表
     * @param ids 规格ID列表
     * @return 规格列表
     */
    List<Specification> selectByIds(@Param("list") List<Integer> ids);
}
