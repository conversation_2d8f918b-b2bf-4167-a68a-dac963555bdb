package com.logic.code.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.logic.code.entity.CompanyConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * 公司信息配置Mapper
 * 
 * <AUTHOR>
 * @date 2025/09/03
 */
@Mapper
public interface CompanyConfigMapper extends BaseMapper<CompanyConfig> {
    
    /**
     * 查询启用的公司信息配置
     */
    @Select("SELECT * FROM weshop_company_config WHERE is_active = 1 LIMIT 1")
    CompanyConfig selectActiveConfig();
}
