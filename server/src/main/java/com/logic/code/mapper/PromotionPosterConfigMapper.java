package com.logic.code.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.logic.code.entity.PromotionPosterConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 推广海报配置Mapper
 * 
 * <AUTHOR>
 * @date 2025/09/03
 */
@Mapper
public interface PromotionPosterConfigMapper extends BaseMapper<PromotionPosterConfig> {
    
    /**
     * 查询所有启用的海报模板，按排序排列
     */
    @Select("SELECT * FROM weshop_promotion_poster_config WHERE is_active = 1 ORDER BY sort_order ASC, id ASC")
    List<PromotionPosterConfig> selectActiveTemplates();
    
    /**
     * 查询默认海报模板
     */
    @Select("SELECT * FROM weshop_promotion_poster_config WHERE is_default = 1 AND is_active = 1 LIMIT 1")
    PromotionPosterConfig selectDefaultTemplate();
}
