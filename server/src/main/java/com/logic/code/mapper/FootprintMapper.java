package com.logic.code.mapper;

import com.logic.code.entity.Footprint;
import com.logic.code.model.dto.GoodsFootprintDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/8 10:58
 * @desc
 */
@Mapper
public interface FootprintMapper extends CommonMapper<Footprint> {
    @Select("SELECT f.goods_id,MAX(f.create_time) AS createTime,g.`name`,g.list_pic_url,g.goods_brief, g.retail_price FROM weshop_footprint f LEFT JOIN weshop_goods g ON f.goods_id = g.id WHERE f.user_id = #{userId} GROUP BY goods_id ORDER BY createTime DESC")
    List<GoodsFootprintDTO> selectGoodsFootprintByUserId(Integer userId);
}
