package com.logic.code.mapper;

import com.logic.code.entity.goods.Category;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/7 22:45
 * @desc
 */
@Mapper
public interface CategoryMapper extends CommonMapper<Category>{

    @Select("select id from weshop_category where parent_id=#{parentId}")
    List<Integer> selectIdsByParentId(Integer parentId);

    List<Integer> selectParentIdsByIdIn(List<Integer> ids);
}
