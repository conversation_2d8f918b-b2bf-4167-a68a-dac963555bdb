package com.logic.code.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.logic.code.entity.ExpressCompany;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 快递公司Mapper
 * <AUTHOR>
 * @date 2025/1/27
 */
@Mapper
public interface ExpressCompanyMapper extends BaseMapper<ExpressCompany> {
    
    /**
     * 查询启用的快递公司列表，按排序排列
     * @return 快递公司列表
     */
    @Select("SELECT * FROM weshop_shipper WHERE enabled = 1 ORDER BY sort ASC, id ASC")
    List<ExpressCompany> selectEnabledShippers();
    
    /**
     * 查询所有快递公司列表，按排序排列
     * @return 快递公司列表
     */
    @Select("SELECT * FROM weshop_shipper ORDER BY sort ASC, id ASC")
    List<ExpressCompany> selectAllShippers();
}