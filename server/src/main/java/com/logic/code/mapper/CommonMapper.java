package com.logic.code.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/7 17:20
 * @desc
 */
public interface CommonMapper<T> extends BaseMapper<T> {
    @Select(" select * from ${table} ${ext} ")
    List<Map<String,Object>> selectMapBySql(@Param("table") String table, @Param("ext") String ext);

    @Select(" ${sql} ")
    List<T> selectBySql(@Param("sql") String sql);

    @Select(" ${sql} ")
    Integer countBySql(@Param("sql") String countSql);
}
