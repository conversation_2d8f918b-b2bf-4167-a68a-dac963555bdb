package com.logic.code.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.logic.code.entity.PointsRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 积分记录Mapper
 */
@Mapper
public interface PointsRecordMapper extends BaseMapper<PointsRecord>, CommonMapper<PointsRecord> {
    
    /**
     * 获取用户积分记录列表
     */
    @Select("SELECT * FROM weshop_points_record WHERE user_id = #{userId} ORDER BY create_time DESC LIMIT #{offset}, #{limit}")
    List<PointsRecord> getUserPointsRecords(@Param("userId") Integer userId, @Param("offset") Integer offset, @Param("limit") Integer limit);
    
    /**
     * 获取用户积分记录总数
     */
    @Select("SELECT COUNT(*) FROM weshop_points_record WHERE user_id = #{userId}")
    Integer getUserPointsRecordsCount(@Param("userId") Integer userId);
    
    /**
     * 获取用户总积分
     */
        @Select("SELECT IFNULL(SUM(points), 0) FROM weshop_points_record WHERE user_id = #{userId}")
    Integer getUserTotalPoints(@Param("userId") Integer userId);
}