package com.logic.code.mapper;

import com.logic.code.entity.goods.DisplaySystem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 展示系统Mapper
 * 
 * <AUTHOR>
 * @date 2025/7/6
 */
@Mapper
public interface DisplaySystemMapper extends CommonMapper<DisplaySystem> {
    
    /**
     * 查询所有启用的展示系统，按排序排列
     */
    @Select("SELECT * FROM weshop_display_system WHERE is_enabled = 1 ORDER BY sort_order ASC, id ASC")
    List<DisplaySystem> selectEnabledSystems();
}
