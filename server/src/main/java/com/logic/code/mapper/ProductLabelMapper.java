package com.logic.code.mapper;

import com.logic.code.entity.goods.ProductLabel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 商品标签Mapper
 */
@Mapper
public interface ProductLabelMapper extends CommonMapper<ProductLabel> {

    /**
     * 分页查询标签列表
     */
    @Select("<script>" +
            "SELECT l.*, c.name as category_name " +
            "FROM weshop_product_label l " +
            "LEFT JOIN weshop_product_label_category c ON l.cate_id = c.id " +
            "WHERE 1=1 " +
            "<if test='name != null and name != \"\"'>" +
            "AND l.name LIKE CONCAT('%', #{name}, '%') " +
            "</if>" +
            "<if test='cateId != null and cateId > 0'>" +
            "AND l.cate_id = #{cateId} " +
            "</if>" +
            "<if test='status != null'>" +
            "AND l.status = #{status} " +
            "</if>" +
            "<if test='isShow != null'>" +
            "AND l.is_show = #{isShow} " +
            "</if>" +
            "ORDER BY l.sort_order ASC, l.id DESC " +
            "LIMIT #{offset}, #{limit}" +
            "</script>")
    List<Map<String, Object>> selectPageList(@Param("name") String name,
                                           @Param("cateId") Integer cateId,
                                           @Param("status") Integer status,
                                           @Param("isShow") Integer isShow,
                                           @Param("offset") Integer offset,
                                           @Param("limit") Integer limit);

    /**
     * 查询总数
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM weshop_product_label l " +
            "WHERE 1=1 " +
            "<if test='name != null and name != \"\"'>" +
            "AND l.name LIKE CONCAT('%', #{name}, '%') " +
            "</if>" +
            "<if test='cateId != null and cateId > 0'>" +
            "AND l.cate_id = #{cateId} " +
            "</if>" +
            "<if test='status != null'>" +
            "AND l.status = #{status} " +
            "</if>" +
            "<if test='isShow != null'>" +
            "AND l.is_show = #{isShow} " +
            "</if>" +
            "</script>")
    Long selectPageCount(@Param("name") String name,
                        @Param("cateId") Integer cateId,
                        @Param("status") Integer status,
                        @Param("isShow") Integer isShow);

    /**
     * 查询所有可用的标签
     */
    @Select("SELECT * FROM weshop_product_label WHERE status = 1 AND is_show = 1 ORDER BY sort_order ASC, id ASC")
    List<ProductLabel> selectAvailableList();

    /**
     * 根据分类ID查询标签
     */
    @Select("SELECT * FROM weshop_product_label WHERE cate_id = #{cateId} AND status = 1 ORDER BY sort_order ASC, id ASC")
    List<ProductLabel> selectByCategoryId(@Param("cateId") Integer cateId);
}
