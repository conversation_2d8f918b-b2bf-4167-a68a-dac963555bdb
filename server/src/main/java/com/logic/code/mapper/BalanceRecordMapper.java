package com.logic.code.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.logic.code.entity.BalanceRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

/**
 * 用户余额使用记录Mapper
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface BalanceRecordMapper extends BaseMapper<BalanceRecord> {

    /**
     * 根据用户ID获取余额记录
     * 
     * @param userId 用户ID
     * @param limit 限制条数
     * @return 余额记录列表
     */
    @Select("SELECT * FROM weshop_balance_record WHERE user_id = #{userId} ORDER BY create_time DESC LIMIT #{limit}")
    List<BalanceRecord> getByUserId(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 根据来源获取余额记录
     * 
     * @param source 来源
     * @param sourceId 来源ID
     * @return 余额记录列表
     */
    @Select("SELECT * FROM weshop_balance_record WHERE source = #{source} AND source_id = #{sourceId} ORDER BY create_time DESC")
    List<BalanceRecord> getBySource(@Param("source") String source, @Param("sourceId") Long sourceId);

    /**
     * 计算用户余额总额
     * 
     * @param userId 用户ID
     * @return 余额总额
     */
    @Select("SELECT COALESCE(SUM(amount), 0) FROM weshop_balance_record WHERE user_id = #{userId}")
    BigDecimal calculateUserBalance(@Param("userId") Long userId);

    /**
     * 获取用户最后一条余额记录
     * 
     * @param userId 用户ID
     * @return 最后一条余额记录
     */
    @Select("SELECT * FROM weshop_balance_record WHERE user_id = #{userId} ORDER BY create_time DESC LIMIT 1")
    BalanceRecord getLastRecord(@Param("userId") Long userId);

    /**
     * 根据类型统计余额记录
     * 
     * @param userId 用户ID
     * @param type 类型
     * @return 记录数量
     */
    @Select("SELECT COUNT(*) FROM weshop_balance_record WHERE user_id = #{userId} AND type = #{type}")
    Integer countByType(@Param("userId") Long userId, @Param("type") String type);
}
