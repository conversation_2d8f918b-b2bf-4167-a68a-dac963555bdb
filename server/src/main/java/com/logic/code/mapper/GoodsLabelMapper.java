package com.logic.code.mapper;

import com.logic.code.entity.goods.GoodsLabel;
import com.logic.code.entity.goods.ProductLabel;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 商品标签关联Mapper
 */
@Mapper
public interface GoodsLabelMapper extends CommonMapper<GoodsLabel> {

    /**
     * 根据商品ID查询关联的标签
     */
    @Select("SELECT l.* FROM weshop_product_label l " +
            "INNER JOIN weshop_goods_label gl ON l.id = gl.label_id " +
            "WHERE gl.goods_id = #{goodsId} AND l.status = 1 AND l.is_show = 1 " +
            "ORDER BY l.sort_order ASC, l.id ASC")
    List<ProductLabel> selectLabelsByGoodsId(@Param("goodsId") Integer goodsId);

    /**
     * 根据商品ID删除关联
     */
    @Delete("DELETE FROM weshop_goods_label WHERE goods_id = #{goodsId}")
    int deleteByGoodsId(@Param("goodsId") Integer goodsId);

    /**
     * 根据标签ID删除关联
     */
    @Delete("DELETE FROM weshop_goods_label WHERE label_id = #{labelId}")
    int deleteByLabelId(@Param("labelId") Integer labelId);

    /**
     * 批量插入商品标签关联
     */
    @Select("<script>" +
            "INSERT INTO weshop_goods_label (goods_id, label_id) VALUES " +
            "<foreach collection='labelIds' item='labelId' separator=','>" +
            "(#{goodsId}, #{labelId})" +
            "</foreach>" +
            "</script>")
    int batchInsert(@Param("goodsId") Integer goodsId, @Param("labelIds") List<Integer> labelIds);
}
