package com.logic.code.mapper;

import com.logic.code.entity.goods.Attribute;
import com.logic.code.model.vo.AttributeWithCategoryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/5
 * @desc 商品属性映射器
 */
@Mapper
public interface AttributeMapper extends CommonMapper<Attribute> {

    /**
     * 查询所有启用的属性
     */
    @Select("SELECT * FROM weshop_attribute WHERE 1=1 ORDER BY sort_order ASC, id ASC")
    List<Attribute> selectAllEnabled();

    /**
     * 根据商品分类查询相关属性
     */
    @Select("SELECT a.* FROM weshop_attribute a " +
            "INNER JOIN weshop_goods g ON g.attribute_category = a.attribute_category_id " +
            "WHERE g.category_id = #{categoryId} " +
            "ORDER BY a.sort_order ASC, a.id ASC")
    List<Attribute> selectByCategoryId(Integer categoryId);

    /**
     * 联合查询属性和属性分类信息
     */
    @Select("SELECT a.id, a.attribute_category_id, a.name, a.input_type, a.sort_order, a.`values`, " +
            "ac.name as category_name, ac.enabled as category_enabled " +
            "FROM weshop_attribute a " +
            "LEFT JOIN weshop_attribute_category ac ON a.attribute_category_id = ac.id " +
            "WHERE (#{name} IS NULL OR a.name LIKE CONCAT('%', #{name}, '%')) " +
            "AND (#{categoryId} IS NULL OR a.attribute_category_id = #{categoryId}) " +
            "AND (ac.enabled IS NULL OR ac.enabled = true) " +
            "ORDER BY ac.name ASC, a.sort_order ASC, a.id ASC " +
            "LIMIT #{offset}, #{limit}")
    List<AttributeWithCategoryVO> selectAttributesWithCategory(String name, Integer categoryId, Integer offset, Integer limit);

    /**
     * 统计联合查询的总数
     */
    @Select("SELECT COUNT(*) FROM weshop_attribute a " +
            "LEFT JOIN weshop_attribute_category ac ON a.attribute_category_id = ac.id " +
            "WHERE (#{name} IS NULL OR a.name LIKE CONCAT('%', #{name}, '%')) " +
            "AND (#{categoryId} IS NULL OR a.attribute_category_id = #{categoryId}) " +
            "AND (ac.enabled IS NULL OR ac.enabled = true)")
    Long countAttributesWithCategory(String name, Integer categoryId);
}
