package com.logic.code.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.logic.code.entity.BankCardInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 银行卡信息Mapper接口
 * <AUTHOR>
 * @date 2025-08-26
 */
@Mapper
public interface BankCardInfoMapper extends BaseMapper<BankCardInfo> {
    
    /**
     * 根据用户ID获取银行卡列表
     * @param userId 用户ID
     * @return 银行卡列表
     */
    @Select("SELECT * FROM weshop_bank_card_info WHERE user_id = #{userId} AND status = 1 ORDER BY is_default DESC, create_time DESC")
    List<BankCardInfo> selectByUserId(@Param("userId") Integer userId);
    
    /**
     * 根据用户ID获取默认银行卡
     * @param userId 用户ID
     * @return 默认银行卡信息
     */
    @Select("SELECT * FROM weshop_bank_card_info WHERE user_id = #{userId} AND is_default = 1 AND status = 1 LIMIT 1")
    BankCardInfo selectDefaultByUserId(@Param("userId") Integer userId);
    
    /**
     * 清除用户的其他默认银行卡标记
     * @param userId 用户ID
     * @param excludeId 排除的银行卡ID
     * @return 更新行数
     */
    @Update("UPDATE weshop_bank_card_info SET is_default = 0 WHERE user_id = #{userId} AND id != #{excludeId}")
    int clearOtherDefaultCards(@Param("userId") Integer userId, @Param("excludeId") Integer excludeId);
    
    /**
     * 根据用户ID和银行卡号查询（用于防重复）
     * @param userId 用户ID
     * @param cardNumber 银行卡号
     * @return 银行卡信息
     */
    @Select("SELECT * FROM weshop_bank_card_info WHERE user_id = #{userId} AND card_number = #{cardNumber} AND status = 1 LIMIT 1")
    BankCardInfo selectByUserIdAndCardNumber(@Param("userId") Integer userId, @Param("cardNumber") String cardNumber);
    
    /**
     * 软删除银行卡（将状态设为0）
     * @param id 银行卡ID
     * @param userId 用户ID（安全检查）
     * @return 更新行数
     */
    @Update("UPDATE weshop_bank_card_info SET status = 0 WHERE id = #{id} AND user_id = #{userId}")
    int softDeleteById(@Param("id") Integer id, @Param("userId") Integer userId);
    
    /**
     * 设置默认银行卡
     * @param id 银行卡ID
     * @param userId 用户ID（安全检查）
     * @return 更新行数
     */
    @Update("UPDATE weshop_bank_card_info SET is_default = 1 WHERE id = #{id} AND user_id = #{userId}")
    int setAsDefault(@Param("id") Integer id, @Param("userId") Integer userId);
}