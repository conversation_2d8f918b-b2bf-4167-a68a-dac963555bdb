package com.logic.code.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logic.code.entity.goods.ParamTemplate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/5
 * @desc 商品参数模板映射器
 */
@Mapper
public interface ParamTemplateMapper extends CommonMapper<ParamTemplate> {

    /**
     * 分页查询参数模板
     */
    Page<ParamTemplate> selectPage(Page<ParamTemplate> page, @Param("name") String name);

    /**
     * 查询所有启用的参数模板
     */
    @Select("SELECT * FROM weshop_param_template WHERE status = 1 ORDER BY create_time DESC")
    List<ParamTemplate> selectAllEnabled();
}
