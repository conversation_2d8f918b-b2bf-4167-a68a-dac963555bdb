package com.logic.code.mapper;

import com.logic.code.entity.goods.ProductLabelCategory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 商品标签分类Mapper
 */
@Mapper
public interface ProductLabelCategoryMapper extends CommonMapper<ProductLabelCategory> {

    /**
     * 分页查询标签分类列表
     */
    @Select("<script>" +
            "SELECT c.*, " +
            "(SELECT COUNT(*) FROM weshop_product_label l WHERE l.cate_id = c.id AND l.status = 1) as label_count " +
            "FROM weshop_product_label_category c " +
            "WHERE 1=1 " +
            "<if test='name != null and name != \"\"'>" +
            "AND c.name LIKE CONCAT('%', #{name}, '%') " +
            "</if>" +
            "<if test='status != null'>" +
            "AND c.status = #{status} " +
            "</if>" +
            "ORDER BY c.sort_order ASC, c.id DESC " +
            "LIMIT #{offset}, #{limit}" +
            "</script>")
    List<Map<String, Object>> selectPageList(@Param("name") String name, 
                                           @Param("status") Integer status,
                                           @Param("offset") Integer offset, 
                                           @Param("limit") Integer limit);

    /**
     * 查询总数
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM weshop_product_label_category c " +
            "WHERE 1=1 " +
            "<if test='name != null and name != \"\"'>" +
            "AND c.name LIKE CONCAT('%', #{name}, '%') " +
            "</if>" +
            "<if test='status != null'>" +
            "AND c.status = #{status} " +
            "</if>" +
            "</script>")
    Long selectPageCount(@Param("name") String name, @Param("status") Integer status);

    /**
     * 查询所有启用的分类
     */
    @Select("SELECT * FROM weshop_product_label_category WHERE status = 1 ORDER BY sort_order ASC, id ASC")
    List<ProductLabelCategory> selectEnabledList();
}
