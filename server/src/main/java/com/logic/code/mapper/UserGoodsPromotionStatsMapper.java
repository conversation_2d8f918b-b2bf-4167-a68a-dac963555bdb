package com.logic.code.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.logic.code.entity.UserGoodsPromotionStats;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 用户商品推广统计Mapper
 */
@Mapper
public interface UserGoodsPromotionStatsMapper extends BaseMapper<UserGoodsPromotionStats>, CommonMapper<UserGoodsPromotionStats> {
    
    /**
     * 根据推广者ID和商品ID查询推广统计
     * @param promoterId 推广者ID
     * @param goodsId 商品ID
     * @return 推广统计
     */
    @Select("SELECT * FROM weshop_user_goods_promotion_stats WHERE promoter_id = #{promoterId} AND goods_id = #{goodsId}")
    UserGoodsPromotionStats selectByPromoterAndGoods(@Param("promoterId") Integer promoterId, @Param("goodsId") Integer goodsId);
    
    /**
     * 获取用户对指定商品的推广次数
     * @param promoterId 推广者ID
     * @param goodsId 商品ID
     * @return 推广次数
     */
    @Select("SELECT COALESCE(total_promotion_count, 0) FROM weshop_user_goods_promotion_stats WHERE promoter_id = #{promoterId} AND goods_id = #{goodsId}")
    Integer getPromotionCount(@Param("promoterId") Integer promoterId, @Param("goodsId") Integer goodsId);
}