package com.logic.code.utils;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 日期工具类
 */
public class DateUtil {
    
    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public static final String YYYY_MM_DD = "yyyy-MM-dd";
    
    /**
     * 日期转字符串
     */
    public static String dateToStr(Date date, String format) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(date);
    }
    
    /**
     * 获取当前时间字符串
     */
    public static String nowDateTimeStr() {
        return dateToStr(new Date(), YYYY_MM_DD_HH_MM_SS);
    }
}
