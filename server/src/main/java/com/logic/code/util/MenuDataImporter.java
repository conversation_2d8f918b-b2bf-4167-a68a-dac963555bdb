package com.logic.code.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 菜单数据导入工具类
 * 用于将login.json中的菜单数据导入到数据库表中
 * 
 * <AUTHOR>
 * @date 2025/1/27
 */
@Slf4j
public class MenuDataImporter {

    /**
     * 主方法，用于生成SQL导入脚本
     */
    public static void main(String[] args) {
        try {
            MenuDataImporter importer = new MenuDataImporter();
            String sql = importer.generateImportSQL();
            
            // 使用项目根目录下的database文件夹
            String projectRoot = System.getProperty("user.dir");
            java.nio.file.Path databaseDir = java.nio.file.Paths.get(projectRoot, "database");
            
            // 确保database目录存在
            if (!java.nio.file.Files.exists(databaseDir)) {
                java.nio.file.Files.createDirectories(databaseDir);
                System.out.println("创建目录: " + databaseDir.toAbsolutePath());
            }
            
            // 写入文件
            java.nio.file.Path outputPath = databaseDir.resolve("import_menu_data_from_json_complete.sql");
            java.nio.file.Files.write(outputPath, sql.getBytes(StandardCharsets.UTF_8));
            
            System.out.println("SQL导入脚本已生成: " + outputPath.toAbsolutePath());
            System.out.println("文件大小: " + sql.length() + " 字符");
            
            // 同时输出到控制台（前100行）
            String[] lines = sql.split("\n");
            int maxLines = Math.min(100, lines.length);
            System.out.println("\n=== SQL脚本预览（前" + maxLines + "行）===");
            for (int i = 0; i < maxLines; i++) {
                System.out.println(lines[i]);
            }
            if (lines.length > maxLines) {
                System.out.println("... 共" + lines.length + "行，已截取前" + maxLines + "行显示");
                System.out.println("完整SQL内容请查看文件: " + outputPath.toAbsolutePath());
            }
        } catch (Exception e) {
            System.err.println("生成导入SQL失败: " + e.getMessage());
            e.printStackTrace();
            
            // 如果文件写入失败，尝试直接输出SQL
            try {
                MenuDataImporter importer = new MenuDataImporter();
                String sql = importer.generateImportSQL();
                System.out.println("\n=== 文件写入失败，直接输出SQL内容 ===");
                System.out.println(sql);
            } catch (Exception ex) {
                System.err.println("生成SQL内容也失败: " + ex.getMessage());
            }
        }
    }

    /**
     * 生成菜单数据导入的SQL脚本
     * 
     * @return SQL脚本字符串
     * @throws IOException 文件读取异常
     */
    public String generateImportSQL() throws IOException {
        // 读取login.json文件
        ClassPathResource resource = new ClassPathResource("json/login.json");
        String jsonContent = new String(resource.getInputStream().readAllBytes(), StandardCharsets.UTF_8);
        
        // 解析JSON
        JSONObject loginData = JSON.parseObject(jsonContent);
        JSONArray menus = loginData.getJSONArray("menus");
        JSONArray uniqueAuth = loginData.getJSONArray("unique_auth");
        
        StringBuilder sql = new StringBuilder();
        sql.append("-- 从login.json导入的菜单数据和权限数据\n");
        sql.append("-- 生成时间: ").append(new Date()).append("\n\n");
        
        // 清空现有数据
        sql.append("-- 清空现有菜单和权限数据\n");
        sql.append("DELETE FROM weshop_menu_permission;\n");
        sql.append("DELETE FROM weshop_system_menu;\n\n");
        
        // 生成菜单数据SQL
        sql.append("-- 插入菜单数据\n");
        List<MenuData> allMenus = new ArrayList<>();
        extractMenusRecursively(menus, allMenus, 0);
        
        // 按ID排序确保父菜单先插入
        allMenus.sort(Comparator.comparing(m -> m.id));
        
        sql.append(String.format("-- 共找到 %d 个菜单项\n", allMenus.size()));
        
        for (MenuData menu : allMenus) {
            sql.append(generateMenuInsertSQL(menu));
        }
        
        sql.append("\n-- 插入菜单权限数据\n");
        Set<String> processedPermissions = new HashSet<>();
        int permissionCount = 0;
        
        // 为每个菜单插入权限数据
        for (MenuData menu : allMenus) {
            if (menu.auth != null && !menu.auth.isEmpty()) {
                for (String permission : menu.auth) {
                    String key = menu.id + "_" + permission;
                    if (!processedPermissions.contains(key)) {
                        sql.append(generatePermissionInsertSQL(menu.id, permission));
                        processedPermissions.add(key);
                        permissionCount++;
                    }
                }
            }
        }
        
        // 插入unique_auth中的权限（如果菜单中没有的话）
        sql.append("\n-- 插入系统级权限数据（来自unique_auth）\n");
        int systemPermissionCount = 0;
        if (uniqueAuth != null) {
            for (int i = 0; i < uniqueAuth.size(); i++) {
                String permission = uniqueAuth.getString(i);
                if (!isPermissionInMenus(permission, allMenus)) {
                    // 为系统级权限创建一个特殊的菜单ID (9999)
                    sql.append(generatePermissionInsertSQL(9999, permission));
                    systemPermissionCount++;
                }
            }
        }
        
        sql.append("\n-- 导入统计信息\n");
        sql.append(String.format("-- 菜单项数量: %d\n", allMenus.size()));
        sql.append(String.format("-- 菜单权限数量: %d\n", permissionCount));
        sql.append(String.format("-- 系统权限数量: %d\n", systemPermissionCount));
        sql.append(String.format("-- 总权限数量: %d\n", permissionCount + systemPermissionCount));
        
        return sql.toString();
    }

    /**
     * 递归提取菜单数据
     */
    private void extractMenusRecursively(JSONArray menuArray, List<MenuData> result, int baseSortOrder) {
        if (menuArray == null) return;
        
        for (int i = 0; i < menuArray.size(); i++) {
            JSONObject menuJson = menuArray.getJSONObject(i);
            MenuData menu = parseMenuData(menuJson, baseSortOrder + i);
            result.add(menu);
            
            // 递归处理子菜单
            JSONArray children = menuJson.getJSONArray("children");
            if (children != null && children.size() > 0) {
                extractMenusRecursively(children, result, 0);
            }
        }
    }

    /**
     * 解析单个菜单数据
     */
    private MenuData parseMenuData(JSONObject menuJson, int defaultSortOrder) {
        MenuData menu = new MenuData();
        menu.id = menuJson.getInteger("id");
        menu.pid = menuJson.getInteger("pid");
        menu.path = menuJson.getString("path");
        menu.title = menuJson.getString("title");
        menu.icon = menuJson.getString("icon");
        menu.header = menuJson.getString("header");
        menu.isHeader = menuJson.getInteger("is_header");
        menu.isShow = menuJson.getInteger("is_show");
        menu.sortOrder = defaultSortOrder;
        
        // 解析权限数组
        JSONArray authArray = menuJson.getJSONArray("auth");
        if (authArray != null) {
            menu.auth = new ArrayList<>();
            for (int i = 0; i < authArray.size(); i++) {
                menu.auth.add(authArray.getString(i));
            }
        }
        
        return menu;
    }

    /**
     * 生成菜单插入SQL
     */
    private String generateMenuInsertSQL(MenuData menu) {
        return String.format(
            "INSERT INTO weshop_system_menu (id, pid, path, title, icon, header, is_header, is_show, sort_order, create_time, update_time) " +
            "VALUES (%d, %d, '%s', '%s', '%s', '%s', %d, %d, %d, NOW(), NOW());\n",
            menu.id,
            menu.pid,
            escapeSqlString(menu.path),
            escapeSqlString(menu.title),
            escapeSqlString(menu.icon != null ? menu.icon : ""),
            escapeSqlString(menu.header != null ? menu.header : ""),
            menu.isHeader != null ? menu.isHeader : 0,
            menu.isShow != null ? menu.isShow : 1,
            menu.sortOrder
        );
    }

    /**
     * 生成权限插入SQL
     */
    private String generatePermissionInsertSQL(Integer menuId, String permission) {
        return String.format(
            "INSERT INTO weshop_menu_permission (menu_id, permission, create_time) " +
            "VALUES (%d, '%s', NOW());\n",
            menuId,
            escapeSqlString(permission)
        );
    }

    /**
     * 检查权限是否已经在菜单中定义
     */
    private boolean isPermissionInMenus(String permission, List<MenuData> menus) {
        for (MenuData menu : menus) {
            if (menu.auth != null && menu.auth.contains(permission)) {
                return true;
            }
        }
        return false;
    }

    /**
     * SQL字符串转义
     */
    private String escapeSqlString(String str) {
        if (str == null) return "";
        return str.replace("'", "''")
                 .replace("\\", "\\\\")
                 .replace("\n", "\\n")
                 .replace("\r", "\\r");
    }

    /**
     * 菜单数据内部类
     */
    private static class MenuData {
        Integer id;
        Integer pid;
        String path;
        String title;
        String icon;
        String header;
        Integer isHeader;
        Integer isShow;
        Integer sortOrder;
        List<String> auth;
    }
}