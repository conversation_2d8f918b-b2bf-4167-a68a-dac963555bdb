package com.logic.code.util;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileInputStream;
import java.io.IOException;
import java.sql.*;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 简单的订单信息更新工具
 * 不依赖Spring Boot，可以直接运行
 */
public class SimpleOrderUpdateUtil {

    // 数据库连接配置 - 请根据实际情况修改
    private static final String DB_URL = "*****************************************";
    private static final String DB_USER = "your_username";
    private static final String DB_PASSWORD = "your_password";

    public static void main(String[] args) {
        if (args.length < 1) {
            System.out.println("请提供Excel文件路径作为参数");
            System.out.println("用法: java SimpleOrderUpdateUtil <excel_file_path>");
            return;
        }

        String filePath = args[0];
        SimpleOrderUpdateUtil util = new SimpleOrderUpdateUtil();
        util.updateOrdersFromExcel(filePath);
    }

    /**
     * 根据Excel文件更新订单信息
     * @param filePath Excel文件路径
     */
    public void updateOrdersFromExcel(String filePath) {
        Connection conn = null;
        try {
            // 建立数据库连接
            conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD);
            
            // 读取Excel文件
            try (FileInputStream fis = new FileInputStream(filePath);
                 Workbook workbook = new XSSFWorkbook(fis)) {

                Sheet sheet = workbook.getSheetAt(0); // 获取第一个工作表
                int rowCount = sheet.getPhysicalNumberOfRows();
                
                System.out.println("开始处理Excel文件，总行数: " + rowCount);
                
                // 跳过标题行，从第二行开始处理数据
                for (int i = 1; i < rowCount; i++) {
                    Row row = sheet.getRow(i);
                    if (row == null) continue;
                    
                    try {
                        // 读取行数据
                        String payTimeStr = getCellValueAsString(row.getCell(0)); // 支付时间
                        String transactionId = getCellValueAsString(row.getCell(1)); // 交易单号
                        String outTradeNo = getCellValueAsString(row.getCell(2)); // 商户单号
                        
                        // 更新订单信息
                        updateOrderInfo(conn, payTimeStr, transactionId, outTradeNo);
                    } catch (Exception e) {
                        System.err.println("处理第" + (i + 1) + "行数据时出错: " + e.getMessage());
                    }
                }
                
                System.out.println("Excel文件处理完成");
            }
        } catch (IOException e) {
            System.err.println("读取Excel文件失败: " + e.getMessage());
        } catch (SQLException e) {
            System.err.println("数据库连接失败: " + e.getMessage());
        } finally {
            if (conn != null) {
                try {
                    conn.close();
                } catch (SQLException e) {
                    System.err.println("关闭数据库连接失败: " + e.getMessage());
                }
            }
        }
    }
    
    /**
     * 更新单个订单信息
     * @param conn 数据库连接
     * @param payTimeStr 支付时间字符串
     * @param transactionId 交易单号
     * @param outTradeNo 商户单号
     */
    private void updateOrderInfo(Connection conn, String payTimeStr, String transactionId, String outTradeNo) {
        if (outTradeNo == null || outTradeNo.isEmpty()) {
            System.out.println("商户单号为空，跳过处理");
            return;
        }
        
        try {
            // 检查订单是否存在
            String checkSql = "SELECT id FROM weshop_order WHERE order_sn = ?";
            PreparedStatement checkStmt = conn.prepareStatement(checkSql);
            checkStmt.setString(1, outTradeNo);
            ResultSet rs = checkStmt.executeQuery();
            
            if (!rs.next()) {
                System.out.println("未找到商户单号为" + outTradeNo + "的订单");
                rs.close();
                checkStmt.close();
                return;
            }
            
            int orderId = rs.getInt("id");
            rs.close();
            checkStmt.close();
            
            // 构建更新SQL
            StringBuilder sqlBuilder = new StringBuilder("UPDATE weshop_order SET ");
            boolean needUpdate = false;
            
            if (payTimeStr != null && !payTimeStr.isEmpty()) {
                Date payTime = parseDate(payTimeStr);
                if (payTime != null) {
                    sqlBuilder.append("pay_time = ?, ");
                    needUpdate = true;
                }
            }
            
            if (transactionId != null && !transactionId.isEmpty()) {
                sqlBuilder.append("transaction_id = ?, ");
                needUpdate = true;
            }
            
            if (!needUpdate) {
                System.out.println("订单" + outTradeNo + "无需更新");
                return;
            }
            
            // 移除最后的逗号和空格
            sqlBuilder.setLength(sqlBuilder.length() - 2);
            sqlBuilder.append(" WHERE id = ?");
            
            // 执行更新操作
            PreparedStatement updateStmt = conn.prepareStatement(sqlBuilder.toString());
            int paramIndex = 1;
            
            if (payTimeStr != null && !payTimeStr.isEmpty()) {
                Date payTime = parseDate(payTimeStr);
                if (payTime != null) {
                    updateStmt.setTimestamp(paramIndex++, new Timestamp(payTime.getTime()));
                }
            }
            
            if (transactionId != null && !transactionId.isEmpty()) {
                updateStmt.setString(paramIndex++, transactionId);
            }
            
            updateStmt.setInt(paramIndex, orderId);
            
            int updatedRows = updateStmt.executeUpdate();
            if (updatedRows > 0) {
                System.out.println("订单" + outTradeNo + "更新成功");
            } else {
                System.out.println("订单" + outTradeNo + "更新失败");
            }
            
            updateStmt.close();
        } catch (Exception e) {
            System.err.println("更新订单" + outTradeNo + "信息时出错: " + e.getMessage());
        }
    }
    
    /**
     * 获取单元格的字符串值
     * @param cell 单元格
     * @return 字符串值
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return null;
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    return sdf.format(cell.getDateCellValue());
                } else {
                    // 数值类型转换为字符串
                    return String.valueOf((long) cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return null;
        }
    }
    
    /**
     * 解析日期字符串
     * @param dateStr 日期字符串
     * @return Date对象
     */
    private Date parseDate(String dateStr) {
        if (dateStr == null || dateStr.isEmpty()) {
            return null;
        }
        
        // 尝试多种日期格式
        String[] patterns = {
            "yyyy-MM-dd HH:mm:ss",
            "yyyy/MM/dd HH:mm:ss",
            "yyyy-MM-dd",
            "yyyy/MM/dd"
        };
        
        for (String pattern : patterns) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat(pattern);
                return sdf.parse(dateStr);
            } catch (ParseException e) {
                // 继续尝试下一种格式
            }
        }
        
        System.out.println("无法解析日期字符串: " + dateStr);
        return null;
    }
}