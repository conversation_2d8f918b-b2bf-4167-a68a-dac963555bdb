package com.logic.code.util;

import com.logic.code.service.PromotionDataMigrationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 推广数据迁移命令行工具
 * 可以通过启动参数 --migration.promotion.enabled=true 来启用自动迁移
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "migration.promotion.enabled", havingValue = "true")
public class PromotionDataMigrationRunner implements CommandLineRunner {

    @Autowired
    private PromotionDataMigrationService migrationService;

    @Override
    public void run(String... args) throws Exception {
        log.info("=== 推广数据迁移工具启动 ===");

        try {
            // 先获取统计信息
            Map<String, Object> stats = migrationService.getMigrationStats();
            log.info("迁移前统计信息: {}", stats);

            // 执行迁移
            log.info("开始执行推广数据迁移...");
            Map<String, Object> result = migrationService.migratePromotionData();

            // 输出结果
            if ((Boolean) result.get("success")) {
                log.info("=== 推广数据迁移完成 ===");
                log.info("总订单数: {}", result.get("totalOrders"));
                log.info("迁移成功: {}", result.get("migratedOrders"));
                log.info("跳过订单: {}", result.get("skippedOrders"));
                log.info("错误订单: {}", result.get("errorOrders"));
            } else {
                log.error("推广数据迁移失败: {}", result.get("message"));
            }

            // 获取迁移后统计信息
            Map<String, Object> afterStats = migrationService.getMigrationStats();
            log.info("迁移后统计信息: {}", afterStats);

        } catch (Exception e) {
            log.error("推广数据迁移工具执行失败: {}", e.getMessage(), e);
        }

        log.info("=== 推广数据迁移工具结束 ===");
    }
}
