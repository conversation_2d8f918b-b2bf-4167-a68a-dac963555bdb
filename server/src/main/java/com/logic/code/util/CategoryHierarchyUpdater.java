package com.logic.code.util;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.logic.code.entity.file.FileCategory;
import com.logic.code.mapper.FileCategoryMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 分类层级更新工具类
 * 用于修复和更新分类的层级和路径信息
 */
@Component
public class CategoryHierarchyUpdater {
    
    private static final Logger logger = LoggerFactory.getLogger(CategoryHierarchyUpdater.class);
    
    @Autowired
    private FileCategoryMapper fileCategoryMapper;
    
    /**
     * 更新所有分类的层级和路径信息
     */
    public void updateAllCategoryHierarchy() {
        logger.info("开始更新分类层级和路径信息...");
        
        try {
            // 获取所有有效分类
            QueryWrapper<FileCategory> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("is_delete", false);
            queryWrapper.orderByAsc("parent_id", "sort_order", "id");
            List<FileCategory> allCategories = fileCategoryMapper.selectList(queryWrapper);
            
            if (allCategories.isEmpty()) {
                logger.info("没有找到需要更新的分类");
                return;
            }
            
            // 构建分类映射
            Map<Integer, FileCategory> categoryMap = new HashMap<>();
            Map<Integer, List<FileCategory>> childrenMap = new HashMap<>();
            
            for (FileCategory category : allCategories) {
                categoryMap.put(category.getId(), category);
                
                Integer parentId = category.getParentId();
                if (parentId == null) {
                    parentId = 0;
                }
                
                childrenMap.computeIfAbsent(parentId, k -> new ArrayList<>()).add(category);
            }
            
            // 从根分类开始递归更新
            List<FileCategory> rootCategories = childrenMap.get(0);
            if (rootCategories != null) {
                for (FileCategory rootCategory : rootCategories) {
                    updateCategoryHierarchyRecursive(rootCategory, 0, "0", categoryMap, childrenMap);
                }
            }
            
            logger.info("分类层级和路径信息更新完成，共更新 {} 个分类", allCategories.size());
            
        } catch (Exception e) {
            logger.error("更新分类层级和路径信息时发生错误", e);
            throw new RuntimeException("更新分类层级失败", e);
        }
    }
    
    /**
     * 递归更新分类层级和路径
     */
    private void updateCategoryHierarchyRecursive(
            FileCategory category, 
            int level, 
            String parentPath,
            Map<Integer, FileCategory> categoryMap,
            Map<Integer, List<FileCategory>> childrenMap) {
        
        // 更新当前分类的层级和路径
        String currentPath = parentPath + "," + category.getId();
        category.setLevel(level);
        category.setPath(currentPath);
        
        // 更新数据库
        fileCategoryMapper.updateById(category);
        
        logger.debug("更新分类: ID={}, 名称={}, 层级={}, 路径={}", 
                category.getId(), category.getTitle(), level, currentPath);
        
        // 递归更新子分类
        List<FileCategory> children = childrenMap.get(category.getId());
        if (children != null && !children.isEmpty()) {
            for (FileCategory child : children) {
                updateCategoryHierarchyRecursive(child, level + 1, currentPath, categoryMap, childrenMap);
            }
        }
    }
    
    /**
     * 验证分类层级结构的完整性
     */
    public boolean validateCategoryHierarchy() {
        logger.info("开始验证分类层级结构...");
        
        try {
            QueryWrapper<FileCategory> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("is_delete", false);
            List<FileCategory> allCategories = fileCategoryMapper.selectList(queryWrapper);
            
            boolean isValid = true;
            int invalidCount = 0;
            
            for (FileCategory category : allCategories) {
                // 检查层级是否正确
                if (category.getLevel() == null) {
                    logger.warn("分类层级为空: ID={}, 名称={}", category.getId(), category.getTitle());
                    isValid = false;
                    invalidCount++;
                    continue;
                }
                
                // 检查路径是否正确
                if (category.getPath() == null || category.getPath().isEmpty()) {
                    logger.warn("分类路径为空: ID={}, 名称={}", category.getId(), category.getTitle());
                    isValid = false;
                    invalidCount++;
                    continue;
                }
                
                // 检查根分类
                if (category.getParentId() == 0) {
                    if (category.getLevel() != 0) {
                        logger.warn("根分类层级不正确: ID={}, 名称={}, 层级={}", 
                                category.getId(), category.getTitle(), category.getLevel());
                        isValid = false;
                        invalidCount++;
                    }
                    if (!"0".equals(category.getPath())) {
                        logger.warn("根分类路径不正确: ID={}, 名称={}, 路径={}", 
                                category.getId(), category.getTitle(), category.getPath());
                        isValid = false;
                        invalidCount++;
                    }
                } else {
                    // 检查非根分类的父分类是否存在
                    FileCategory parent = fileCategoryMapper.selectById(category.getParentId());
                    if (parent == null || parent.getIsDelete()) {
                        logger.warn("分类的父分类不存在或已删除: ID={}, 名称={}, 父分类ID={}", 
                                category.getId(), category.getTitle(), category.getParentId());
                        isValid = false;
                        invalidCount++;
                    } else {
                        // 检查层级是否比父分类大1
                        if (category.getLevel() != parent.getLevel() + 1) {
                            logger.warn("分类层级不正确: ID={}, 名称={}, 层级={}, 父分类层级={}", 
                                    category.getId(), category.getTitle(), category.getLevel(), parent.getLevel());
                            isValid = false;
                            invalidCount++;
                        }
                        
                        // 检查路径是否正确
                        String expectedPath = parent.getPath() + "," + parent.getId();
                        if (!expectedPath.equals(category.getPath())) {
                            logger.warn("分类路径不正确: ID={}, 名称={}, 实际路径={}, 期望路径={}", 
                                    category.getId(), category.getTitle(), category.getPath(), expectedPath);
                            isValid = false;
                            invalidCount++;
                        }
                    }
                }
            }
            
            if (isValid) {
                logger.info("分类层级结构验证通过，共验证 {} 个分类", allCategories.size());
            } else {
                logger.warn("分类层级结构验证失败，共发现 {} 个问题", invalidCount);
            }
            
            return isValid;
            
        } catch (Exception e) {
            logger.error("验证分类层级结构时发生错误", e);
            return false;
        }
    }
    
    /**
     * 修复分类层级结构
     * 如果验证失败，可以调用此方法进行修复
     */
    public void fixCategoryHierarchy() {
        logger.info("开始修复分类层级结构...");
        
        if (!validateCategoryHierarchy()) {
            logger.info("发现分类层级问题，开始自动修复...");
            updateAllCategoryHierarchy();
            
            // 再次验证
            if (validateCategoryHierarchy()) {
                logger.info("分类层级结构修复成功");
            } else {
                logger.error("分类层级结构修复失败，请手动检查数据");
            }
        } else {
            logger.info("分类层级结构正常，无需修复");
        }
    }
    
    /**
     * 获取分类层级统计信息
     */
    public Map<String, Object> getCategoryHierarchyStats() {
        QueryWrapper<FileCategory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_delete", false);
        List<FileCategory> allCategories = fileCategoryMapper.selectList(queryWrapper);
        
        Map<String, Object> stats = new HashMap<>();
        Map<Integer, Integer> levelCounts = new HashMap<>();
        
        int totalCount = allCategories.size();
        int maxLevel = 0;
        
        for (FileCategory category : allCategories) {
            Integer level = category.getLevel();
            if (level != null) {
                levelCounts.put(level, levelCounts.getOrDefault(level, 0) + 1);
                maxLevel = Math.max(maxLevel, level);
            }
        }
        
        stats.put("totalCount", totalCount);
        stats.put("maxLevel", maxLevel);
        stats.put("levelCounts", levelCounts);
        
        return stats;
    }
}
