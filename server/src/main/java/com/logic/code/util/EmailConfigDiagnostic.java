package com.logic.code.util;

import jakarta.mail.*;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Properties;

/**
 * 邮件配置诊断工具
 * 用于测试和诊断邮件配置问题
 *
 * <AUTHOR>
 * @date 2025/7/15
 */
@Slf4j
@Component
public class EmailConfigDiagnostic {

    @Value("${spring.mail.host}")
    private String host;

    @Value("${spring.mail.port}")
    private int port;

    @Value("${spring.mail.username}")
    private String username;

    @Value("${spring.mail.password}")
    private String password;

    /**
     * 诊断邮件配置
     *
     * @return 诊断结果
     */
    public String diagnoseEmailConfig() {
        StringBuilder result = new StringBuilder();
        result.append("=== 邮件配置诊断 ===\n");

        // 1. 检查基本配置
        result.append("1. 基本配置检查:\n");
        result.append("   - 邮件服务器: ").append(host).append("\n");
        result.append("   - 端口: ").append(port).append("\n");
        result.append("   - 用户名: ").append(username).append("\n");
        result.append("   - 密码: ").append(password != null && !password.isEmpty() ? "已配置" : "未配置").append("\n");

        // 2. 检查QQ邮箱特殊要求
        if (host.contains("qq.com")) {
            result.append("\n2. QQ邮箱配置检查:\n");
            result.append("   - 检测到QQ邮箱配置\n");
            result.append("   - 注意：QQ邮箱需要使用授权码，不是登录密码\n");
            result.append("   - 获取授权码步骤：\n");
            result.append("     1. 登录QQ邮箱网页版\n");
            result.append("     2. 设置 -> 账户 -> POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务\n");
            result.append("     3. 开启POP3/SMTP服务\n");
            result.append("     4. 生成授权码\n");
        }

        // 3. 测试连接
        result.append("\n3. 连接测试:\n");
        try {
            testConnection();
            result.append("   - 连接测试: 成功\n");
        } catch (Exception e) {
            result.append("   - 连接测试: 失败\n");
            result.append("   - 错误信息: ").append(e.getMessage()).append("\n");

            // 提供解决建议
            result.append("\n4. 解决建议:\n");
            if (e.getMessage().contains("535")) {
                result.append("   - 535错误通常表示认证失败\n");
                result.append("   - 请检查用户名和密码（授权码）是否正确\n");
                result.append("   - 确保已开启SMTP服务\n");
            } else if (e.getMessage().contains("Connection")) {
                result.append("   - 连接错误，请检查网络和服务器地址\n");
                result.append("   - 确认端口号是否正确\n");
            }
        }

        return result.toString();
    }

    /**
     * 测试邮件服务器连接
     */
    private void testConnection() throws MessagingException {
        Properties props = new Properties();
        props.put("mail.smtp.host", host);
        props.put("mail.smtp.port", port);
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.starttls.enable", "true");
        props.put("mail.smtp.starttls.required", "true");
        props.put("mail.smtp.ssl.enable", "false");

        Session session = Session.getInstance(props, new Authenticator() {
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(username, password);
            }
        });

        // 测试连接
        Transport transport = session.getTransport("smtp");
        transport.connect(host, port, username, password);
        transport.close();
    }

    /**
     * 发送测试邮件
     *
     * @param toEmail 收件人邮箱
     * @return 是否发送成功
     */
    public boolean sendTestEmail(String toEmail) {
        try {
            Properties props = new Properties();
            props.put("mail.smtp.host", host);
            props.put("mail.smtp.port", port);
            props.put("mail.smtp.auth", "true");
            props.put("mail.smtp.starttls.enable", "true");
            props.put("mail.smtp.starttls.required", "true");
            props.put("mail.smtp.ssl.enable", "false");

            Session session = Session.getInstance(props, new Authenticator() {
                @Override
                protected PasswordAuthentication getPasswordAuthentication() {
                    return new PasswordAuthentication(username, password);
                }
            });

            Message message = new MimeMessage(session);
            message.setFrom(new InternetAddress(username));
            message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(toEmail));
            message.setSubject("邮件配置测试");
            message.setText("这是一封测试邮件，用于验证邮件配置是否正确。\n\n发送时间：" + new java.util.Date());

            Transport.send(message);
            log.info("测试邮件发送成功，收件人：{}", toEmail);
            return true;
        } catch (Exception e) {
            log.error("测试邮件发送失败，收件人：{}，错误：{}", toEmail, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取常见邮箱服务器配置
     *
     * @param emailType 邮箱类型
     * @return 配置信息
     */
    public String getEmailServerConfig(String emailType) {
        StringBuilder config = new StringBuilder();
        config.append("=== ").append(emailType).append(" 邮箱配置 ===\n");

        switch (emailType.toLowerCase()) {
            case "qq":
                config.append("SMTP服务器: smtp.qq.com\n");
                config.append("端口: 587 (STARTTLS) 或 465 (SSL)\n");
                config.append("认证: 需要\n");
                config.append("特殊说明: 需要使用授权码，不是登录密码\n");
                break;
            case "163":
                config.append("SMTP服务器: smtp.163.com\n");
                config.append("端口: 587 (STARTTLS) 或 465 (SSL)\n");
                config.append("认证: 需要\n");
                config.append("特殊说明: 需要开启客户端授权密码\n");
                break;
            case "gmail":
                config.append("SMTP服务器: smtp.gmail.com\n");
                config.append("端口: 587 (STARTTLS) 或 465 (SSL)\n");
                config.append("认证: 需要\n");
                config.append("特殊说明: 需要开启两步验证并使用应用专用密码\n");
                break;
            case "outlook":
                config.append("SMTP服务器: smtp-mail.outlook.com\n");
                config.append("端口: 587 (STARTTLS)\n");
                config.append("认证: 需要\n");
                config.append("特殊说明: 使用Microsoft账户密码\n");
                break;
            default:
                config.append("未知邮箱类型，请查阅相关文档\n");
        }

        return config.toString();
    }
}
