package com.logic.code.config.typehandler;

import com.logic.code.common.enmus.CategoryLevelEnum;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Custom TypeHandler for CategoryLevelEnum
 * Converts between database integer values and CategoryLevelEnum values
 */
public class CategoryLevelEnumTypeHandler extends BaseTypeHandler<CategoryLevelEnum> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, CategoryLevelEnum parameter, JdbcType jdbcType) throws SQLException {
        ps.setInt(i, parameter.ordinal());
    }

    @Override
    public CategoryLevelEnum getNullableResult(ResultSet rs, String columnName) throws SQLException {
        int value = rs.getInt(columnName);
        return rs.wasNull() ? null : CategoryLevelEnum.fromValue(value);
    }

    @Override
    public CategoryLevelEnum getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        int value = rs.getInt(columnIndex);
        return rs.wasNull() ? null : CategoryLevelEnum.fromValue(value);
    }

    @Override
    public CategoryLevelEnum getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        int value = cs.getInt(columnIndex);
        return cs.wasNull() ? null : CategoryLevelEnum.fromValue(value);
    }
} 