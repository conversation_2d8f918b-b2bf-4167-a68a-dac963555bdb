package com.logic.code.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Token配置类
 * 
 * <AUTHOR>
 * @date 2025/7/29
 */
@Data
@Component
@ConfigurationProperties(prefix = "weshop.jwt")
public class TokenConfig {

    /**
     * JWT密钥
     */
    private String secret = "asdqweasasdqweasasdqweasasdqweasasdqweasasdqweasasdqweasasdqweas";

    /**
     * Token有效期（毫秒）
     * 默认7天
     */
    //private long ttl = 2L * 60L * 1000L;
    private long ttl = 7L * 24L * 60L * 60L * 1000L;

    /**
     * Token刷新阈值（毫秒）
     * 当token剩余有效期小于此时间时，可以刷新
     * 默认2天
     */
    private long refreshThreshold = 2L * 24L * 60L * 60L * 1000L;

    /**
     * 是否启用自动刷新
     */
    private boolean autoRefreshEnabled = true;

    /**
     * 是否启用过期token刷新
     * 允许在token过期后的一定时间内仍可刷新
     */
    private boolean expiredRefreshEnabled = true;

    /**
     * 过期token可刷新的时间窗口（毫秒）
     * 默认1小时
     */
    private long expiredRefreshWindow = 60L * 60L * 1000L;

    /**
     * Token请求头名称
     */
    private String headerName = "X-Weshop-Token";

    /**
     * 新Token响应头名称
     */
    private String newTokenHeaderName = "X-New-Token";

    /**
     * 是否在日志中记录token操作
     */
    private boolean logEnabled = true;

    /**
     * 是否启用token状态检查接口
     */
    private boolean statusCheckEnabled = true;

    /**
     * 是否启用手动刷新接口
     */
    private boolean manualRefreshEnabled = true;
}
