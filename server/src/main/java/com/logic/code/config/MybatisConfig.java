package com.logic.code.config;

import com.logic.code.common.enmus.CategoryLevelEnum;
import com.logic.code.common.enmus.OrderStatusEnum;
import com.logic.code.common.enmus.PayStatusEnum;
import com.logic.code.common.enmus.RegionTypeEnum;
import com.logic.code.common.typehandlers.OrderStatusEnumTypeHandler;
import com.logic.code.common.typehandlers.PayStatusEnumTypeHandler;
import com.logic.code.common.typehandlers.RegionTypeEnumTypeHandler;
import com.logic.code.config.typehandler.CategoryLevelEnumTypeHandler;
import jakarta.annotation.PostConstruct;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

/**
 * MyBatis Mapper configuration
 */
@Configuration
public class MybatisConfig {

    @Autowired
    private SqlSessionFactory sqlSessionFactory;

    @PostConstruct
    public void registerTypeHandlers() {
        TypeHandlerRegistry typeHandlerRegistry = sqlSessionFactory.getConfiguration().getTypeHandlerRegistry();
        // Register custom type handler for CategoryLevelEnum
        typeHandlerRegistry.register(CategoryLevelEnum.class, CategoryLevelEnumTypeHandler.class);
        // Register custom type handler for RegionTypeEnum
        typeHandlerRegistry.register(RegionTypeEnum.class, RegionTypeEnumTypeHandler.class);
        // Register custom type handler for OrderStatusEnum
        typeHandlerRegistry.register(OrderStatusEnum.class, OrderStatusEnumTypeHandler.class);
        // Register custom type handler for PayStatusEnum
        typeHandlerRegistry.register(PayStatusEnum.class, PayStatusEnumTypeHandler.class);
    }

    /**
     * Configure the MapperScannerConfigurer with proper properties
     */
   /* @Bean
    public MapperScannerConfigurer mapperScannerConfigurer() {
        MapperScannerConfigurer scannerConfigurer = new MapperScannerConfigurer();
        // Set the mapper package to scan
        scannerConfigurer.setBasePackage("com.logic.code.mapper");

        // Ensure mapper interfaces are loaded before attempting to process them
        scannerConfigurer.setSqlSessionFactoryBeanName("sqlSessionFactory");

        // Set properties explicitly as a Properties object
        Properties properties = new Properties();
        // Specify which mapper interfaces to use
        properties.setProperty("mappers", "tk.mybatis.mapper.common.BaseMapper,tk.mybatis.mapper.common.MySqlMapper");
        // Marker interface all entities should implement to help with generic type resolution
        properties.setProperty("markerInterface", BaseEntity.class.getName());
        // Using the identity strategy for MySQL
        properties.setProperty("IDENTITY", "MYSQL");
        // Don't require non-empty conditions
        properties.setProperty("notEmpty", "false");
        // Use camelhump naming style (snake_case in DB to camelCase in Java)
        properties.setProperty("style", "camelhump");
        // Enable safe updates by default
        properties.setProperty("safeUpdate", "true");
        // Ensure IDs are checked before updates
        properties.setProperty("checkExampleEntityClass", "true");
        // Set database-specific dialect
        properties.setProperty("dialect", "mysql");

        scannerConfigurer.setProperties(properties);

        return scannerConfigurer;
    }*/
}
