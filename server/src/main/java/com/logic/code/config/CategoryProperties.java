package com.logic.code.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 分类系统配置属性
 */
@Component
@ConfigurationProperties(prefix = "category")
public class CategoryProperties {
    
    /**
     * 是否在应用启动时自动修复分类层级结构
     */
    private boolean autoFixHierarchy = true;
    
    /**
     * 是否在启动时显示分类统计信息
     */
    private boolean showStats = true;
    
    /**
     * 分类层级限制
     */
    private int maxLevel = 5;
    
    /**
     * 默认排序值
     */
    private int defaultSortOrder = 0;
    
    /**
     * 是否允许删除包含文件的分类
     */
    private boolean allowDeleteWithFiles = false;
    
    /**
     * 是否允许删除包含子分类的分类
     */
    private boolean allowDeleteWithChildren = false;
    
    /**
     * 分类名称配置
     */
    private Title title = new Title();
    
    /**
     * 分类描述配置
     */
    private Description description = new Description();
    
    // Getters and Setters
    public boolean isAutoFixHierarchy() {
        return autoFixHierarchy;
    }
    
    public void setAutoFixHierarchy(boolean autoFixHierarchy) {
        this.autoFixHierarchy = autoFixHierarchy;
    }
    
    public boolean isShowStats() {
        return showStats;
    }
    
    public void setShowStats(boolean showStats) {
        this.showStats = showStats;
    }
    
    public int getMaxLevel() {
        return maxLevel;
    }
    
    public void setMaxLevel(int maxLevel) {
        this.maxLevel = maxLevel;
    }
    
    public int getDefaultSortOrder() {
        return defaultSortOrder;
    }
    
    public void setDefaultSortOrder(int defaultSortOrder) {
        this.defaultSortOrder = defaultSortOrder;
    }
    
    public boolean isAllowDeleteWithFiles() {
        return allowDeleteWithFiles;
    }
    
    public void setAllowDeleteWithFiles(boolean allowDeleteWithFiles) {
        this.allowDeleteWithFiles = allowDeleteWithFiles;
    }
    
    public boolean isAllowDeleteWithChildren() {
        return allowDeleteWithChildren;
    }
    
    public void setAllowDeleteWithChildren(boolean allowDeleteWithChildren) {
        this.allowDeleteWithChildren = allowDeleteWithChildren;
    }
    
    public Title getTitle() {
        return title;
    }
    
    public void setTitle(Title title) {
        this.title = title;
    }
    
    public Description getDescription() {
        return description;
    }
    
    public void setDescription(Description description) {
        this.description = description;
    }
    
    /**
     * 分类名称配置
     */
    public static class Title {
        private int minLength = 1;
        private int maxLength = 50;
        
        public int getMinLength() {
            return minLength;
        }
        
        public void setMinLength(int minLength) {
            this.minLength = minLength;
        }
        
        public int getMaxLength() {
            return maxLength;
        }
        
        public void setMaxLength(int maxLength) {
            this.maxLength = maxLength;
        }
    }
    
    /**
     * 分类描述配置
     */
    public static class Description {
        private int maxLength = 200;
        
        public int getMaxLength() {
            return maxLength;
        }
        
        public void setMaxLength(int maxLength) {
            this.maxLength = maxLength;
        }
    }
}
