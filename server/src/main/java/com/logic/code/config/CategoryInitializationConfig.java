package com.logic.code.config;

import com.logic.code.util.CategoryHierarchyUpdater;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * 分类初始化配置
 * 在应用启动时检查和修复分类层级结构
 */
@Component
public class CategoryInitializationConfig implements ApplicationRunner {

    private static final Logger logger = LoggerFactory.getLogger(CategoryInitializationConfig.class);

    @Autowired
    private CategoryHierarchyUpdater categoryHierarchyUpdater;

    /**
     * 是否在启动时自动修复分类层级
     * 可以通过配置文件控制：category.auto-fix-hierarchy=true/false
     */
    @Value("${category.auto-fix-hierarchy:false}")
    private boolean autoFixHierarchy;

    /**
     * 是否在启动时显示分类统计信息
     */
    @Value("${category.show-stats:false}")
    private boolean showStats;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        logger.info("开始初始化分类系统...");

        /*try {
            if (autoFixHierarchy) {
                // 检查并修复分类层级结构
                categoryHierarchyUpdater.fixCategoryHierarchy();
            } else {
                // 仅验证分类层级结构
                boolean isValid = categoryHierarchyUpdater.validateCategoryHierarchy();
                if (!isValid) {
                    logger.warn("分类层级结构存在问题，建议设置 category.auto-fix-hierarchy=true 来自动修复");
                }
            }

            if (showStats) {
                // 显示分类统计信息
                var stats = categoryHierarchyUpdater.getCategoryHierarchyStats();
                logger.info("分类统计信息: {}", stats);
            }

            logger.info("分类系统初始化完成");

        } catch (Exception e) {
            logger.error("分类系统初始化失败", e);
            // 不抛出异常，避免影响应用启动
        }*/
    }
}
