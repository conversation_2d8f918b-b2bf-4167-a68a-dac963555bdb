package com.logic.code.config;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.bean.WxMaKefuMessage;
import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage.MsgData;
import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import cn.binarywang.wx.miniapp.message.WxMaMessageHandler;
import cn.binarywang.wx.miniapp.message.WxMaMessageRouter;
import com.google.common.collect.Lists;
import jakarta.annotation.PostConstruct;
import me.chanjar.weixin.common.bean.result.WxMediaUploadResult;
import me.chanjar.weixin.common.error.WxErrorException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.io.File;
import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * WeChat Mini App Configuration
 */
@Configuration
@EnableConfigurationProperties(WxMaProperties.class)
public class WxMaConfiguration {
    private static final Logger log = LoggerFactory.getLogger(WxMaConfiguration.class);

    private static final Map<String, WxMaMessageRouter> ROUTERS = new ConcurrentHashMap<>();
    private static final Map<String, WxMaService> MA_SERVICES = new ConcurrentHashMap<>();

    private final WxMaProperties properties;

    public WxMaConfiguration(WxMaProperties properties) {
        this.properties = properties;
    }

    /**
     * Get WeChat Mini App service by appid
     *
     * @param appid Mini App appid
     * @return WxMaService instance
     */
    public static WxMaService getMaService(String appid) {
        return Optional.ofNullable(MA_SERVICES.get(appid))
                .orElseThrow(() -> new IllegalArgumentException(
                        String.format("Configuration for appid=[%s] not found. Please verify your settings.", appid)));
    }

    /**
     * Get WeChat Mini App message router by appid
     *
     * @param appid Mini App appid
     * @return WxMaMessageRouter instance
     */
    public static WxMaMessageRouter getRouter(String appid) {
        return ROUTERS.get(appid);
    }

    @PostConstruct
    public void init() {
        List<WxMaProperties.Config> configs = Optional.ofNullable(this.properties.getConfigs())
                .orElseThrow(() -> new IllegalStateException(
                        "WeChat Mini App configuration is missing. Please check README file and add necessary configuration."));

        Map<String, WxMaService> serviceMap = configs.stream()
                .map(this::createMaService)
                .collect(Collectors.toMap(
                        service -> service.getWxMaConfig().getAppid(),
                        Function.identity()
                ));

        MA_SERVICES.putAll(serviceMap);
    }

    /**
     * Create WxMaService from config
     */
    private WxMaService createMaService(WxMaProperties.Config config) {
        WxMaDefaultConfigImpl wxConfig = new WxMaDefaultConfigImpl();
        wxConfig.setAppid(config.getAppid());
        wxConfig.setSecret(config.getSecret());
        wxConfig.setToken(config.getToken());
        wxConfig.setAesKey(config.getAesKey());
        wxConfig.setMsgDataFormat(config.getMsgDataFormat());

        WxMaService service = new WxMaServiceImpl();
        service.setWxMaConfig(wxConfig);

        // Create and store the router for this service
        WxMaMessageRouter router = createMessageRouter(service);
        ROUTERS.put(config.getAppid(), router);

        return service;
    }

    /**
     * Create message router with handlers
     */
    private WxMaMessageRouter createMessageRouter(WxMaService service) {
        final WxMaMessageRouter router = new WxMaMessageRouter(service);
        router
                .rule().handler(logHandler).next()
                .rule().async(false).content("模板").handler(templateMsgHandler).end()
                .rule().async(false).content("文本").handler(textHandler).end()
                .rule().async(false).content("图片").handler(picHandler).end()
                .rule().async(false).content("二维码").handler(qrcodeHandler).end();
        return router;
    }

    /**
     * Send message and handle exceptions
     */
    private static void trySendKefuMsg(WxMaService service, String fromUser, WxMaKefuMessage message) {
        try {
            service.getMsgService().sendKefuMsg(message);
        } catch (WxErrorException e) {
            log.error("Failed to send kefu message to user: {}", fromUser, e);
        }
    }

    private final WxMaMessageHandler templateMsgHandler = (wxMessage, context, service, sessionManager) -> {
        try {
            // In newer versions, use WxMaSubscribeMessage instead of WxMaTemplateMessage
            WxMaSubscribeMessage subscribeMessage = WxMaSubscribeMessage.builder()
                    .templateId("此处更换为自己的模板id")
                    .page("index")
                    .data(Lists.newArrayList(new MsgData("keyword1","339208499")))
                    .toUser(wxMessage.getFromUser())
                    .build();
            service.getMsgService().sendSubscribeMsg(subscribeMessage);
        } catch (WxErrorException e) {
            log.error("Failed to send template message", e);
        }
        return null;
    };

    private final WxMaMessageHandler logHandler = (wxMessage, context, service, sessionManager) -> {
        log.info("Received message: {}", wxMessage);
        WxMaKefuMessage message = WxMaKefuMessage.newTextBuilder()
                .content("收到信息为：" + wxMessage.toJson())
                .toUser(wxMessage.getFromUser())
                .build();
        trySendKefuMsg(service, wxMessage.getFromUser(), message);
        return null;
    };

    private final WxMaMessageHandler textHandler = (wxMessage, context, service, sessionManager) -> {
        WxMaKefuMessage message = WxMaKefuMessage.newTextBuilder()
                .content("回复文本消息")
                .toUser(wxMessage.getFromUser())
                .build();
        trySendKefuMsg(service, wxMessage.getFromUser(), message);
        return null;
    };

    private final WxMaMessageHandler picHandler = (wxMessage, context, service, sessionManager) -> {
        try {
            InputStream imageStream = ClassLoader.getSystemResourceAsStream("tmp.png");
            if (imageStream == null) {
                log.error("Image resource not found: tmp.png");
                return null;
            }

            WxMediaUploadResult uploadResult = service.getMediaService()
                    .uploadMedia("image", "png", imageStream);

            WxMaKefuMessage message = WxMaKefuMessage.newImageBuilder()
                    .mediaId(uploadResult.getMediaId())
                    .toUser(wxMessage.getFromUser())
                    .build();

            trySendKefuMsg(service, wxMessage.getFromUser(), message);
        } catch (WxErrorException e) {
            log.error("Failed to handle picture message", e);
        }
        return null;
    };

    private final WxMaMessageHandler qrcodeHandler = (wxMessage, context, service, sessionManager) -> {
        try {
            final File file = service.getQrcodeService().createQrcode("123", 430);
            WxMediaUploadResult uploadResult = service.getMediaService().uploadMedia("image", file);

            WxMaKefuMessage message = WxMaKefuMessage.newImageBuilder()
                    .mediaId(uploadResult.getMediaId())
                    .toUser(wxMessage.getFromUser())
                    .build();

            trySendKefuMsg(service, wxMessage.getFromUser(), message);
        } catch (WxErrorException e) {
            log.error("Failed to handle QR code message", e);
        }
        return null;
    };
}
