package com.logic.code.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CachingConfigurer;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.cache.interceptor.CacheErrorHandler;
import org.springframework.cache.interceptor.CacheResolver;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.cache.interceptor.SimpleCacheErrorHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * 缓存配置类
 * 
 * <AUTHOR>
 * @date 2025/8/4
 */
@Configuration
public class CacheConfig implements CachingConfigurer {

    /**
     * 配置缓存管理器
     */
    @Bean
    @Primary
    @Override
    public CacheManager cacheManager() {
        ConcurrentMapCacheManager cacheManager = new ConcurrentMapCacheManager();
        
        // 预定义缓存名称
        cacheManager.setCacheNames(Arrays.asList(
            "goodsPageInfo",     // 商品分页信息缓存
            "goodsDetail",       // 商品详情缓存
            "goodsCategory",     // 商品分类缓存
            "entityClass",       // 实体类缓存
            "homeIndex"          // 首页数据缓存
        ));
        
        // 允许动态创建缓存
        cacheManager.setAllowNullValues(false);
        
        return cacheManager;
    }

    /**
     * 自定义缓存键生成器
     */
    @Bean("goodsCacheKeyGenerator")
    @Override
    public KeyGenerator keyGenerator() {
        return new KeyGenerator() {
            @Override
            public Object generate(Object target, Method method, Object... params) {
                if (params.length == 0) {
                    return method.getName();
                }
                
                // 为商品查询方法生成优化的缓存键
                if ("queryGoodsPageInfo".equals(method.getName()) && params.length > 0) {
                    return generateGoodsPageCacheKey(params[0]);
                }
                
                // 默认键生成策略
                return method.getName() + "_" + 
                       Arrays.stream(params)
                             .map(param -> param != null ? param.toString() : "null")
                             .collect(Collectors.joining("_"));
            }
            
            /**
             * 为商品分页查询生成缓存键
             */
            private String generateGoodsPageCacheKey(Object searchQuery) {
                if (searchQuery == null) {
                    return "goods_page_default";
                }
                
                try {
                    // 使用反射获取查询参数
                    Class<?> clazz = searchQuery.getClass();
                    StringBuilder keyBuilder = new StringBuilder("goods_page");
                    
                    // 获取分类ID
                    Integer categoryId = (Integer) clazz.getMethod("getCategoryId").invoke(searchQuery);
                    keyBuilder.append("_cat").append(categoryId != null ? categoryId : 0);
                    
                    // 获取页码
                    int pageNum = (Integer) clazz.getMethod("getPageNum").invoke(searchQuery);
                    keyBuilder.append("_p").append(pageNum);
                    
                    // 获取品牌ID
                    Integer brandId = (Integer) clazz.getMethod("getBrandId").invoke(searchQuery);
                    if (brandId != null) {
                        keyBuilder.append("_b").append(brandId);
                    }
                    
                    // 获取关键词
                    String keyword = (String) clazz.getMethod("getKeyword").invoke(searchQuery);
                    if (keyword != null && !keyword.trim().isEmpty()) {
                        keyBuilder.append("_k").append(keyword.hashCode());
                    }
                    
                    // 获取新品标识
                    Boolean newly = (Boolean) clazz.getMethod("getNewly").invoke(searchQuery);
                    if (newly != null && newly) {
                        keyBuilder.append("_new");
                    }
                    
                    // 获取热门标识
                    Boolean hot = (Boolean) clazz.getMethod("getHot").invoke(searchQuery);
                    if (hot != null && hot) {
                        keyBuilder.append("_hot");
                    }
                    
                    return keyBuilder.toString();
                    
                } catch (Exception e) {
                    // 如果反射失败，使用默认策略
                    return "goods_page_" + searchQuery.toString().hashCode();
                }
            }
        };
    }

    /**
     * 缓存错误处理器
     */
    @Bean
    @Override
    public CacheErrorHandler errorHandler() {
        return new SimpleCacheErrorHandler() {
            @Override
            public void handleCacheGetError(RuntimeException exception, org.springframework.cache.Cache cache, Object key) {
                // 记录缓存获取错误，但不抛出异常
                System.err.println("Cache get error for key: " + key + ", cache: " + cache.getName() + ", error: " + exception.getMessage());
            }
            
            @Override
            public void handleCachePutError(RuntimeException exception, org.springframework.cache.Cache cache, Object key, Object value) {
                // 记录缓存存储错误，但不抛出异常
                System.err.println("Cache put error for key: " + key + ", cache: " + cache.getName() + ", error: " + exception.getMessage());
            }
            
            @Override
            public void handleCacheEvictError(RuntimeException exception, org.springframework.cache.Cache cache, Object key) {
                // 记录缓存清除错误，但不抛出异常
                System.err.println("Cache evict error for key: " + key + ", cache: " + cache.getName() + ", error: " + exception.getMessage());
            }
            
            @Override
            public void handleCacheClearError(RuntimeException exception, org.springframework.cache.Cache cache) {
                // 记录缓存清空错误，但不抛出异常
                System.err.println("Cache clear error for cache: " + cache.getName() + ", error: " + exception.getMessage());
            }
        };
    }

    /**
     * 缓存解析器（可选）
     */
    @Override
    public CacheResolver cacheResolver() {
        return null; // 使用默认解析器
    }
}
