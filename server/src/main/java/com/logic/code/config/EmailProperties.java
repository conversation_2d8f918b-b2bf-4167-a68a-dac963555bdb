package com.logic.code.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 邮件配置属性类
 *
 * <AUTHOR>
 * @date 2025/7/13
 */
@Data
@Component
@ConfigurationProperties(prefix = "app.email")
public class EmailProperties {

    /**
     * 默认发送方邮箱地址
     */
    private String defaultFrom = "<EMAIL>";

    /**
     * 默认发送方名称
     */
    private String defaultFromName = "伍俊惠选";

    /**
     * 默认收件人邮箱地址列表（用于系统通知）
     */
    private String[] defaultRecipients = {"<EMAIL>"};

    /**
     * 邮件模板配置
     */
    private Template template = new Template();

    /**
     * 附件配置
     */
    private Attachment attachment = new Attachment();

    @Data
    public static class Template {
        /**
         * 邮件模板路径
         */
        private String path = "classpath:/templates/email/";

        /**
         * 默认邮件模板
         */
        private String defaultTemplate = "default.html";
    }

    @Data
    public static class Attachment {
        /**
         * 附件最大大小（MB）
         */
        private int maxSize = 25;

        /**
         * 允许的附件类型
         */
        private String[] allowedTypes = {
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // .xlsx
            "application/vnd.ms-excel", // .xls
            "application/pdf", // .pdf
            "text/csv", // .csv
            "application/zip", // .zip
            "image/jpeg", // .jpg
            "image/png" // .png
        };
    }
}
