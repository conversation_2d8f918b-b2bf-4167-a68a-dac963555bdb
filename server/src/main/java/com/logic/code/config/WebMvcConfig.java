package com.logic.code.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.JsonGenerator;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.List;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Value("${app.upload.dir:./uploads}")
    private String uploadDir;

    @Value("${app.upload.url:/uploads}")
    private String uploadUrl;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // Create uploads directory if it doesn't exist
        File dir = new File(uploadDir);
        if (!dir.exists()) {
            dir.mkdirs();
        }

        // Register resource handler for uploads
        String uploadPath = "file:" + dir.getAbsolutePath() + File.separator;

        // Add resource handler for uploads with both paths
        registry.addResourceHandler("/uploads/**")
                .addResourceLocations(uploadPath);

        registry.addResourceHandler("/weshop-wjhx/uploads/**")
                .addResourceLocations(uploadPath);
    }

    /**
     * 配置HTTP消息转换器，解决中文乱码问题和JSON转义问题
     */
    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        // 配置字符串转换器，使用UTF-8编码
        StringHttpMessageConverter stringConverter = new StringHttpMessageConverter(StandardCharsets.UTF_8);
        stringConverter.setWriteAcceptCharset(false); // 避免在响应头中添加charset参数
        converters.add(stringConverter);

        // 配置JSON转换器，确保UTF-8编码并禁用不必要的转义
        MappingJackson2HttpMessageConverter jsonConverter = new MappingJackson2HttpMessageConverter();
        jsonConverter.setDefaultCharset(StandardCharsets.UTF_8);

        // 配置ObjectMapper，禁用不必要的转义
        ObjectMapper objectMapper = new ObjectMapper();
        // 禁用HTML字符转义，避免/被转义为\/
        objectMapper.getFactory().configure(JsonGenerator.Feature.ESCAPE_NON_ASCII, false);
        objectMapper.getFactory().configure(JsonGenerator.Feature.QUOTE_NON_NUMERIC_NUMBERS, false);

        jsonConverter.setObjectMapper(objectMapper);
        converters.add(jsonConverter);
    }
}
