package com.logic.code.service;

import com.logic.code.entity.system.MenuPermission;
import com.logic.code.mapper.CommonMapper;
import com.logic.code.mapper.MenuPermissionMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 菜单权限服务类
 * 
 * <AUTHOR>
 * @date 2025/7/7
 */
@Service
public class MenuPermissionService extends BaseService<MenuPermission> {

    @Resource
    private MenuPermissionMapper menuPermissionMapper;

    @Override
    protected CommonMapper<MenuPermission> getMapper() {
        return menuPermissionMapper;
    }

    /**
     * 根据菜单ID获取权限列表
     */
    public List<String> getPermissionsByMenuId(Integer menuId) {
        return menuPermissionMapper.selectPermissionsByMenuId(menuId);
    }

    /**
     * 获取所有权限信息
     */
    public List<MenuPermission> getAllPermissions() {
        return menuPermissionMapper.selectAllPermissions();
    }

    /**
     * 为菜单添加权限
     */
    public void addPermissionToMenu(Integer menuId, String permission) {
        MenuPermission menuPermission = MenuPermission.builder()
                .menuId(menuId)
                .permission(permission)
                .build();
        insert(menuPermission);
    }

    /**
     * 删除菜单的权限
     */
    public void removePermissionFromMenu(Integer menuId, String permission) {
        MenuPermission condition = MenuPermission.builder()
                .menuId(menuId)
                .permission(permission)
                .build();
        delete(condition);
    }
}
