package com.logic.code.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 积分数据一致性定时调度服务
 * 定期检查和修复用户积分数据不一致问题
 * 
 * <AUTHOR>
 * @date 2025/1/3
 */
@Service
@Slf4j
public class PointsConsistencyScheduleService {
    
    @Autowired
    private PointsService pointsService;
    
    /**
     * 是否启用积分一致性检查定时任务
     */
    @Value("${points.consistency.check.enabled:true}")
    private boolean consistencyCheckEnabled;
    
    /**
     * 是否启用自动修复功能
     */
    @Value("${points.consistency.auto-fix.enabled:false}")
    private boolean autoFixEnabled;
    
    /**
     * 积分一致性检查阈值，超过此数量的不一致用户才会触发告警
     */
    @Value("${points.consistency.alert.threshold:10}")
    private int alertThreshold;
    
    /**
     * 每天凌晨2点执行积分数据一致性检查
     * cron表达式：秒 分 时 日 月 周
     */
    @Scheduled(cron = "0 */2 * * * ?")
    public void dailyPointsConsistencyCheck() {
        if (!consistencyCheckEnabled) {
            log.debug("积分一致性检查已禁用，跳过定时任务");
            return;
        }
        
        try {
            log.info("开始执行每日积分数据一致性检查...");
            
            // 先进行一致性检查
            Map<String, Object> checkResult = pointsService.validateUserPointsConsistency();
            
            Integer inconsistentCount = (Integer) checkResult.get("inconsistentCount");
            Integer totalUsers = (Integer) checkResult.get("totalUsers");
            
            log.info("积分一致性检查完成：总用户数={}, 不一致用户数={}", totalUsers, inconsistentCount);
            
            // 如果发现不一致数据且超过阈值，记录告警
            if (inconsistentCount != null && inconsistentCount > alertThreshold) {
                log.warn("⚠️ 积分数据不一致告警：发现{}个用户积分数据不一致，超过告警阈值{}", 
                    inconsistentCount, alertThreshold);
                
                // 如果启用了自动修复，则执行修复
                if (autoFixEnabled) {
                    log.info("启用自动修复，开始修复积分数据不一致问题...");
                    Map<String, Object> fixResult = pointsService.fixUserPointsInconsistency(true);
                    
                    Integer fixedCount = (Integer) fixResult.get("fixedCount");
                    Integer failedCount = (Integer) fixResult.get("failedCount");
                    
                    log.info("自动修复完成：修复成功{}个，修复失败{}个", fixedCount, failedCount);
                    
                    if (failedCount != null && failedCount > 0) {
                        log.error("⚠️ 自动修复失败告警：{}个用户积分修复失败，需要人工处理", failedCount);
                    }
                } else {
                    log.warn("自动修复已禁用，请手动处理积分数据不一致问题");
                }
            } else if (inconsistentCount != null && inconsistentCount > 0) {
                log.info("发现{}个用户积分数据不一致，未超过告警阈值{}，记录日志", 
                    inconsistentCount, alertThreshold);
            } else {
                log.info("✓ 所有用户积分数据一致");
            }
            
        } catch (Exception e) {
            log.error("每日积分数据一致性检查失败", e);
        }
    }
    
    /**
     * 每小时执行一次轻量级积分一致性检查（仅统计，不修复）
     * 用于及时发现积分数据问题
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void hourlyPointsConsistencyMonitor() {
        if (!consistencyCheckEnabled) {
            return;
        }
        
        try {
            log.debug("开始执行每小时积分数据一致性监控...");
            
            Map<String, Object> checkResult = pointsService.validateUserPointsConsistency();
            Integer inconsistentCount = (Integer) checkResult.get("inconsistentCount");
            
            if (inconsistentCount != null && inconsistentCount > 0) {
                log.warn("积分数据监控：发现{}个用户积分数据不一致", inconsistentCount);
                
                // 如果不一致数量激增，立即告警
                if (inconsistentCount > alertThreshold * 2) {
                    log.error("🚨 积分数据严重不一致告警：发现{}个用户积分数据不一致，远超告警阈值，请立即处理！", 
                        inconsistentCount);
                }
            }
            
        } catch (Exception e) {
            log.error("每小时积分数据一致性监控失败", e);
        }
    }
    
    /**
     * 手动触发积分数据一致性检查
     * @param autoFix 是否自动修复
     * @return 检查结果
     */
    public Map<String, Object> manualPointsConsistencyCheck(boolean autoFix) {
        try {
            log.info("手动触发积分数据一致性检查，自动修复：{}", autoFix);
            
            if (autoFix) {
                return pointsService.fixUserPointsInconsistency(true);
            } else {
                return pointsService.validateUserPointsConsistency();
            }
            
        } catch (Exception e) {
            log.error("手动积分数据一致性检查失败", e);
            throw new RuntimeException("积分数据检查失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取积分一致性检查配置信息
     * @return 配置信息
     */
    public Map<String, Object> getConsistencyCheckConfig() {
        return Map.of(
            "consistencyCheckEnabled", consistencyCheckEnabled,
            "autoFixEnabled", autoFixEnabled,
            "alertThreshold", alertThreshold,
            "dailyCheckCron", "0 0 2 * * ?",
            "hourlyMonitorCron", "0 0 * * * ?"
        );
    }
}
