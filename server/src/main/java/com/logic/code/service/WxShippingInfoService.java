package com.logic.code.service;

import com.logic.code.entity.order.WxShippingInfo;
import com.logic.code.mapper.CommonMapper;
import com.logic.code.mapper.WxShippingInfoMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * 微信发货信息管理Service
 */
@Service
public class WxShippingInfoService extends BaseService<WxShippingInfo> {

    @Resource
    private WxShippingInfoMapper wxShippingInfoMapper;

    @Override
    protected CommonMapper<WxShippingInfo> getMapper() {
        return wxShippingInfoMapper;
    }

    /**
     * 根据订单ID查询微信发货信息
     */
    public WxShippingInfo queryByOrderId(Integer orderId) {
        return queryOne(WxShippingInfo.builder().orderId(orderId).build());
    }

    /**
     * 根据微信支付单号查询发货信息
     */
    public WxShippingInfo queryByTransactionId(String transactionId) {
        return queryOne(WxShippingInfo.builder().transactionId(transactionId).build());
    }

    /**
     * 根据商户订单号查询发货信息
     */
    public WxShippingInfo queryByOutTradeNo(String mchid, String outTradeNo) {
        return queryOne(WxShippingInfo.builder()
                .mchid(mchid)
                .outTradeNo(outTradeNo)
                .build());
    }
}
