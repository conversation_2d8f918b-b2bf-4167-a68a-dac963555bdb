package com.logic.code.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logic.code.entity.goods.ParamTemplate;
import com.logic.code.mapper.CommonMapper;
import com.logic.code.mapper.ParamTemplateMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/5
 * @desc 商品参数模板服务类
 */
@Service
public class ParamTemplateService extends BaseService<ParamTemplate> {

    @Resource
    private ParamTemplateMapper paramTemplateMapper;

    @Override
    protected CommonMapper<ParamTemplate> getMapper() {
        return paramTemplateMapper;
    }

    /**
     * 分页查询参数模板
     */
    public Page<ParamTemplate> getPage(String name, Integer page, Integer limit) {
        Page<ParamTemplate> pageInfo = new Page<>(page, limit);
        return paramTemplateMapper.selectPage(pageInfo, name);
    }

    /**
     * 查询所有启用的参数模板
     */
    public List<ParamTemplate> queryAllEnabled() {
        return paramTemplateMapper.selectAllEnabled();
    }

    /**
     * 保存参数模板
     */
    public ParamTemplate saveTemplate(ParamTemplate template) {
        if (template.getId() != null && template.getId() > 0) {
            // 更新
            template.setUpdateTime(new Date());
            updateById(template);
        } else {
            // 新增
            template.setCreateTime(new Date());
            template.setUpdateTime(new Date());
            template.setStatus(1); // 默认启用
            create(template);
        }
        return template;
    }
}
