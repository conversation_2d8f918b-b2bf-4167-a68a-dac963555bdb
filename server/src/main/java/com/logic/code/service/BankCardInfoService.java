package com.logic.code.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.logic.code.entity.BankCardInfo;
import com.logic.code.mapper.BankCardInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 银行卡信息服务类
 * <AUTHOR>
 * @date 2025-08-26
 */
@Slf4j
@Service
public class BankCardInfoService extends ServiceImpl<BankCardInfoMapper, BankCardInfo> {

    /**
     * 根据用户ID获取银行卡列表
     * @param userId 用户ID
     * @return 银行卡列表
     */
    public List<BankCardInfo> getBankCardsByUserId(Integer userId) {
        return baseMapper.selectByUserId(userId);
    }

    /**
     * 根据用户ID获取默认银行卡
     * @param userId 用户ID
     * @return 默认银行卡信息
     */
    public BankCardInfo getDefaultBankCard(Integer userId) {
        return baseMapper.selectDefaultByUserId(userId);
    }

    /**
     * 保存银行卡信息
     * @param userId 用户ID
     * @param bankInfo 银行卡信息Map
     * @return 保存的银行卡信息
     */
    @Transactional
    public BankCardInfo saveBankCardInfo(Integer userId, Map<String, Object> bankInfo) {
        try {
            String cardNumber = (String) bankInfo.get("cardNumber");
            
            // 检查是否已经存在相同的银行卡
            BankCardInfo existingCard = baseMapper.selectByUserIdAndCardNumber(userId, cardNumber);
            if (existingCard != null) {
                // 如果已存在，更新信息并返回
                existingCard.setBankName((String) bankInfo.get("bankName"));
                existingCard.setCardHolder((String) bankInfo.get("cardHolder"));
                existingCard.setBankAddress((String) bankInfo.get("bankAddress"));
                existingCard.setCardImageUrl((String) bankInfo.get("cardImage"));
                existingCard.setUpdateTime(new Date());
                
                baseMapper.updateById(existingCard);
                return existingCard;
            }
            
            // 创建新的银行卡信息
            BankCardInfo cardInfo = new BankCardInfo();
            cardInfo.setUserId(userId);
            cardInfo.setBankName((String) bankInfo.get("bankName"));
            cardInfo.setCardNumber(cardNumber);
            cardInfo.setCardHolder((String) bankInfo.get("cardHolder"));
            cardInfo.setBankAddress((String) bankInfo.get("bankAddress"));
            cardInfo.setCardImageUrl((String) bankInfo.get("cardImage"));
            
            // 检查用户是否有其他银行卡，如果没有则设为默认
            List<BankCardInfo> userCards = baseMapper.selectByUserId(userId);
            cardInfo.setIsDefault(userCards.isEmpty());
            
            cardInfo.setStatus(1); // 启用状态
            cardInfo.setCreateTime(new Date());
            cardInfo.setUpdateTime(new Date());
            
            int result = baseMapper.insert(cardInfo);
            return result > 0 ? cardInfo : null;
        } catch (Exception e) {
            log.error("保存银行卡信息失败: {}", e.getMessage(), e);
            throw new RuntimeException("保存银行卡信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 设置默认银行卡
     * @param userId 用户ID
     * @param cardId 银行卡ID
     * @return 是否成功
     */
    @Transactional
    public boolean setDefaultBankCard(Integer userId, Integer cardId) {
        try {
            // 清除其他默认标记
            baseMapper.clearOtherDefaultCards(userId, cardId);
            // 设置新的默认银行卡
            int result = baseMapper.setAsDefault(cardId, userId);
            return result > 0;
        } catch (Exception e) {
            log.error("设置默认银行卡失败: {}", e.getMessage(), e);
            throw new RuntimeException("设置默认银行卡失败: " + e.getMessage(), e);
        }
    }

    /**
     * 删除银行卡（软删除）
     * @param userId 用户ID
     * @param cardId 银行卡ID
     * @return 是否成功
     */
    public boolean deleteBankCard(Integer userId, Integer cardId) {
        try {
            int result = baseMapper.softDeleteById(cardId, userId);
            return result > 0;
        } catch (Exception e) {
            log.error("删除银行卡失败: {}", e.getMessage(), e);
            throw new RuntimeException("删除银行卡失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证银行卡信息完整性
     * @param bankInfo 银行卡信息
     * @return 验证结果
     */
    public void validateBankCardInfo(Map<String, Object> bankInfo) {
        if (bankInfo == null) {
            throw new IllegalArgumentException("银行卡信息不能为空");
        }
        
        String bankName = (String) bankInfo.get("bankName");
        String cardNumber = (String) bankInfo.get("cardNumber");
        String cardHolder = (String) bankInfo.get("cardHolder");
        String bankAddress = (String) bankInfo.get("bankAddress");
        String cardImage = (String) bankInfo.get("cardImage");
        
        if (bankName == null || bankName.trim().isEmpty()) {
            throw new IllegalArgumentException("开户行名称不能为空");
        }
        
        if (cardNumber == null || cardNumber.trim().isEmpty()) {
            throw new IllegalArgumentException("银行卡号不能为空");
        }
        
        // 验证银行卡号格式（简单验证：16-19位数字）
        String cleanCardNumber = cardNumber.replaceAll("\\s+", "");
        if (!cleanCardNumber.matches("\\d{16,19}")) {
            throw new IllegalArgumentException("银行卡号格式不正确");
        }
        
        if (cardHolder == null || cardHolder.trim().isEmpty()) {
            throw new IllegalArgumentException("持卡人姓名不能为空");
        }
        
        if (bankAddress == null || bankAddress.trim().isEmpty()) {
            throw new IllegalArgumentException("开户行地址不能为空");
        }
        
        if (cardImage == null || cardImage.trim().isEmpty()) {
            throw new IllegalArgumentException("银行卡照片不能为空");
        }
    }
}