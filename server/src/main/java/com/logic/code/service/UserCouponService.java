package com.logic.code.service;

import com.logic.code.entity.UserCoupon;

import java.util.List;
import java.util.Map;

/**
 * 用户优惠券服务接口
 * <AUTHOR>
 * @date 2025/7/25
 */
public interface UserCouponService {
    
    /**
     * 获取用户优惠券统计
     * @param userId 用户ID
     * @return 统计信息
     */
    Map<String, Object> getCouponStats(Integer userId);
    
    /**
     * 获取用户优惠券列表
     * @param userId 用户ID
     * @param status 状态筛选
     * @param page 页码
     * @param size 每页大小
     * @return 优惠券列表
     */
    List<UserCoupon> getUserCoupons(Integer userId, String status, Integer page, Integer size);
    
    /**
     * 使用优惠券
     * @param userId 用户ID
     * @param couponId 优惠券ID
     * @param orderId 订单ID
     * @return 是否成功
     */
    boolean useCoupon(Integer userId, Integer couponId, Integer orderId);
    
    /**
     * 为用户添加优惠券
     * @param userId 用户ID
     * @param couponTemplateId 优惠券模板ID
     * @param count 数量
     * @return 是否成功
     */
    boolean addCouponsToUser(Integer userId, Integer couponTemplateId, Integer count);

    /**
     * 退回优惠券（订单取消时调用）
     * @param orderId 订单ID
     * @return 是否成功
     */
    boolean refundCoupon(Integer orderId);
}