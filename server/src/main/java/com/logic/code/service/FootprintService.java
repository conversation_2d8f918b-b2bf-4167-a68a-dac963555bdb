package com.logic.code.service;

import com.logic.code.common.utils.JwtHelper;
import com.logic.code.entity.Collect;
import com.logic.code.entity.Footprint;
import com.logic.code.entity.User;
import com.logic.code.mapper.CommonMapper;
import com.logic.code.mapper.FootprintMapper;
import com.logic.code.model.dto.GoodsFootprintDTO;
import jakarta.annotation.Resource;
import logic.orm.WrapperBuilder;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/8 11:00
 * @desc
 */
@Service
public class FootprintService extends BaseService<Footprint> {

    @Resource
    private FootprintMapper footprintMapper;

    @Override
    protected CommonMapper<Footprint> getMapper() {
        return footprintMapper;
    }

    public List<GoodsFootprintDTO> queryGoodsFootprintByUserId(Integer userId) {
        return footprintMapper.selectGoodsFootprintByUserId(userId);
    }

    public long count() {
        return footprintMapper.selectCount(WrapperBuilder.autoWhere(Footprint.builder().userId(JwtHelper.getUserInfo().getId()).build()));
    }

    public List<List<GoodsFootprintDTO>> queryGoodsFootprintTimeLine() {
        User userInfo = JwtHelper.getUserInfo();
        List<GoodsFootprintDTO> goodsFootprintList = queryGoodsFootprintByUserId(userInfo.getId());

        return goodsFootprintList.stream()
                .collect(Collectors.groupingBy(gf -> gf.getCreateTime()))
                .entrySet()
                .stream()
                .sorted((e1, e2) -> {
                    Long d1 = e1.getKey().toEpochDay();
                    Long d2 = e2.getKey().toEpochDay();
                    return d2.compareTo(d1);
                }).map(Map.Entry::getValue)
                .collect(Collectors.toList());
    }
}
