package com.logic.code.service;

import com.logic.code.entity.SearchHistory;
import com.logic.code.mapper.CommonMapper;
import com.logic.code.mapper.SearchHistoryMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/5/8 9:48
 * @desc
 */
@Service
public class SearchHistoryService extends BaseService<SearchHistory> {

    @Resource
    private SearchHistoryMapper searchHistoryMapper;

    @Override
    protected CommonMapper<SearchHistory> getMapper() {
        return searchHistoryMapper;
    }


}
