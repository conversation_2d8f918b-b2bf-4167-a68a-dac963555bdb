package com.logic.code.service;

import com.logic.code.common.response.Result;
import com.logic.code.entity.Comment;
import com.logic.code.entity.CommentPicture;
import com.logic.code.entity.User;
import com.logic.code.mapper.CommentMapper;
import com.logic.code.mapper.CommentPictureMapper;
import com.logic.code.mapper.CommonMapper;
import com.logic.code.model.query.CommentQuery;
import com.logic.code.model.vo.CommentCountVO;
import com.logic.code.model.vo.CommentResultVO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/8 10:06
 * @desc
 */
@Service
public class CommentService extends BaseService<Comment> {

    @Resource
    private CommentMapper commentMapper;

    @Resource
    private UserService userService;

    @Resource
    private CommentPictureService commentPictureService;

    @Override
    protected CommonMapper<Comment> getMapper() {
        return commentMapper;
    }

    public List<Comment> queryIfRequirePictureList(CommentQuery commentQuery) {
        int limit = commentQuery.getPageSize();
        int offset = (commentQuery.getPageNum() - 1) * limit;
        return commentMapper.selectIfRequirePictureList(commentQuery, offset, limit);
    }

    public Integer countIfRequirePictureList(CommentQuery commentQuery) {
        return commentMapper.countIfRequirePictureList(commentQuery);
    }


    public List<CommentResultVO> queryList(CommentQuery commentQuery) {
        List<CommentResultVO> commentResultList = queryIfRequirePictureList(commentQuery).stream()
                .map(CommentResultVO::new)
                .collect(Collectors.toList());
        for (CommentResultVO commentResultDTO : commentResultList) {
            commentResultDTO.setPicList(commentPictureService.queryPicUrlByCommentId(commentResultDTO.getId()));
            User user = Optional.ofNullable(userService.queryById(commentResultDTO.getUserId())).orElseGet(() -> new User());
            commentResultDTO.setUserInfo(new CommentResultVO.UserInfoVO(user));
        }
        return commentResultList;
    }

    public CommentCountVO countList(CommentQuery commentQuery) {
        // 使用新的统计查询方法
        Map<String, Object> stats = commentMapper.selectCommentStats(commentQuery);

        CommentCountVO result = new CommentCountVO();
        result.setAllCount(((Number) stats.getOrDefault("allCount", 0)).longValue());
        result.setHasPicCount(((Number) stats.getOrDefault("hasPicCount", 0)).longValue());
        result.setGoodCount(((Number) stats.getOrDefault("goodCount", 0)).longValue());
        result.setNormalCount(((Number) stats.getOrDefault("normalCount", 0)).longValue());
        result.setBadCount(((Number) stats.getOrDefault("badCount", 0)).longValue());

        // 处理平均评分
        Object avgRating = stats.get("averageRating");
        if (avgRating != null) {
            result.setAverageRating(new BigDecimal(avgRating.toString()));
        } else {
            result.setAverageRating(null); // 无评论时返回null
        }

        // 处理好评率
        Object satisfaction = stats.get("satisfactionRate");
        if (satisfaction != null) {
            result.setSatisfactionRate(new BigDecimal(satisfaction.toString()));
        } else {
            result.setSatisfactionRate(new BigDecimal("100.00")); // 默认100%
        }

        return result;
    }

    /**
     * 创建评论并保存图片
     */
    @Transactional
    public Comment createWithPictures(Comment comment, List<String> picUrls) {
        // 保存评论
        create(comment);

        // 保存图片
        if (picUrls != null && !picUrls.isEmpty()) {
            for (int i = 0; i < picUrls.size(); i++) {
                CommentPicture commentPicture = new CommentPicture()
                        .setCommentId(comment.getId())
                        .setPicUrl(picUrls.get(i))
                        .setSortOrder(i < 9); // 前9张图片设置为true
                commentPictureService.create(commentPicture);
            }
        }

        return comment;
    }

    /**
     * 检查用户是否已经评价过指定商品
     */
    public boolean hasUserCommented(Integer userId, Integer valueId, Integer typeId) {
        Integer count = commentMapper.checkUserCommentExists(userId, valueId, typeId);
        return count != null && count > 0;
    }

}
