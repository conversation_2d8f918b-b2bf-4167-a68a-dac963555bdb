package com.logic.code.service;

import com.logic.code.entity.goods.ProductLabel;
import com.logic.code.mapper.CommonMapper;
import com.logic.code.mapper.ProductLabelMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 商品标签服务
 */
@Service
public class ProductLabelService extends BaseService<ProductLabel> {

    @Resource
    private ProductLabelMapper productLabelMapper;

    @Override
    protected CommonMapper<ProductLabel> getMapper() {
        return productLabelMapper;
    }

    /**
     * 分页查询标签
     */
    public Map<String, Object> queryPage(Map<String, Object> params) {
        Integer page = (Integer) params.get("page");
        Integer limit = (Integer) params.get("limit");
        String name = (String) params.get("name");
        Integer cateId = (Integer) params.get("cate_id");
        Integer status = (Integer) params.get("status");
        Integer isShow = (Integer) params.get("is_show");

        if (page == null || page < 1) page = 1;
        if (limit == null || limit < 1) limit = 10;

        Integer offset = (page - 1) * limit;

        List<Map<String, Object>> list = productLabelMapper.selectPageList(name, cateId, status, isShow, offset, limit);
        Long total = productLabelMapper.selectPageCount(name, cateId, status, isShow);

        Map<String, Object> result = new HashMap<>();
        result.put("list", list);
        result.put("count", total);
        result.put("page", page);
        result.put("limit", limit);

        return result;
    }

    /**
     * 获取所有可用的标签
     */
    public List<ProductLabel> getAvailableList() {
        return productLabelMapper.selectAvailableList();
    }

    /**
     * 根据分类ID查询标签
     */
    public List<ProductLabel> getByCategoryId(Integer cateId) {
        return productLabelMapper.selectByCategoryId(cateId);
    }

    /**
     * 保存标签
     */
    public ProductLabel saveLabel(ProductLabel label) {
        if (label.getId() != null && label.getId() > 0) {
            // 更新
            updateById(label);
        } else {
            // 新增
            if (label.getSortOrder() == null) {
                label.setSortOrder(0);
            }
            if (label.getStatus() == null) {
                label.setStatus(1);
            }
            if (label.getIsShow() == null) {
                label.setIsShow(1);
            }
            if (label.getType() == null) {
                label.setType(1);
            }
            insert(label);
        }
        return label;
    }

    /**
     * 更新状态
     */
    public boolean updateStatus(Integer id, Integer status) {
        ProductLabel label = queryById(id);
        if (label != null) {
            label.setStatus(status);
            return updateById(label) > 0;
        }
        return false;
    }

    /**
     * 更新显示状态
     */
    public boolean updateIsShow(Integer id, Integer isShow) {
        ProductLabel label = queryById(id);
        if (label != null) {
            label.setIsShow(isShow);
            return updateById(label) > 0;
        }
        return false;
    }
}
