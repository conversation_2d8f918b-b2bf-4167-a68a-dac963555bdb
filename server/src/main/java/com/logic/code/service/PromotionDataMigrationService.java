package com.logic.code.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.logic.code.entity.PromotionEarnings;
import com.logic.code.entity.User;
import com.logic.code.entity.order.Order;
import com.logic.code.mapper.OrderMapper;
import com.logic.code.mapper.PromotionEarningsMapper;
import com.logic.code.mapper.UserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 推广数据迁移服务
 * 用于将历史订单数据迁移到推广收益表中
 */
@Slf4j
@Service
public class PromotionDataMigrationService {

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private PromotionEarningsMapper promotionEarningsMapper;

    /**
     * 执行推广数据迁移
     * @return 迁移结果统计
     */
    @Transactional
    public Map<String, Object> migratePromotionData() {
        log.info("开始执行推广数据迁移...");
        
        Map<String, Object> result = new HashMap<>();
        int totalOrders = 0;
        int migratedOrders = 0;
        int skippedOrders = 0;
        int errorOrders = 0;

        try {
            // 1. 查询所有有推广关系的用户
            QueryWrapper<User> userWrapper = new QueryWrapper<>();
            userWrapper.isNotNull("promoter_id");
            List<User> promotedUsers = userMapper.selectList(userWrapper);
            
            log.info("找到 {} 个被推广用户", promotedUsers.size());

            // 2. 为每个被推广用户查询其订单
            for (User promotedUser : promotedUsers) {
                Integer promoterId = promotedUser.getPromoterId();
                Integer promotedUserId = promotedUser.getId();

                // 查询该用户的所有订单
                QueryWrapper<Order> orderWrapper = new QueryWrapper<>();
                orderWrapper.eq("user_id", promotedUserId);
                List<Order> orders = orderMapper.selectList(orderWrapper);

                totalOrders += orders.size();
                log.info("用户 {} 共有 {} 个订单", promotedUserId, orders.size());

                // 3. 为每个订单创建推广收益记录
                for (Order order : orders) {
                    try {
                        // 检查是否已经存在推广收益记录
                        QueryWrapper<PromotionEarnings> existsWrapper = new QueryWrapper<>();
                        existsWrapper.eq("order_id", order.getId());
                        PromotionEarnings existingEarnings = promotionEarningsMapper.selectOne(existsWrapper);
                        
                        if (existingEarnings != null) {
                            log.debug("订单 {} 的推广收益记录已存在，跳过", order.getId());
                            skippedOrders++;
                            continue;
                        }

                        // 创建推广收益记录
                        PromotionEarnings earnings = createEarningsRecord(promoterId, promotedUserId, order);
                        promotionEarningsMapper.insert(earnings);
                        
                        migratedOrders++;
                        log.debug("为订单 {} 创建推广收益记录成功", order.getId());

                    } catch (Exception e) {
                        log.error("为订单 {} 创建推广收益记录失败: {}", order.getId(), e.getMessage());
                        errorOrders++;
                    }
                }
            }

            result.put("success", true);
            result.put("message", "推广数据迁移完成");
            
        } catch (Exception e) {
            log.error("推广数据迁移失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "推广数据迁移失败: " + e.getMessage());
        }

        result.put("totalOrders", totalOrders);
        result.put("migratedOrders", migratedOrders);
        result.put("skippedOrders", skippedOrders);
        result.put("errorOrders", errorOrders);

        log.info("推广数据迁移完成 - 总订单数: {}, 迁移成功: {}, 跳过: {}, 错误: {}", 
                totalOrders, migratedOrders, skippedOrders, errorOrders);

        return result;
    }

    /**
     * 创建推广收益记录
     */
    private PromotionEarnings createEarningsRecord(Integer promoterId, Integer promotedUserId, Order order) {
        PromotionEarnings earnings = new PromotionEarnings();
        
        earnings.setPromoterId(promoterId);
        earnings.setPromotedUserId(promotedUserId);
        earnings.setOrderId(order.getId());
        earnings.setOrderNo(order.getOrderSn());
        
        // 使用订单的实际支付金额作为订单金额
        BigDecimal orderAmount = order.getActualPrice() != null ? order.getActualPrice() : BigDecimal.ZERO;
        earnings.setOrderAmount(orderAmount);
        
        // 设置佣金比例为10%
        BigDecimal commissionRate = new BigDecimal("10.00");
        earnings.setCommissionRate(commissionRate);
        
        // 计算佣金金额
        BigDecimal commissionAmount = orderAmount.multiply(commissionRate.divide(new BigDecimal("100")));
        earnings.setCommissionAmount(commissionAmount);
        
        // 根据订单状态设置收益状态
        String status = determineEarningsStatus(order);
        earnings.setStatus(status);
        
        // 设置时间信息
        earnings.setOrderCreateTime(order.getCreateTime());
        earnings.setCreateTime(new Date());
        
        // 如果订单已确认收货，设置确认时间和生效时间
        if ("confirmed".equals(status) && order.getConfirmReceiveTime() != null) {
            earnings.setConfirmTime(order.getConfirmReceiveTime());
            earnings.setEffectiveTime(order.getConfirmReceiveTime());
        }
        
        // 设置描述信息
        earnings.setDescription(generateDescription(order, status));
        
        return earnings;
    }

    /**
     * 根据订单状态确定收益状态
     */
    private String determineEarningsStatus(Order order) {
        // 如果订单已确认收货，收益状态为已确认
        if (order.getConfirmReceiveTime() != null) {
            return "confirmed";
        }
        
        // 如果订单已发货但未确认收货，收益状态为待确认
        if (order.getShippingTime() != null) {
            return "pending";
        }
        
        // 如果订单已支付但未发货，收益状态为待确认
        if (order.getPayTime() != null) {
            return "pending";
        }
        
        // 其他情况默认为待确认
        return "pending";
    }

    /**
     * 生成描述信息
     */
    private String generateDescription(Order order, String status) {
        if ("confirmed".equals(status)) {
            return "历史订单迁移 - 用户已确认收货，佣金生效";
        } else {
            return "历史订单迁移 - 推广用户下单，待确认收货";
        }
    }

    /**
     * 清空推广收益表数据（谨慎使用）
     */
    @Transactional
    public Map<String, Object> clearPromotionEarnings() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            int deletedCount = promotionEarningsMapper.delete(new QueryWrapper<>());
            
            result.put("success", true);
            result.put("message", "推广收益数据清空完成");
            result.put("deletedCount", deletedCount);
            
            log.info("推广收益数据清空完成，删除记录数: {}", deletedCount);
            
        } catch (Exception e) {
            log.error("清空推广收益数据失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "清空推广收益数据失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取迁移统计信息
     */
    public Map<String, Object> getMigrationStats() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            // 统计推广收益记录数
            long earningsCount = promotionEarningsMapper.selectCount(new QueryWrapper<>());

            // 统计有推广关系的用户数
            QueryWrapper<User> userWrapper = new QueryWrapper<>();
            userWrapper.isNotNull("promoter_id");
            long promotedUserCount = userMapper.selectCount(userWrapper);

            // 统计订单总数
            long totalOrderCount = orderMapper.selectCount(new QueryWrapper<>());
            
            stats.put("earningsRecordCount", earningsCount);
            stats.put("promotedUserCount", promotedUserCount);
            stats.put("totalOrderCount", totalOrderCount);
            stats.put("migrationRate", totalOrderCount > 0 ? 
                String.format("%.2f%%", (double) earningsCount / totalOrderCount * 100) : "0%");
            
        } catch (Exception e) {
            log.error("获取迁移统计信息失败: {}", e.getMessage(), e);
            stats.put("error", e.getMessage());
        }
        
        return stats;
    }
}
