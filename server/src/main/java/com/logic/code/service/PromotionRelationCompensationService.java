package com.logic.code.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.logic.code.common.enmus.PayStatusEnum;
import com.logic.code.entity.User;
import com.logic.code.entity.order.Order;
import com.logic.code.mapper.OrderMapper;
import com.logic.code.mapper.UserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 推广关系补偿服务
 * 用于修复和补偿缺失的推广关系
 */
@Service
@Slf4j
public class PromotionRelationCompensationService {
    
    @Autowired
    private OrderMapper orderMapper;
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private PromotionCommissionService promotionCommissionService;
    
    /**
     * 补偿单个订单的推广者设置
     * @param orderId 订单ID
     * @return 是否补偿成功
     */
    @Transactional
    public boolean compensateOrderPromoter(Integer orderId) {
        try {
            log.info("开始补偿订单{}的推广者设置", orderId);
            
            // 获取订单信息
            Order order = orderMapper.selectById(orderId);
            if (order == null) {
                log.warn("订单不存在：{}", orderId);
                return false;
            }
            
            // 如果订单已有推广者，无需补偿
            if (order.getPromoterId() != null) {
                log.info("订单{}已有推广者{}，无需补偿", orderId, order.getPromoterId());
                return true;
            }
            
            // 获取下单用户信息
            User user = userMapper.selectById(order.getUserId());
            if (user == null) {
                log.warn("用户不存在：{}", order.getUserId());
                return false;
            }
            
            // 如果用户没有推广者，无法补偿
            if (user.getPromoterId() == null) {
                log.info("用户{}没有推广者，无法补偿订单{}", user.getId(), orderId);
                return false;
            }
            
            // 验证推广者是否存在
            User promoter = userMapper.selectById(user.getPromoterId());
            if (promoter == null) {
                log.warn("推广者{}不存在，无法补偿订单{}", user.getPromoterId(), orderId);
                return false;
            }
            
            // 检查是否自己推广自己
            if (user.getPromoterId().equals(user.getId())) {
                log.warn("用户{}不能推广自己，跳过补偿订单{}", user.getId(), orderId);
                return false;
            }
            
            // 设置推广者ID
            order.setPromoterId(user.getPromoterId());

            int updateResult = orderMapper.updateById(order);
            
            if (updateResult > 0) {
                log.info("订单{}推广者补偿成功：推广者ID={}", order.getOrderSn(), user.getPromoterId());
                return true;
            } else {
                log.error("订单{}推广者补偿失败：数据库更新失败", order.getOrderSn());
                return false;
            }
            
        } catch (Exception e) {
            log.error("补偿订单{}推广者设置失败", orderId, e);
            return false;
        }
    }
    
    /**
     * 批量补偿缺失推广者的订单
     * @param days 补偿最近几天的订单
     * @return 补偿结果统计
     */
    @Transactional
    public Map<String, Object> batchCompensateOrderPromoters(Integer days) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("开始批量补偿最近{}天缺失推广者的订单", days);
            
            // 查找需要补偿的订单
            String sql = """
                SELECT o.id 
                FROM weshop_order o
                JOIN weshop_user u ON o.user_id = u.id
                WHERE o.promoter_id IS NULL 
                AND u.promoter_id IS NOT NULL
                AND o.create_time >= DATE_SUB(NOW(), INTERVAL ? DAY)
                ORDER BY o.create_time DESC
                """;
            
            // 这里需要使用MyBatis-Plus的原生SQL查询
            // 简化实现，直接查询订单列表
            QueryWrapper<Order> wrapper = new QueryWrapper<>();
            wrapper.isNull("promoter_id");
            wrapper.ge("create_time", new Date(System.currentTimeMillis() - days * 24 * 60 * 60 * 1000L));
            wrapper.orderByDesc("create_time");
            
            List<Order> ordersToCompensate = orderMapper.selectList(wrapper);
            
            int totalOrders = ordersToCompensate.size();
            int successCount = 0;
            int failCount = 0;
            
            log.info("找到{}个需要补偿的订单", totalOrders);
            
            for (Order order : ordersToCompensate) {
                // 检查用户是否有推广者
                User user = userMapper.selectById(order.getUserId());
                if (user != null && user.getPromoterId() != null) {
                    boolean success = compensateOrderPromoter(order.getId());
                    if (success) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                } else {
                    failCount++;
                }
                
                // 避免一次性处理太多数据，每100个订单休息一下
                if ((successCount + failCount) % 100 == 0) {
                    try {
                        Thread.sleep(100); // 休息100ms
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
            
            result.put("totalOrders", totalOrders);
            result.put("successCount", successCount);
            result.put("failCount", failCount);
            result.put("successRate", totalOrders > 0 ? (double) successCount / totalOrders * 100 : 0);
            result.put("compensateTime", new Date());
            
            log.info("批量补偿完成：总计{}个订单，成功{}个，失败{}个", totalOrders, successCount, failCount);
            
        } catch (Exception e) {
            log.error("批量补偿订单推广者失败", e);
            result.put("error", e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 补偿并重新计算推广佣金
     * @param orderId 订单ID
     * @return 是否成功
     */
    @Transactional
    public boolean compensateAndRecalculateCommission(Integer orderId) {
        try {
            log.info("开始补偿订单{}并重新计算推广佣金", orderId);
            
            // 1. 先补偿推广者设置
            boolean compensated = compensateOrderPromoter(orderId);
            if (!compensated) {
                log.warn("订单{}推广者补偿失败，跳过佣金计算", orderId);
                return false;
            }
            
            // 2. 检查订单是否已支付
            Order order = orderMapper.selectById(orderId);
            if (order == null || order.getPayStatus() != PayStatusEnum.PAID) {
                log.info("订单{}未支付，跳过佣金计算", orderId);
                return true; // 补偿成功，但不需要计算佣金
            }
            
            // 3. 重新计算推广佣金
            boolean commissionCalculated = promotionCommissionService.processPromotionCommissionAfterPayment(orderId);
            if (commissionCalculated) {
                log.info("订单{}推广佣金重新计算成功", orderId);
                return true;
            } else {
                log.warn("订单{}推广佣金重新计算失败", orderId);
                return false;
            }
            
        } catch (Exception e) {
            log.error("补偿订单{}并重新计算推广佣金失败", orderId, e);
            return false;
        }
    }
    
    /**
     * 检查推广关系一致性
     * @param days 检查最近几天的数据
     * @return 检查结果
     */
    public Map<String, Object> checkPromotionRelationConsistency(Integer days) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("开始检查最近{}天的推广关系一致性", days);
            
            // 统计订单推广关系情况
            QueryWrapper<Order> orderWrapper = new QueryWrapper<>();
            orderWrapper.ge("create_time", new Date(System.currentTimeMillis() - days * 24 * 60 * 60 * 1000L));
            
            List<Order> recentOrders = orderMapper.selectList(orderWrapper);
            
            int totalOrders = recentOrders.size();
            int ordersWithPromoter = 0;
            int usersWithPromoter = 0;
            int missingPromoterOrders = 0;
            
            for (Order order : recentOrders) {
                if (order.getPromoterId() != null) {
                    ordersWithPromoter++;
                }
                
                // 检查用户是否有推广者
                User user = userMapper.selectById(order.getUserId());
                if (user != null && user.getPromoterId() != null) {
                    usersWithPromoter++;
                    
                    // 如果用户有推广者但订单没有，说明缺失
                    if (order.getPromoterId() == null) {
                        missingPromoterOrders++;
                    }
                }
            }
            
            result.put("checkDays", days);
            result.put("totalOrders", totalOrders);
            result.put("ordersWithPromoter", ordersWithPromoter);
            result.put("usersWithPromoter", usersWithPromoter);
            result.put("missingPromoterOrders", missingPromoterOrders);
            result.put("promoterSetRate", totalOrders > 0 ? (double) ordersWithPromoter / totalOrders * 100 : 0);
            result.put("missingRate", totalOrders > 0 ? (double) missingPromoterOrders / totalOrders * 100 : 0);
            result.put("checkTime", new Date());
            
            log.info("推广关系一致性检查完成：总订单{}个，有推广者{}个，缺失推广者{}个", 
                totalOrders, ordersWithPromoter, missingPromoterOrders);
            
        } catch (Exception e) {
            log.error("检查推广关系一致性失败", e);
            result.put("error", e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 获取需要补偿的订单列表
     * @param days 最近几天
     * @param limit 限制数量
     * @return 订单列表
     */
    public Map<String, Object> getMissingPromoterOrders(Integer days, Integer limit) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 查找需要补偿的订单
            QueryWrapper<Order> wrapper = new QueryWrapper<>();
            wrapper.isNull("promoter_id");
            wrapper.ge("create_time", new Date(System.currentTimeMillis() - days * 24 * 60 * 60 * 1000L));
            wrapper.orderByDesc("create_time");
            wrapper.last("LIMIT " + limit);
            
            List<Order> orders = orderMapper.selectList(wrapper);
            
            result.put("orders", orders);
            result.put("count", orders.size());
            result.put("queryTime", new Date());
            
        } catch (Exception e) {
            log.error("获取需要补偿的订单列表失败", e);
            result.put("error", e.getMessage());
        }
        
        return result;
    }
}