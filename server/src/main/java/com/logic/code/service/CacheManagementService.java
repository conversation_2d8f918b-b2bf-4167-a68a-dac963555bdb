package com.logic.code.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 缓存管理服务
 * 提供缓存监控、统计和管理功能
 * 
 * <AUTHOR>
 * @date 2025/8/4
 */
@Service
public class CacheManagementService {

    @Autowired
    private CacheManager cacheManager;
    
    // 缓存统计信息
    private final Map<String, CacheStats> cacheStatsMap = new ConcurrentHashMap<>();
    
    /**
     * 缓存统计信息类
     */
    public static class CacheStats {
        private final AtomicLong hitCount = new AtomicLong(0);
        private final AtomicLong missCount = new AtomicLong(0);
        private final AtomicLong putCount = new AtomicLong(0);
        private final AtomicLong evictCount = new AtomicLong(0);
        
        public void recordHit() { hitCount.incrementAndGet(); }
        public void recordMiss() { missCount.incrementAndGet(); }
        public void recordPut() { putCount.incrementAndGet(); }
        public void recordEvict() { evictCount.incrementAndGet(); }
        
        public long getHitCount() { return hitCount.get(); }
        public long getMissCount() { return missCount.get(); }
        public long getPutCount() { return putCount.get(); }
        public long getEvictCount() { return evictCount.get(); }
        
        public double getHitRate() {
            long total = hitCount.get() + missCount.get();
            return total == 0 ? 0.0 : (double) hitCount.get() / total;
        }
    }
    
    /**
     * 获取所有缓存名称
     */
    public Collection<String> getCacheNames() {
        return cacheManager.getCacheNames();
    }
    
    /**
     * 获取指定缓存
     */
    public Cache getCache(String cacheName) {
        return cacheManager.getCache(cacheName);
    }
    
    /**
     * 清空指定缓存
     */
    public void evictCache(String cacheName) {
        Cache cache = cacheManager.getCache(cacheName);
        if (cache != null) {
            cache.clear();
            getCacheStats(cacheName).recordEvict();
        }
    }
    
    /**
     * 清空所有缓存
     */
    public void evictAllCaches() {
        for (String cacheName : cacheManager.getCacheNames()) {
            evictCache(cacheName);
        }
    }
    
    /**
     * 清空商品相关缓存
     */
    public void evictGoodsRelatedCaches() {
        evictCache("goodsPageInfo");
        evictCache("goodsDetail");
        evictCache("goodsCategory");
    }
    
    /**
     * 获取缓存统计信息
     */
    public CacheStats getCacheStats(String cacheName) {
        return cacheStatsMap.computeIfAbsent(cacheName, k -> new CacheStats());
    }
    
    /**
     * 获取所有缓存统计信息
     */
    public Map<String, Map<String, Object>> getAllCacheStats() {
        Map<String, Map<String, Object>> result = new HashMap<>();
        
        for (String cacheName : cacheManager.getCacheNames()) {
            CacheStats stats = getCacheStats(cacheName);
            Map<String, Object> statsMap = new HashMap<>();
            statsMap.put("hitCount", stats.getHitCount());
            statsMap.put("missCount", stats.getMissCount());
            statsMap.put("putCount", stats.getPutCount());
            statsMap.put("evictCount", stats.getEvictCount());
            statsMap.put("hitRate", String.format("%.2f%%", stats.getHitRate() * 100));
            
            result.put(cacheName, statsMap);
        }
        
        return result;
    }
    
    /**
     * 获取缓存详细信息
     */
    public Map<String, Object> getCacheInfo(String cacheName) {
        Cache cache = cacheManager.getCache(cacheName);
        if (cache == null) {
            return Collections.emptyMap();
        }
        
        Map<String, Object> info = new HashMap<>();
        info.put("name", cacheName);
        info.put("nativeCache", cache.getNativeCache().getClass().getSimpleName());
        
        CacheStats stats = getCacheStats(cacheName);
        info.put("stats", Map.of(
            "hitCount", stats.getHitCount(),
            "missCount", stats.getMissCount(),
            "putCount", stats.getPutCount(),
            "evictCount", stats.getEvictCount(),
            "hitRate", String.format("%.2f%%", stats.getHitRate() * 100)
        ));
        
        return info;
    }
    
    /**
     * 预热商品缓存
     * 预加载热门商品数据到缓存中
     */
    public void warmUpGoodsCache() {
        // 这里可以实现缓存预热逻辑
        // 例如：预加载前几页的商品数据
        System.out.println("开始预热商品缓存...");
        
        // 可以调用商品服务的查询方法来预热缓存
        // 例如：查询前3页的热门商品、新品等
        
        System.out.println("商品缓存预热完成");
    }
    
    /**
     * 检查缓存健康状态
     */
    public Map<String, Object> checkCacheHealth() {
        Map<String, Object> health = new HashMap<>();
        
        try {
            Collection<String> cacheNames = cacheManager.getCacheNames();
            health.put("status", "UP");
            health.put("cacheCount", cacheNames.size());
            health.put("cacheNames", cacheNames);
            
            // 检查每个缓存的状态
            Map<String, String> cacheStatus = new HashMap<>();
            for (String cacheName : cacheNames) {
                Cache cache = cacheManager.getCache(cacheName);
                cacheStatus.put(cacheName, cache != null ? "AVAILABLE" : "UNAVAILABLE");
            }
            health.put("cacheStatus", cacheStatus);
            
        } catch (Exception e) {
            health.put("status", "DOWN");
            health.put("error", e.getMessage());
        }
        
        return health;
    }
    
    /**
     * 重置缓存统计信息
     */
    public void resetCacheStats() {
        cacheStatsMap.clear();
    }
    
    /**
     * 重置指定缓存的统计信息
     */
    public void resetCacheStats(String cacheName) {
        cacheStatsMap.remove(cacheName);
    }
}
