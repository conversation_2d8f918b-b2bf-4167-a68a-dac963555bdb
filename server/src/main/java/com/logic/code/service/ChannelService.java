package com.logic.code.service;

import com.logic.code.entity.goods.Channel;
import com.logic.code.mapper.ChannelMapper;
import com.logic.code.mapper.CommonMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/5/7 23:29
 * @desc
 */
@Service
public class ChannelService extends BaseService<Channel>{

    @Resource
    private ChannelMapper channelMapper;
    @Override
    protected CommonMapper<Channel> getMapper() {
        return channelMapper;
    }
}
