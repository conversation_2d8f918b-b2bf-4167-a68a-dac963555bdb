package com.logic.code.service;

import com.logic.code.common.utils.JwtHelper;
import com.logic.code.entity.MsgNotice;
import com.logic.code.entity.order.Cart;
import com.logic.code.mapper.CommonMapper;
import com.logic.code.mapper.MsgNoticeMapper;
import jakarta.annotation.Resource;
import logic.orm.WrapperBuilder;
import logic.orm.utils.ListUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/30 15:15
 * @desc
 */
@Service
public class MsgNoticeService extends BaseService<MsgNotice> {

    @Resource
    private MsgNoticeMapper msgNoticeMapper;

    @Override
    protected CommonMapper<MsgNotice> getMapper() {
        return msgNoticeMapper;
    }

    public boolean insertOrUpdate(String id) {
        MsgNotice params = new MsgNotice();
        params.setUserId(JwtHelper.getUserInfo().getId());
        params.setTemplId(id);
        List<MsgNotice> notices = msgNoticeMapper.selectList(WrapperBuilder.autoWhere(params));
        if (ListUtils.isNotBlank(notices)) {
            MsgNotice msgNotice = notices.get(0);
            msgNotice.setNum(msgNotice.getNum() + 1);
            return msgNoticeMapper.updateById(msgNotice) > 0;
        } else {
            params.setNum(1);
            return msgNoticeMapper.insert(params) > 0;
        }
    }

    public boolean reduce(String id) {
        MsgNotice params = new MsgNotice();
        params.setUserId(JwtHelper.getUserInfo().getId());
        params.setTemplId(id);
        List<MsgNotice> notices = msgNoticeMapper.selectList(WrapperBuilder.autoWhere(params));
        if (ListUtils.isNotBlank(notices)) {
            MsgNotice msgNotice = notices.get(0);
            msgNotice.setNum(msgNotice.getNum() - 1);
            return msgNoticeMapper.updateById(msgNotice) > 0;
        }
        return false;
    }
}
