package com.logic.code.service;

import com.logic.code.entity.system.SystemMenu;
import com.logic.code.mapper.CommonMapper;
import com.logic.code.mapper.MenuPermissionMapper;
import com.logic.code.mapper.SystemMenuMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 系统菜单服务类
 * 
 * <AUTHOR>
 * @date 2025/7/7
 */
@Service
public class SystemMenuService extends BaseService<SystemMenu> {

    @Resource
    private SystemMenuMapper systemMenuMapper;
    
    @Resource
    private MenuPermissionMapper menuPermissionMapper;

    @Override
    protected CommonMapper<SystemMenu> getMapper() {
        return systemMenuMapper;
    }

    /**
     * 获取菜单树结构
     */
    public List<SystemMenu> getMenuTree() {
        // 获取所有显示的菜单
        List<SystemMenu> allMenus = systemMenuMapper.selectAllVisible();
        
        // 获取所有权限信息
        Map<Integer, List<String>> permissionMap = getPermissionMap();
        
        // 为每个菜单设置权限
        for (SystemMenu menu : allMenus) {
            menu.setAuth(permissionMap.getOrDefault(menu.getId(), new ArrayList<>()));
        }
        
        // 构建树结构
        return buildMenuTree(allMenus);
    }

    /**
     * 构建菜单树结构
     */
    private List<SystemMenu> buildMenuTree(List<SystemMenu> allMenus) {
        // 创建菜单映射
        Map<Integer, SystemMenu> menuMap = new HashMap<>();
        for (SystemMenu menu : allMenus) {
            menuMap.put(menu.getId(), menu);
        }
        
        // 构建树结构
        List<SystemMenu> rootMenus = new ArrayList<>();
        for (SystemMenu menu : allMenus) {
            if (menu.getPid() == 0) {
                // 根菜单
                rootMenus.add(menu);
            } else {
                // 子菜单
                SystemMenu parent = menuMap.get(menu.getPid());
                if (parent != null) {
                    if (parent.getChildren() == null) {
                        parent.setChildren(new ArrayList<>());
                    }
                    parent.getChildren().add(menu);
                }
            }
        }
        
        // 递归构建多级子菜单
        buildChildrenTree(rootMenus, menuMap);
        
        return rootMenus;
    }

    /**
     * 递归构建子菜单树
     */
    private void buildChildrenTree(List<SystemMenu> menus, Map<Integer, SystemMenu> menuMap) {
        for (SystemMenu menu : menus) {
            if (menu.getChildren() != null && !menu.getChildren().isEmpty()) {
                // 对子菜单进行排序
                menu.getChildren().sort((m1, m2) -> {
                    int sort1 = m1.getSortOrder() != null ? m1.getSortOrder() : 0;
                    int sort2 = m2.getSortOrder() != null ? m2.getSortOrder() : 0;
                    if (sort1 != sort2) {
                        return Integer.compare(sort1, sort2);
                    }
                    return Integer.compare(m1.getId(), m2.getId());
                });
                
                // 递归处理子菜单
                buildChildrenTree(menu.getChildren(), menuMap);
            }
        }
    }

    /**
     * 获取权限映射
     */
    private Map<Integer, List<String>> getPermissionMap() {
        return menuPermissionMapper.selectAllPermissions()
                .stream()
                .collect(Collectors.groupingBy(
                    permission -> permission.getMenuId(),
                    Collectors.mapping(
                        permission -> permission.getPermission(),
                        Collectors.toList()
                    )
                ));
    }

    /**
     * 根据菜单ID获取权限列表
     */
    public List<String> getPermissionsByMenuId(Integer menuId) {
        return menuPermissionMapper.selectPermissionsByMenuId(menuId);
    }

    /**
     * 获取所有根菜单
     */
    public List<SystemMenu> getRootMenus() {
        return systemMenuMapper.selectRootMenus();
    }

    /**
     * 根据父级ID获取子菜单
     */
    public List<SystemMenu> getMenusByPid(Integer pid) {
        return systemMenuMapper.selectByPid(pid);
    }
}
