package com.logic.code.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logic.code.entity.file.FileAttachment;
import com.logic.code.mapper.FileAttachmentMapper;
import com.logic.code.service.FileAttachmentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

/**
 * File attachment service implementation
 */
@Service
public class FileAttachmentServiceImpl implements FileAttachmentService {

    private static final Logger logger = LoggerFactory.getLogger(FileAttachmentServiceImpl.class);

    @Autowired
    private FileAttachmentMapper fileAttachmentMapper;

    @Value("${app.upload.dir:./uploads}")
    private String uploadDir;

    @Value("${app.upload.url:/weshop-wjhx/uploads}")
    private String uploadUrl;

    @Override
    @Transactional
    public Map<String, String> uploadFile(MultipartFile file, Integer categoryId, Integer userId, String userName) {
        if (file == null || file.isEmpty()) {
            throw new RuntimeException("File is empty");
        }

        try {
            // Create upload directory if it doesn't exist
            Path uploadPath = Paths.get(uploadDir);
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
            }

            // Generate a unique file name
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null) {
                originalFilename = "unknown.jpg";
            }

            String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            String filename = UUID.randomUUID() + extension;

            // Save the file
            Path targetLocation = uploadPath.resolve(filename);
            Files.copy(file.getInputStream(), targetLocation);

            // Save file info to database
            FileAttachment attachment = new FileAttachment();
            attachment.setName(originalFilename);
            attachment.setPath(targetLocation.toString());
            attachment.setUrl(uploadUrl + "/" + filename);
            attachment.setSize(file.getSize());
            attachment.setType(extension);
            attachment.setCategoryId(categoryId);
            attachment.setUserId(userId);
            attachment.setUserName(userName);
            attachment.setUploadSource(0); // Local upload
            attachment.setIsDelete(false);
            attachment.setCreateTime(new Date());
            attachment.setUpdateTime(new Date());

            fileAttachmentMapper.insert(attachment);

            // Return file info
            Map<String, String> result = new HashMap<>();
            result.put("src", attachment.getUrl());
            result.put("filename", filename);
            result.put("id", attachment.getId().toString());

            return result;
        } catch (IOException e) {
            logger.error("Failed to upload file", e);
            throw new RuntimeException("Failed to upload file: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Map<String, String> uploadOnlineFile(String url, Integer categoryId, Integer userId, String userName) {
        if (!StringUtils.hasText(url)) {
            throw new RuntimeException("URL is empty");
        }

        try {
            // Create upload directory if it doesn't exist
            Path uploadPath = Paths.get(uploadDir);
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
            }

            // Download file from URL
            URL fileUrl = new URL(url);
            String originalFilename = url.substring(url.lastIndexOf("/") + 1);
            String extension = originalFilename.contains(".") ?
                    originalFilename.substring(originalFilename.lastIndexOf(".")) : ".jpg";
            String filename = UUID.randomUUID() + extension;

            // Save the file
            Path targetLocation = uploadPath.resolve(filename);
            try (InputStream in = fileUrl.openStream()) {
                Files.copy(in, targetLocation);
            }

            // Get file size
            long fileSize = Files.size(targetLocation);

            // Save file info to database
            FileAttachment attachment = new FileAttachment();
            attachment.setName(originalFilename);
            attachment.setPath(targetLocation.toString());
            attachment.setUrl(uploadUrl + "/" + filename);
            attachment.setSize(fileSize);
            attachment.setType(extension);
            attachment.setCategoryId(categoryId);
            attachment.setUserId(userId);
            attachment.setUserName(userName);
            attachment.setUploadSource(1); // Online upload
            attachment.setIsDelete(false);
            attachment.setCreateTime(new Date());
            attachment.setUpdateTime(new Date());

            fileAttachmentMapper.insert(attachment);

            // Return file info
            Map<String, String> result = new HashMap<>();
            result.put("src", attachment.getUrl());
            result.put("filename", filename);
            result.put("id", attachment.getId().toString());

            return result;
        } catch (IOException e) {
            logger.error("Failed to upload online file", e);
            throw new RuntimeException("Failed to upload online file: " + e.getMessage());
        }
    }

    @Override
    public Page<FileAttachment> getFileList(Integer page, Integer limit, Integer categoryId, String keyword) {
        Page<FileAttachment> pageParam = new Page<>(page, limit);
        QueryWrapper<FileAttachment> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_delete", false);

        if (categoryId != null && categoryId > 0) {
            queryWrapper.eq("category_id", categoryId);
        }

        if (StringUtils.hasText(keyword)) {
            queryWrapper.like("name", keyword);
        }

        queryWrapper.orderByDesc("create_time");

        return fileAttachmentMapper.selectPage(pageParam, queryWrapper);
    }

    @Override
    public FileAttachment getById(Integer id) {
        return fileAttachmentMapper.selectById(id);
    }

    @Override
    @Transactional
    public boolean updateFile(FileAttachment file) {
        file.setUpdateTime(new Date());
        return fileAttachmentMapper.updateById(file) > 0;
    }

    @Override
    @Transactional
    public boolean deleteFile(Integer id) {
        FileAttachment file = fileAttachmentMapper.selectById(id);
        if (file == null) {
            return false;
        }

        // Delete physical file
        try {
            Path filePath = Paths.get(file.getPath());
            if (Files.exists(filePath)) {
                Files.delete(filePath);
            }
        } catch (IOException e) {
            logger.error("Failed to delete file", e);
        }

        // Soft delete in database
        file.setIsDelete(true);
        file.setUpdateTime(new Date());
        return fileAttachmentMapper.updateById(file) > 0;
    }

    @Override
    @Transactional
    public boolean moveFiles(List<Integer> ids, Integer categoryId) {
        if (ids == null || ids.isEmpty() || categoryId == null) {
            return false;
        }

        for (Integer id : ids) {
            FileAttachment file = fileAttachmentMapper.selectById(id);
            if (file != null && !file.getIsDelete()) {
                file.setCategoryId(categoryId);
                file.setUpdateTime(new Date());
                fileAttachmentMapper.updateById(file);
            }
        }

        return true;
    }
}
