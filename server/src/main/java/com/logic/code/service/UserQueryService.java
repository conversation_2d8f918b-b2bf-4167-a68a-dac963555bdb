package com.logic.code.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.logic.code.entity.User;
import com.logic.code.mapper.UserMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 用户查询服务
 * <AUTHOR>
 * @date 2025/9/5
 */
@Service
public class UserQueryService {

    @Resource
    private UserMapper userMapper;

    /**
     * 根据User实体字段进行分页查询
     *
     * @param queryParams 查询参数
     * @return 查询结果
     */
    public Map<String, Object> queryUsersByFields(Map<String, Object> queryParams) {
        try {
            // 构建查询条件
            QueryWrapper<User> wrapper = new QueryWrapper<>();
            
            // 用户ID查询
            if (queryParams.get("id") != null && !queryParams.get("id").toString().isEmpty()) {
                wrapper.eq("id", queryParams.get("id"));
            }
            
            // 用户名查询
            if (queryParams.get("username") != null && !queryParams.get("username").toString().isEmpty()) {
                wrapper.like("username", queryParams.get("username"));
            }
            
            // 昵称查询
            if (queryParams.get("nickname") != null && !queryParams.get("nickname").toString().isEmpty()) {
                wrapper.like("nickname", queryParams.get("nickname"));
            }
            
            // 手机号查询
            if (queryParams.get("mobile") != null && !queryParams.get("mobile").toString().isEmpty()) {
                wrapper.like("mobile", queryParams.get("mobile"));
            }
            
            // 性别查询
            if (queryParams.get("gender") != null && !queryParams.get("gender").toString().isEmpty()) {
                wrapper.eq("gender", queryParams.get("gender"));
            }
            
            // 推广等级查询
            if (queryParams.get("promotionLevel") != null && !queryParams.get("promotionLevel").toString().isEmpty()) {
                wrapper.eq("promotion_level", queryParams.get("promotionLevel"));
            }
            
            // 余额范围查询
            if (queryParams.get("minBalance") != null && !queryParams.get("minBalance").toString().isEmpty()) {
                wrapper.ge("balance", new BigDecimal(queryParams.get("minBalance").toString()));
            }
            if (queryParams.get("maxBalance") != null && !queryParams.get("maxBalance").toString().isEmpty()) {
                wrapper.le("balance", new BigDecimal(queryParams.get("maxBalance").toString()));
            }
            
            // 积分范围查询
            if (queryParams.get("minPoints") != null && !queryParams.get("minPoints").toString().isEmpty()) {
                wrapper.ge("points", queryParams.get("minPoints"));
            }
            if (queryParams.get("maxPoints") != null && !queryParams.get("maxPoints").toString().isEmpty()) {
                wrapper.le("points", queryParams.get("maxPoints"));
            }
            
            // 注册时间范围查询
            if (queryParams.get("registerTimeStart") != null && !queryParams.get("registerTimeStart").toString().isEmpty()) {
                wrapper.ge("register_time", queryParams.get("registerTimeStart"));
            }
            if (queryParams.get("registerTimeEnd") != null && !queryParams.get("registerTimeEnd").toString().isEmpty()) {
                wrapper.le("register_time", queryParams.get("registerTimeEnd"));
            }
            
            // 排序
            wrapper.orderByDesc("register_time");
            
            // 分页参数
            Integer page = queryParams.get("page") != null ? Integer.parseInt(queryParams.get("page").toString()) : 1;
            Integer limit = queryParams.get("limit") != null ? Integer.parseInt(queryParams.get("limit").toString()) : 20;
            
            // 计算总数
            Long total = userMapper.selectCount(wrapper);
            
            // 分页查询
            int offset = (page - 1) * limit;
            wrapper.last("LIMIT " + offset + ", " + limit);
            
            List<User> users = userMapper.selectList(wrapper);
            
            // 构建返回结果
            List<Map<String, Object>> userList = new ArrayList<>();
            for (User user : users) {
                Map<String, Object> userInfo = new HashMap<>();
                userInfo.put("id", user.getId());
                userInfo.put("username", user.getUsername());
                userInfo.put("nickname", user.getNickname());
                userInfo.put("mobile", user.getMobile());
                userInfo.put("gender", user.getGender());
                userInfo.put("birthday", user.getBirthday());
                userInfo.put("registerTime", user.getRegisterTime());
                userInfo.put("lastLoginTime", user.getLastLoginTime());
                userInfo.put("lastLoginIp", user.getLastLoginIp());
                userInfo.put("userLevelId", user.getUserLevelId());
                userInfo.put("registerIp", user.getRegisterIp());
                userInfo.put("avatar", user.getAvatar());
                userInfo.put("wechatOpenId", user.getWechatOpenId());
                userInfo.put("promoterId", user.getPromoterId());
                userInfo.put("promotionCode", user.getPromotionCode());
                userInfo.put("promotionCount", user.getPromotionCount());
                userInfo.put("promotionTime", user.getPromotionTime());
                userInfo.put("firstPromotionTime", user.getFirstPromotionTime());
                userInfo.put("promotionLevel", user.getPromotionLevel());
                userInfo.put("balance", user.getBalance() != null ? user.getBalance() : BigDecimal.ZERO);
                userInfo.put("points", user.getPoints() != null ? user.getPoints() : 0);
                userInfo.put("mark", user.getMark());
                userInfo.put("updateTime", user.getUpdateTime());
                
                userList.add(userInfo);
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("list", userList);
            result.put("count", total);
            result.put("page", page);
            result.put("limit", limit);
            result.put("pages", (int) Math.ceil((double) total / limit));
            
            return result;
        } catch (Exception e) {
            throw new RuntimeException("用户分页查询失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 更新用户等级
     *
     * @param userId 用户ID
     * @param userLevelId 用户等级ID
     * @return 是否更新成功
     */
    public boolean updateUserLevel(Integer userId, Integer userLevelId) {
        try {
            // 查询用户是否存在
            User user = userMapper.selectById(userId);
            if (user == null) {
                throw new RuntimeException("用户不存在");
            }
            
            // 更新用户等级
            User updateUser = new User();
            updateUser.setId(userId);
            updateUser.setUserLevelId(userLevelId);
            
            int result = userMapper.updateById(updateUser);
            return result > 0;
        } catch (Exception e) {
            throw new RuntimeException("更新用户等级失败: " + e.getMessage(), e);
        }
    }
}
