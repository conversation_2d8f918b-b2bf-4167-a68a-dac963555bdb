package com.logic.code.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logic.code.common.CommonPage;
import com.logic.code.common.PageParamRequest;
import com.logic.code.entity.User;
import com.logic.code.entity.PromotionEarnings;
import com.logic.code.mapper.PromotionEarningsMapper;
import com.logic.code.mapper.UserMapper;
import com.logic.code.model.income.IncomeDetailsRequest;
import com.logic.code.model.income.IncomeDetailsResponse;
import com.logic.code.model.income.IncomeStatisticsResponse;
import com.logic.code.service.IncomeService;
import com.logic.code.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Income service implementation with enhanced business logic
 */
@Slf4j
@Service
public class IncomeServiceImpl implements IncomeService {

    @Autowired
    private PromotionEarningsMapper promotionEarningsMapper;

    @Autowired
    private UserMapper userMapper;

    private static final Map<String, String> STATUS_MAP = new HashMap<>();

    static {
        // Status mapping based on PromotionCommissionService status management
        STATUS_MAP.put("pending", "Pending");     // pending - order paid but not confirmed
        STATUS_MAP.put("confirmed", "Confirmed"); // confirmed - goods received, withdrawable
        STATUS_MAP.put("cancelled", "Cancelled"); // cancelled - rolled back after order cancellation
    }

    @Override
    public IncomeStatisticsResponse getUserIncomeStatistics(Integer userId) {
        log.info("Getting user income statistics for userId: {}", userId);

        // Get user info
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("User not found");
        }

        IncomeStatisticsResponse response = new IncomeStatisticsResponse();
        response.setUserId(userId);
        response.setNickname(user.getNickname());
        response.setAvatar(user.getAvatar());

        // Query all income records for user (promoter)
        LambdaQueryWrapper<PromotionEarnings> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PromotionEarnings::getPromoterId, userId);

        List<PromotionEarnings> records = promotionEarningsMapper.selectList(wrapper);

        // Calculate statistics based on PromotionCommissionService logic
        BigDecimal totalIncome = BigDecimal.ZERO;
        BigDecimal confirmedAmount = BigDecimal.ZERO;
        BigDecimal pendingAmount = BigDecimal.ZERO;
        BigDecimal commissionAmount = BigDecimal.ZERO;
        BigDecimal tieredPromotionAmount = BigDecimal.ZERO;
        BigDecimal monthlyIncome = BigDecimal.ZERO;
        BigDecimal dailyIncome = BigDecimal.ZERO;

        LocalDate today = LocalDate.now();
        LocalDate monthStart = today.withDayOfMonth(1);

        for (PromotionEarnings record : records) {
            BigDecimal amount = record.getCommissionAmount();
            if (amount != null) {
                totalIncome = totalIncome.add(amount);

                // Statistics by status
                if ("pending".equals(record.getStatus())) {
                    pendingAmount = pendingAmount.add(amount);
                } else if ("confirmed".equals(record.getStatus())) {
                    confirmedAmount = confirmedAmount.add(amount);
                }

                // Statistics by type
                if (Boolean.TRUE.equals(record.getIsTieredPromotion())) {
                    tieredPromotionAmount = tieredPromotionAmount.add(amount);
                } else {
                    commissionAmount = commissionAmount.add(amount);
                }

                // Time-based statistics
                if (record.getCreateTime() != null) {
                    LocalDate createDate = record.getCreateTime().toInstant()
                            .atZone(java.time.ZoneId.systemDefault()).toLocalDate();
                    if (createDate.equals(today)) {
                        dailyIncome = dailyIncome.add(amount);
                    }
                    if (!createDate.isBefore(monthStart)) {
                        monthlyIncome = monthlyIncome.add(amount);
                    }
                }
            }
        }

        response.setTotalIncome(totalIncome);
        response.setConfirmedAmount(confirmedAmount);
        response.setPendingAmount(pendingAmount);
        response.setCommissionAmount(commissionAmount);
        response.setTieredPromotionAmount(tieredPromotionAmount);
        response.setMonthlyIncome(monthlyIncome);
        response.setDailyIncome(dailyIncome);
        response.setTotalRecords(records.size());
        response.setLastUpdateTime(DateUtil.nowDateTimeStr());

        log.info("User income statistics completed for userId: {}, total: {}, confirmed: {}, pending: {}", 
                userId, totalIncome, confirmedAmount, pendingAmount);
        return response;
    }

    @Override
    public IncomeDetailsResponse getUserIncomeDetails(IncomeDetailsRequest request, PageParamRequest pageParamRequest) {
        log.info("Getting user income details, request: {}", request);

        // Build query conditions
        LambdaQueryWrapper<PromotionEarnings> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PromotionEarnings::getPromoterId, request.getUserId());

        // Filter by status
        if (request.getStatus() != null) {
            String statusStr = getStatusString(request.getStatus());
            if (statusStr != null) {
                wrapper.eq(PromotionEarnings::getStatus, statusStr);
            }
        }

        // Date range filter
        if (StringUtils.isNotBlank(request.getStartDate())) {
            wrapper.ge(PromotionEarnings::getCreateTime, request.getStartDate() + " 00:00:00");
        }
        if (StringUtils.isNotBlank(request.getEndDate())) {
            wrapper.le(PromotionEarnings::getCreateTime, request.getEndDate() + " 23:59:59");
        }

        // Keyword search
        if (StringUtils.isNotBlank(request.getKeyword())) {
            wrapper.and(w -> w.like(PromotionEarnings::getOrderNo, request.getKeyword())
                            .or().like(PromotionEarnings::getGoodsName, request.getKeyword()));
        }

        // Sorting
        if ("amount".equals(request.getOrderBy())) {
            if ("asc".equals(request.getOrderDirection())) {
                wrapper.orderByAsc(PromotionEarnings::getCommissionAmount);
            } else {
                wrapper.orderByDesc(PromotionEarnings::getCommissionAmount);
            }
        } else {
            if ("asc".equals(request.getOrderDirection())) {
                wrapper.orderByAsc(PromotionEarnings::getCreateTime);
            } else {
                wrapper.orderByDesc(PromotionEarnings::getCreateTime);
            }
        }

        // Paginated query
        Page<PromotionEarnings> page = new Page<>(pageParamRequest.getPage(), pageParamRequest.getLimit());
        IPage<PromotionEarnings> pageResult = promotionEarningsMapper.selectPage(page, wrapper);

        // Convert to response objects
        List<IncomeDetailsResponse.IncomeDetailItem> items = new ArrayList<>();
        for (PromotionEarnings record : pageResult.getRecords()) {
            items.add(convertToDetailItem(record));
        }

        // Calculate summary
        IncomeDetailsResponse.IncomeDetailsSummary summary = calculateSummary(request);

        IncomeDetailsResponse response = new IncomeDetailsResponse();
        response.setList(items);
        response.setPageInfo(CommonPage.restPage(pageResult));
        response.setSummary(summary);

        log.info("User income details completed for userId: {}, records: {}", request.getUserId(), items.size());
        return response;
    }

    @Override
    public String exportUserIncomeDetails(IncomeDetailsRequest request) {
        log.info("Exporting user income details, request: {}", request);
        
        // TODO: Implement export functionality
        String exportUrl = "/export/income_details_" + request.getUserId() + "_" + System.currentTimeMillis() + ".xlsx";
        
        log.info("User income details export completed, url: {}", exportUrl);
        return exportUrl;
    }

    /**
     * Convert status integer to string
     */
    private String getStatusString(Integer status) {
        if (status == null) return null;
        switch (status) {
            case 0: return "pending";
            case 1: return "confirmed";
            case 2: return "cancelled";
            default: return null;
        }
    }

    /**
     * Convert to detail item object
     */
    private IncomeDetailsResponse.IncomeDetailItem convertToDetailItem(PromotionEarnings record) {
        IncomeDetailsResponse.IncomeDetailItem item = new IncomeDetailsResponse.IncomeDetailItem();
        item.setId(record.getId().longValue());
        item.setUserId(record.getPromoterId());
        item.setIncomeType(Boolean.TRUE.equals(record.getIsTieredPromotion()) ? 2 : 1);
        item.setIncomeTypeName(Boolean.TRUE.equals(record.getIsTieredPromotion()) ? "Tiered Promotion" : "Commission");
        item.setAmount(record.getCommissionAmount());
        item.setOrderNo(record.getOrderNo());
        item.setProductName(record.getGoodsName());
        item.setStatus(getStatusInteger(record.getStatus()));
        item.setStatusName(STATUS_MAP.get(record.getStatus()));
        item.setCreateTime(record.getCreateTime() != null ? DateUtil.dateToStr(record.getCreateTime(), DateUtil.YYYY_MM_DD_HH_MM_SS) : null);
        item.setRemark(record.getDescription());
        
        // Add tiered promotion info
        if (Boolean.TRUE.equals(record.getIsTieredPromotion())) {
            item.setTierLevel(getTierLevelDescription(record));
            item.setPromotionOrderCount(record.getPromotionOrderCount());
        }
        
        return item;
    }

    /**
     * Convert status string to integer
     */
    private Integer getStatusInteger(String status) {
        if (status == null) return null;
        switch (status) {
            case "pending": return 0;
            case "confirmed": return 1;
            case "cancelled": return 2;
            default: return null;
        }
    }

    /**
     * Get tier level description
     */
    private String getTierLevelDescription(PromotionEarnings record) {
        Integer count = record.getPromotionOrderCount();
        if (count != null) {
            if (count == 1) return "1st Order";
            if (count == 2) return "2nd Order";
            if (count == 3) return "3rd Order";
            if (count >= 4) return "4th+ Order";
        }
        return "Regular Promotion";
    }

    /**
     * Calculate summary information
     */
    private IncomeDetailsResponse.IncomeDetailsSummary calculateSummary(IncomeDetailsRequest request) {
        LambdaQueryWrapper<PromotionEarnings> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PromotionEarnings::getPromoterId, request.getUserId());

        // Apply same filter conditions
        if (request.getStatus() != null) {
            String statusStr = getStatusString(request.getStatus());
            if (statusStr != null) {
                wrapper.eq(PromotionEarnings::getStatus, statusStr);
            }
        }
        if (StringUtils.isNotBlank(request.getStartDate())) {
            wrapper.ge(PromotionEarnings::getCreateTime, request.getStartDate() + " 00:00:00");
        }
        if (StringUtils.isNotBlank(request.getEndDate())) {
            wrapper.le(PromotionEarnings::getCreateTime, request.getEndDate() + " 23:59:59");
        }
        if (StringUtils.isNotBlank(request.getKeyword())) {
            wrapper.and(w -> w.like(PromotionEarnings::getOrderNo, request.getKeyword())
                            .or().like(PromotionEarnings::getGoodsName, request.getKeyword()));
        }

        List<PromotionEarnings> records = promotionEarningsMapper.selectList(wrapper);

        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal confirmedAmount = BigDecimal.ZERO;
        BigDecimal pendingAmount = BigDecimal.ZERO;

        for (PromotionEarnings record : records) {
            BigDecimal amount = record.getCommissionAmount();
            if (amount != null) {
                totalAmount = totalAmount.add(amount);

                if ("pending".equals(record.getStatus())) {
                    pendingAmount = pendingAmount.add(amount);
                } else if ("confirmed".equals(record.getStatus())) {
                    confirmedAmount = confirmedAmount.add(amount);
                }
            }
        }

        IncomeDetailsResponse.IncomeDetailsSummary summary = new IncomeDetailsResponse.IncomeDetailsSummary();
        summary.setTotalAmount(totalAmount);
        summary.setConfirmedAmount(confirmedAmount);
        summary.setPendingAmount(pendingAmount);
        summary.setTotalCount(records.size());

        return summary;
    }
}
