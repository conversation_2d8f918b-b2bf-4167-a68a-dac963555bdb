package com.logic.code.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderV3Request;
import com.github.binarywang.wxpay.bean.result.WxPayUnifiedOrderV3Result;
import com.github.binarywang.wxpay.bean.result.enums.TradeTypeEnum;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.logic.code.common.utils.JwtHelper;
import com.logic.code.entity.Coupon;
import com.logic.code.entity.RechargeRecord;
import com.logic.code.entity.User;
import com.logic.code.entity.UserCoupon;
import com.logic.code.mapper.RechargeRecordMapper;
import com.logic.code.mapper.UserMapper;
import com.logic.code.service.RechargeService;
import com.logic.code.service.UserCouponService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * 充值服务实现类
 * <AUTHOR>
 * @date 2025/7/24
 */
@Service
@Slf4j
public class RechargeServiceImpl implements RechargeService {

    @Autowired
    private RechargeRecordMapper rechargeRecordMapper;
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private UserCouponService userCouponService;
    
    @Autowired
    private WxPayService wxPayService;

    @Override
    @Transactional
    public RechargeRecord createRechargeOrder(Integer userId, BigDecimal amount) {
        RechargeRecord record = new RechargeRecord();
        record.setUserId(userId);
        record.setAmount(amount);
        record.setStatus(0); // 待支付
        record.setPaymentMethod(1); // 微信支付
        record.setCreateTime(new Date());
        
        // 根据充值金额设置赠送优惠券
        if (amount.compareTo(new BigDecimal("1000")) >= 0) {
            record.setCouponCount(5);
            record.setCouponAmount(new BigDecimal("20"));
            record.setRemark("充值1000元赠送5张20元消费券");
        } else if (amount.compareTo(new BigDecimal("500")) >= 0) {
            record.setCouponCount(5);
            record.setCouponAmount(new BigDecimal("10"));
            record.setRemark("充值500元赠送5张10元消费券");
        } else {
            record.setCouponCount(0);
            record.setCouponAmount(BigDecimal.ZERO);
            record.setRemark("普通充值");
        }
        
        rechargeRecordMapper.insert(record);
        return record;
    }

    @Override
    public WxPayUnifiedOrderV3Result.JsapiResult prepayRecharge(Integer rechargeId) {
        RechargeRecord record = rechargeRecordMapper.selectById(rechargeId);
        if (record == null || record.getStatus() != 0) {
            throw new RuntimeException("充值订单不存在或状态异常");
        }
        
        User user = userMapper.selectById(record.getUserId());
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        try {
            //record.setAmount(BigDecimal.valueOf(0.01));
            WxPayUnifiedOrderV3Request request = new WxPayUnifiedOrderV3Request();
            request.setDescription("账户充值-" + record.getAmount() + "元");
            request.setOutTradeNo("RECHARGE_" + record.getId() + "_" + System.currentTimeMillis());
            request.setNotifyUrl(wxPayService.getConfig().getNotifyUrl().replace("/notify", "/notifyRecharge"));
            
            WxPayUnifiedOrderV3Request.Amount amount = new WxPayUnifiedOrderV3Request.Amount();
            amount.setTotal(record.getAmount().multiply(new BigDecimal("100")).intValue()); // 转换为分
            request.setAmount(amount);
            
            WxPayUnifiedOrderV3Request.Payer payer = new WxPayUnifiedOrderV3Request.Payer();
            payer.setOpenid(user.getWechatOpenId());
            request.setPayer(payer);
            
            // 更新充值记录的微信订单号
            record.setWxOrderId(request.getOutTradeNo());
            rechargeRecordMapper.updateById(record);
            
            // 使用泛型方法调用，直接返回JsapiResult类型
            WxPayUnifiedOrderV3Result.JsapiResult result = wxPayService.createOrderV3(TradeTypeEnum.JSAPI, request);
            return result;

        } catch (WxPayException e) {
            log.error("创建充值预支付订单失败", e);
            throw new RuntimeException("创建支付订单失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean handleRechargePaySuccess(Integer rechargeId, String wxTransactionId) {
        RechargeRecord record = rechargeRecordMapper.selectById(rechargeId);
        if (record == null || record.getStatus() != 0) {
            log.warn("充值订单不存在或状态异常，rechargeId: {}", rechargeId);
            return false;
        }
        
        try {
            // 更新充值记录状态
            record.setStatus(1); // 已支付
            record.setWxTransactionId(wxTransactionId);
            record.setPayTime(new Date());
            rechargeRecordMapper.updateById(record);
            
            // 更新用户余额
            User user = userMapper.selectById(record.getUserId());
            if (user.getBalance() == null) {
                user.setBalance(BigDecimal.ZERO);
            }
            user.setBalance(user.getBalance().add(record.getAmount()));
            userMapper.updateById(user);
            
            // 赠送优惠券
            if (record.getCouponCount() > 0) {
                giveUserCoupons(record.getUserId(), record.getCouponCount(), record.getCouponAmount());
            }
            
            log.info("充值成功处理完成，用户ID: {}, 充值金额: {}, 赠送优惠券: {}张{}元", 
                    record.getUserId(), record.getAmount(), record.getCouponCount(), record.getCouponAmount());
            
            return true;
        } catch (Exception e) {
            log.error("处理充值支付成功回调失败", e);
            return false;
        }
    }
    
    /**
     * 赠送用户优惠券
     */
    private void giveUserCoupons(Integer userId, Integer count, BigDecimal amount) {
        try {
            // 根据优惠券金额确定模板ID
            Integer couponTemplateId = 1; // 默认10元券
            if (amount.compareTo(new BigDecimal("20")) == 0) {
                couponTemplateId = 2; // 20元券
            }
            
            // 使用UserCouponService添加优惠券
            boolean success = userCouponService.addCouponsToUser(userId, couponTemplateId, count);
            if (success) {
                log.info("成功为用户{}赠送{}张{}元优惠券", userId, count, amount);
            } else {
                log.error("为用户{}赠送优惠券失败", userId);
            }
        } catch (Exception e) {
            log.error("赠送用户优惠券异常", e);
        }
    }

    @Override
    public List<RechargeRecord> getUserRechargeRecords(Integer userId, Integer page, Integer size) {
        Page<RechargeRecord> pageObj = new Page<>(page, size);
        QueryWrapper<RechargeRecord> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId)
               .orderByDesc("create_time");
        
        Page<RechargeRecord> result = rechargeRecordMapper.selectPage(pageObj, wrapper);
        return result.getRecords();
    }

    @Override
    public Map<String, Object> getRechargeConfig() {
        Map<String, Object> config = new HashMap<>();
        
        // 充值档位配置
        List<Map<String, Object>> rechargeOptions = new ArrayList<>();
        
        Map<String, Object> option1 = new HashMap<>();
        option1.put("amount", 500);
        option1.put("bonus", "赠送5张10元消费券");
        option1.put("couponCount", 5);
        option1.put("couponAmount", 10);
        rechargeOptions.add(option1);
        
        Map<String, Object> option2 = new HashMap<>();
        option2.put("amount", 1000);
        option2.put("bonus", "赠送5张20元消费券");
        option2.put("couponCount", 5);
        option2.put("couponAmount", 20);
        rechargeOptions.add(option2);
        
        config.put("rechargeOptions", rechargeOptions);
        config.put("minAmount", 1);
        config.put("maxAmount", 10000);
        
        return config;
    }

    @Override
    public boolean cancelRechargeOrder(Integer rechargeId, Integer userId) {
        try {
            RechargeRecord record = rechargeRecordMapper.selectById(rechargeId);
            if (record == null) {
                log.warn("充值记录不存在，rechargeId: {}", rechargeId);
                return false;
            }
            
            // 验证是否是当前用户的订单
            if (!record.getUserId().equals(userId)) {
                log.warn("用户无权取消此充值订单，userId: {}, rechargeId: {}", userId, rechargeId);
                return false;
            }
            
            // 只有待支付状态的订单才能取消
            if (record.getStatus() != 0) {
                log.warn("充值订单状态不允许取消，status: {}, rechargeId: {}", record.getStatus(), rechargeId);
                return false;
            }
            
            // 更新订单状态为已取消
            record.setStatus(2);
            int updateResult = rechargeRecordMapper.updateById(record);
            
            if (updateResult > 0) {
                log.info("充值订单取消成功，rechargeId: {}", rechargeId);
                return true;
            } else {
                log.error("充值订单取消失败，数据库更新失败，rechargeId: {}", rechargeId);
                return false;
            }
        } catch (Exception e) {
            log.error("取消充值订单异常，rechargeId: {}", rechargeId, e);
            return false;
        }
    }
}