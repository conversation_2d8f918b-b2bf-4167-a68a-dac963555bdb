package com.logic.code.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logic.code.entity.file.FileAttachment;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * File attachment service interface
 */
public interface FileAttachmentService {
    
    /**
     * Upload file
     * @param file File to upload
     * @param categoryId Category ID
     * @param userId User ID
     * @param userName User name
     * @return Uploaded file info
     */
    Map<String, String> uploadFile(MultipartFile file, Integer categoryId, Integer userId, String userName);
    
    /**
     * Upload online file
     * @param url File URL
     * @param categoryId Category ID
     * @param userId User ID
     * @param userName User name
     * @return Uploaded file info
     */
    Map<String, String> uploadOnlineFile(String url, Integer categoryId, Integer userId, String userName);
    
    /**
     * Get file list
     * @param page Page number
     * @param limit Page size
     * @param categoryId Category ID
     * @param keyword Search keyword
     * @return Page of files
     */
    Page<FileAttachment> getFileList(Integer page, Integer limit, Integer categoryId, String keyword);
    
    /**
     * Get file by ID
     * @param id File ID
     * @return File
     */
    FileAttachment getById(Integer id);
    
    /**
     * Update file
     * @param file File to update
     * @return Updated file
     */
    boolean updateFile(FileAttachment file);
    
    /**
     * Delete file
     * @param id File ID
     * @return True if deleted successfully
     */
    boolean deleteFile(Integer id);
    
    /**
     * Move files to another category
     * @param ids File IDs
     * @param categoryId Target category ID
     * @return True if moved successfully
     */
    boolean moveFiles(List<Integer> ids, Integer categoryId);
} 
