package com.logic.code.service;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaCodeLineColor;
import cn.hutool.core.io.FileUtil;
import com.logic.code.config.WxMaConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.UUID;

/**
 * 微信小程序二维码服务实现类
 */
@Slf4j
@Service
public class WxQrCodeService {

    @Value("${wx.miniapp.configs[0].appid}")
    private String appid;

    /**
     * 获取小程序二维码（对应官方createQRCode接口）
     * 适用于需要的码数量较少的业务场景，该接口生成的小程序码永久有效，有数量限制(100,000)
     *
     * @param path  小程序页面路径，最大长度128个字符
     * @param width 二维码的宽度，单位像素。最小280px，最大1280px
     * @return 二维码图片字节数组
     */
    public byte[] createQRCode(String path, int width) {
        try {
            // 使用配置的小程序appid获取服务
            final WxMaService wxService = WxMaConfiguration.getMaService(appid);

            // 确保宽度在有效范围内
            if (width < 280) {
                width = 280; // 微信小程序码最小宽度
            } else if (width > 1280) {
                width = 1280; // 微信小程序码最大宽度
            }

            log.info("开始生成小程序二维码，appid: {}, path: {}, width: {}", appid, path, width);

            // 调用微信官方提供的createQRCode接口生成二维码
            return wxService.getQrcodeService().createQrcodeBytes(path, width);
        } catch (Exception e) {
            log.error("生成小程序二维码失败，appid: {}, path: {}", appid, path, e);
            throw new RuntimeException("生成小程序二维码失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取不限制的小程序码
     *
     * @param scene      参数，最大32个可见字符
     * @param page       页面路径，为空则是首页
     * @param width      宽度，单位px，默认430，最小280px，最大1280px
     * @param autoColor  是否自动配置线条颜色
     * @param lineColor  线条颜色，autoColor为false时有效
     * @param isHyaline  是否需要透明底色
     * @param envVersion 版本类型，release、develop、trial，默认release
     * @return 二维码图片字节数组
     */
    public byte[] createUnlimitedQrCode(String scene, String page, int width,
                                        boolean autoColor, WxMaCodeLineColor lineColor,
                                        boolean isHyaline, String envVersion) {
        try {
            // 使用配置的小程序appid获取服务
            final WxMaService wxService = WxMaConfiguration.getMaService(appid);

            // 确保路径有效且QR码比例合适
            boolean checkPath = false;

            // 确保宽度在有效范围内，且能创建方形二维码
            if (width < 280) {
                width = 280; // 微信小程序码最小宽度
            } else if (width > 1280) {
                width = 1280; // 微信小程序码最大宽度
            }

            log.info("开始生成小程序码, appid: {}, scene: {}, page: {}, width: {}, envVersion: {}",
                     appid, scene, page, width, envVersion);

            return wxService.getQrcodeService()
                    .createWxaCodeUnlimitBytes(scene, page, checkPath, envVersion, width, autoColor, lineColor, isHyaline);
        } catch (Exception e) {
            log.error("生成小程序码失败，appid: {}, scene: {}, page: {}", appid, scene, page, e);
            throw new RuntimeException("生成小程序码失败", e);
        }
    }

    /**
     * 获取不限制的小程序码（使用默认参数）
     *
     * @param scene 参数，最大32个可见字符
     * @param page  页面路径，为空则是首页
     * @return 二维码图片字节数组
     */
    public byte[] createUnlimitedQrCode(String scene, String page) {
        // 默认生成方形二维码，使用合适的默认参数
        return createUnlimitedQrCode(scene, page, 430, false, null, false, "release");
    }

    /**
     * 获取不限制的小程序码并保存为文件
     *
     * @param scene    参数，最大32个可见字符
     * @param page     页面路径，为空则是首页
     * @param savePath 保存路径，为空则保存到临时目录
     * @return 保存的文件路径
     */
    public String createUnlimitedQrCodeAndSave(String scene, String page, String savePath) {
        byte[] qrCodeBytes = createUnlimitedQrCode(scene, page);

        if (savePath == null || savePath.isEmpty()) {
            savePath = System.getProperty("java.io.tmpdir") + File.separator + "wxqrcode_" + UUID.randomUUID() + ".jpg";
        }

        FileUtil.writeBytes(qrCodeBytes, savePath);
        return savePath;
    }

    /**
     * 设置自定义线条颜色
     *
     * @param r 红色（0-255）
     * @param g 绿色（0-255）
     * @param b 蓝色（0-255）
     * @return 线条颜色对象
     */
    public WxMaCodeLineColor createLineColor(String r, String g, String b) {
        WxMaCodeLineColor lineColor = new WxMaCodeLineColor();
        lineColor.setR(r);
        lineColor.setG(g);
        lineColor.setB(b);
        return lineColor;
    }
}
