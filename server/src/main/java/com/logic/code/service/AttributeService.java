package com.logic.code.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logic.code.entity.goods.Attribute;
import com.logic.code.mapper.AttributeMapper;
import com.logic.code.mapper.CommonMapper;
import com.logic.code.model.vo.AttributeWithCategoryVO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/5
 * @desc 商品属性服务类
 */
@Service
public class AttributeService extends BaseService<Attribute> {

    @Resource
    private AttributeMapper attributeMapper;

    @Override
    protected CommonMapper<Attribute> getMapper() {
        return attributeMapper;
    }

    /**
     * 根据属性分类ID查询属性列表
     */
    public List<Attribute> queryByAttributeCategoryId(Integer attributeCategoryId) {
        return queryList(new Attribute().setAttributeCategoryId(attributeCategoryId));
    }

    /**
     * 查询所有启用的属性
     */
    public List<Attribute> queryAllEnabled() {
        return attributeMapper.selectAllEnabled();
    }

    /**
     * 根据商品分类查询相关属性
     */
    public List<Attribute> queryByCategoryId(Integer categoryId) {
        return attributeMapper.selectByCategoryId(categoryId);
    }

    /**
     * 分页查询属性列表
     */
    public Page<Attribute> getPage(String name, Integer page, Integer limit) {
        Page<Attribute> pageData = new Page<>(page, limit);
        QueryWrapper<Attribute> queryWrapper = new QueryWrapper<>();

        // 如果有名称搜索条件，添加模糊查询
        if (StringUtils.hasText(name)) {
            queryWrapper.like("name", name);
        }

        // 按排序字段和ID排序
        queryWrapper.orderByAsc("sort_order", "id");

        return attributeMapper.selectPage(pageData, queryWrapper);
    }

    /**
     * 联合查询属性和属性分类信息（分页）
     */
    public List<AttributeWithCategoryVO> getAttributesWithCategory(String name, Integer categoryId, Integer page, Integer limit) {
        Integer offset = (page - 1) * limit;
        return attributeMapper.selectAttributesWithCategory(name, categoryId, offset, limit);
    }

    /**
     * 统计联合查询的总数
     */
    public Long countAttributesWithCategory(String name, Integer categoryId) {
        return attributeMapper.countAttributesWithCategory(name, categoryId);
    }

    /**
     * 根据属性分类ID统计属性数量
     */
    public Long countByCategoryId(Integer categoryId) {
        QueryWrapper<Attribute> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("attribute_category_id", categoryId);
        return attributeMapper.selectCount(queryWrapper);
    }
}
