package com.logic.code.service;

import com.logic.code.entity.goods.DisplaySystem;

import java.util.List;

/**
 * 展示系统服务接口
 * 
 * <AUTHOR>
 * @date 2025/7/6
 */
public interface DisplaySystemService {
    
    /**
     * 获取所有启用的展示系统
     */
    List<DisplaySystem> getEnabledSystems();
    
    /**
     * 根据ID获取展示系统
     */
    DisplaySystem getById(Integer id);
    
    /**
     * 获取所有展示系统
     */
    List<DisplaySystem> getAllSystems();
}
