package com.logic.code.service;

/**
 * <AUTHOR>
 * @date 2025/5/28 22:40
 * @desc
 */

import com.github.binarywang.wxpay.bean.notify.WxPayRefundNotifyV3Result;
import com.github.binarywang.wxpay.bean.request.WxPayRefundV3Request;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderV3Request;
import com.github.binarywang.wxpay.bean.result.WxPayOrderQueryV3Result;
import com.github.binarywang.wxpay.bean.result.WxPayRefundQueryV3Result;
import com.github.binarywang.wxpay.bean.result.WxPayRefundV3Result;
import com.github.binarywang.wxpay.bean.result.enums.TradeTypeEnum;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.logic.code.common.utils.WxOrderUtils;
import com.logic.code.config.WxPayProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: WxPayRemoteService
 * @Description: 微信支付远程服务
 * @Author: Ray
 * @Date: 2022-02-10 14:09
 */
@Service
@Slf4j
public class WxPayRemoteService {

    @Autowired
    private WxPayService wxService;
    @Autowired
    private WxPayProperties properties;

    /**
     * @Description: 调用下单API
     * @Author: Ray
     * @Date: 2022-02-10 14:54
     **/
    public Map<String, Object> createOrderV3() throws WxPayException {
        log.debug("创建订单 >>>>> ");
        // 商品描述
        String description = "自定义商品描述";
        // 商户订单号
        String outTradeNo = WxOrderUtils.genOutTradeNo();
        // 总金额，单位分
        Integer total = 1;

        WxPayUnifiedOrderV3Request request = this.genWxPayUnifiedOrderV3Request(description, outTradeNo, total);
        String qrCode = wxService.createOrderV3(TradeTypeEnum.JSAPI, request);

        // 返回需要的信息
        HashMap<String, Object> map = new HashMap<>();;
        map.put("qrCode", qrCode);
        map.put("outTradeNo", outTradeNo);
        return map;
    }

    /**
     * @Description: 组装请求数据
     * @Author: Ray
     * @Date: 2022-02-10 14:30
     **/
    private WxPayUnifiedOrderV3Request genWxPayUnifiedOrderV3Request(String description, String outTradeNo, Integer total) {
        WxPayUnifiedOrderV3Request request = new WxPayUnifiedOrderV3Request();
        // 商品描述
        request.setDescription(description);
        // 商户订单号
        request.setOutTradeNo(outTradeNo);

        // 通知地址
        request.setNotifyUrl(properties.getNotifyUrl());
        // 订单金额
        WxPayUnifiedOrderV3Request.Amount amount = new WxPayUnifiedOrderV3Request.Amount();
        // 总金额，单位分
        amount.setTotal(total);
        // 货币类型，选填
        amount.setCurrency("CNY");
        request.setAmount(amount);
        return request;
    }
}
