package com.logic.code.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.logic.code.common.enmus.OrderStatusEnum;
import com.logic.code.common.enmus.PayStatusEnum;
import com.logic.code.entity.TieredPromotionGoods;
import com.logic.code.entity.UserGoodsPromotionStats;
import com.logic.code.entity.goods.Goods;
import com.logic.code.mapper.TieredPromotionGoodsMapper;
import com.logic.code.mapper.UserGoodsPromotionStatsMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 阶梯推广服务类
 */
@Service
@Slf4j
public class TieredPromotionService extends ServiceImpl<TieredPromotionGoodsMapper, TieredPromotionGoods> {
    
    @Autowired
    private TieredPromotionGoodsMapper tieredPromotionGoodsMapper;
    
    @Autowired
    private UserGoodsPromotionStatsMapper userGoodsPromotionStatsMapper;
    
    @Autowired
    private GoodsService goodsService;
    
    /**
     * 计算阶梯推广佣金
     * @param promoterId 推广者ID
     * @param goodsId 商品ID
     * @param orderAmount 订单金额
     * @return 佣金计算结果
     */
    public Map<String, Object> calculateTieredCommission(Integer promoterId, Integer goodsId, BigDecimal orderAmount) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 检查是否为阶梯推广商品
            TieredPromotionGoods tieredGoods = tieredPromotionGoodsMapper.selectByGoodsId(goodsId);
            
            if (tieredGoods == null) {
                // 不是阶梯推广商品，使用默认10%返现
                result.put("isTieredPromotion", false);
                result.put("commissionRate", new BigDecimal("10.00"));
                result.put("commissionAmount", orderAmount.multiply(new BigDecimal("0.10")));
                result.put("promotionOrderCount", 1);
                result.put("tierLevel", "default");
                return result;
            }
            
            // 检查推广者是否已购买过此商品（阶梯推广商品的前置条件）
            boolean hasPurchased = checkPromoterPurchaseHistory(promoterId, goodsId);
            if (!hasPurchased) {
                // 推广者未购买过此商品，不能参与阶梯推广
                result.put("isTieredPromotion", false);
                result.put("commissionRate", BigDecimal.ZERO);
                result.put("commissionAmount", BigDecimal.ZERO);
                result.put("promotionOrderCount", 0);
                result.put("tierLevel", "not_qualified");
                result.put("errorMessage", "需要先购买此商品后才可以参与此活动");
                result.put("requirePurchase", true);
                return result;
            }
            
            // 获取当前推广次数
            Integer currentCount = userGoodsPromotionStatsMapper.getPromotionCount(promoterId, goodsId);
            if (currentCount == null) {
                currentCount = 0;
            }
            
            // 计算这是第几笔推广
            int promotionOrderCount = currentCount + 1;
            
            // 根据推广次数确定返现比例
            BigDecimal commissionRate;
            String tierLevel;
            
            if (promotionOrderCount == 1) {
                commissionRate = tieredGoods.getTier1Rate();
                tierLevel = "tier1";
            } else if (promotionOrderCount == 2) {
                commissionRate = tieredGoods.getTier2Rate();
                tierLevel = "tier2";
            } else if (promotionOrderCount == 3) {
                commissionRate = tieredGoods.getTier3Rate();
                tierLevel = "tier3";
            } else {
                commissionRate = tieredGoods.getTier4PlusRate();
                tierLevel = "tier4plus";
            }
            
            // 计算返现金额
            BigDecimal commissionAmount = orderAmount.multiply(commissionRate.divide(new BigDecimal("100")));
            
            result.put("isTieredPromotion", true);
            result.put("commissionRate", commissionRate);
            result.put("commissionAmount", commissionAmount);
            result.put("promotionOrderCount", promotionOrderCount);
            result.put("tierLevel", tierLevel);
            result.put("tieredGoodsConfig", tieredGoods);
            
            log.info("阶梯推广佣金计算完成：推广者={}, 商品={}, 第{}笔, 返现比例={}%, 返现金额={}", 
                promoterId, goodsId, promotionOrderCount, commissionRate, commissionAmount);
            
        } catch (Exception e) {
            log.error("计算阶梯推广佣金失败：推广者={}, 商品={}, 订单金额={}", promoterId, goodsId, orderAmount, e);
            // 异常情况下使用默认返现
            result.put("isTieredPromotion", false);
            result.put("commissionRate", new BigDecimal("10.00"));
            result.put("commissionAmount", orderAmount.multiply(new BigDecimal("0.10")));
            result.put("promotionOrderCount", 1);
            result.put("tierLevel", "default");
        }
        
        return result;
    }
    
    /**
     * 更新用户商品推广统计
     * @param promoterId 推广者ID
     * @param goodsId 商品ID
     * @param commissionAmount 佣金金额
     * @param promotionOrderCount 推广次数
     */
    @Transactional
    public void updatePromotionStats(Integer promoterId, Integer goodsId, BigDecimal commissionAmount, Integer promotionOrderCount) {
        try {
            UserGoodsPromotionStats stats = userGoodsPromotionStatsMapper.selectByPromoterAndGoods(promoterId, goodsId);
            
            if (stats == null) {
                // 创建新的统计记录
                stats = new UserGoodsPromotionStats();
                stats.setPromoterId(promoterId);
                stats.setGoodsId(goodsId);
                stats.setTotalPromotionCount(1);
                stats.setTier1Count(promotionOrderCount == 1 ? 1 : 0);
                stats.setTier2Count(promotionOrderCount == 2 ? 1 : 0);
                stats.setTier3Count(promotionOrderCount == 3 ? 1 : 0);
                stats.setTier4PlusCount(promotionOrderCount >= 4 ? 1 : 0);
                stats.setTotalCommission(commissionAmount);
                stats.setFirstPromotionTime(new Date());
                stats.setLastPromotionTime(new Date());
                stats.setCreateTime(new Date());
                stats.setUpdateTime(new Date());
                
                userGoodsPromotionStatsMapper.insert(stats);
            } else {
                // 更新现有统计记录
                stats.setTotalPromotionCount(stats.getTotalPromotionCount() + 1);
                
                if (promotionOrderCount == 1) {
                    stats.setTier1Count(stats.getTier1Count() + 1);
                } else if (promotionOrderCount == 2) {
                    stats.setTier2Count(stats.getTier2Count() + 1);
                } else if (promotionOrderCount == 3) {
                    stats.setTier3Count(stats.getTier3Count() + 1);
                } else {
                    stats.setTier4PlusCount(stats.getTier4PlusCount() + 1);
                }
                
                stats.setTotalCommission(stats.getTotalCommission().add(commissionAmount));
                stats.setLastPromotionTime(new Date());
                stats.setUpdateTime(new Date());
                
                userGoodsPromotionStatsMapper.updateById(stats);
            }
            
            log.info("用户商品推广统计更新成功：推广者={}, 商品={}, 第{}笔推广", promoterId, goodsId, promotionOrderCount);
            
        } catch (Exception e) {
            log.error("更新用户商品推广统计失败：推广者={}, 商品={}", promoterId, goodsId, e);
            throw new RuntimeException("更新推广统计失败", e);
        }
    }
    
    /**
     * 检查商品是否为阶梯推广商品
     * @param goodsId 商品ID
     * @return 是否为阶梯推广商品
     */
    public boolean isTieredPromotionGoods(Integer goodsId) {
        return tieredPromotionGoodsMapper.isTieredPromotionGoods(goodsId);
    }
    
    /**
     * 获取阶梯推广商品配置
     * @param goodsId 商品ID
     * @return 阶梯推广配置
     */
    public TieredPromotionGoods getTieredPromotionConfig(Integer goodsId) {
        return tieredPromotionGoodsMapper.selectByGoodsId(goodsId);
    }
    
    /**
     * 获取用户的商品推广统计
     * @param promoterId 推广者ID
     * @param goodsId 商品ID
     * @return 推广统计
     */
    public UserGoodsPromotionStats getUserPromotionStats(Integer promoterId, Integer goodsId) {
        return userGoodsPromotionStatsMapper.selectByPromoterAndGoods(promoterId, goodsId);
    }
    
    /**
     * 获取用户的所有阶梯推广统计
     * @param promoterId 推广者ID
     * @return 推广统计列表
     */
    public List<UserGoodsPromotionStats> getUserAllPromotionStats(Integer promoterId) {
        QueryWrapper<UserGoodsPromotionStats> wrapper = new QueryWrapper<>();
        wrapper.eq("promoter_id", promoterId);
        wrapper.orderByDesc("total_commission");
        return userGoodsPromotionStatsMapper.selectList(wrapper);
    }
    
    /**
     * 配置阶梯推广商品
     * @param goodsId 商品ID
     * @param tier1Rate 第1笔返现比例
     * @param tier2Rate 第2笔返现比例
     * @param tier3Rate 第3笔返现比例
     * @param tier4PlusRate 第4笔及以后返现比例
     * @param description 描述
     * @return 是否成功
     */
    @Transactional
    public boolean configureTieredPromotionGoods(Integer goodsId, BigDecimal tier1Rate, BigDecimal tier2Rate, 
                                               BigDecimal tier3Rate, BigDecimal tier4PlusRate, String description) {
        try {
            // 获取商品信息
            Goods goods = goodsService.queryById(goodsId);
            if (goods == null) {
                log.error("商品不存在：{}", goodsId);
                return false;
            }
            
            // 检查是否已存在配置
            TieredPromotionGoods existingConfig = tieredPromotionGoodsMapper.selectByGoodsId(goodsId);
            
            if (existingConfig != null) {
                // 更新现有配置
                existingConfig.setTier1Rate(tier1Rate);
                existingConfig.setTier2Rate(tier2Rate);
                existingConfig.setTier3Rate(tier3Rate);
                existingConfig.setTier4PlusRate(tier4PlusRate);
                existingConfig.setDescription(description);
                existingConfig.setIsActive(true);
                existingConfig.setUpdateTime(new Date());
                
                tieredPromotionGoodsMapper.updateById(existingConfig);
            } else {
                // 创建新配置
                TieredPromotionGoods newConfig = new TieredPromotionGoods();
                newConfig.setGoodsId(goodsId);
                newConfig.setGoodsName(goods.getName());
                newConfig.setTier1Rate(tier1Rate);
                newConfig.setTier2Rate(tier2Rate);
                newConfig.setTier3Rate(tier3Rate);
                newConfig.setTier4PlusRate(tier4PlusRate);
                newConfig.setDescription(description);
                newConfig.setIsActive(true);
                newConfig.setCreateTime(new Date());
                newConfig.setUpdateTime(new Date());
                
                tieredPromotionGoodsMapper.insert(newConfig);
            }
            
            log.info("阶梯推广商品配置成功：商品ID={}, 返现比例={}%/{}%/{}%/{}%", 
                goodsId, tier1Rate, tier2Rate, tier3Rate, tier4PlusRate);
            
            return true;
            
        } catch (Exception e) {
            log.error("配置阶梯推广商品失败：商品ID={}", goodsId, e);
            return false;
        }
    }
    
    /**
     * 获取所有阶梯推广商品配置
     * @return 配置列表
     */
    public List<TieredPromotionGoods> getAllTieredPromotionGoods() {
        QueryWrapper<TieredPromotionGoods> wrapper = new QueryWrapper<>();
        wrapper.eq("is_active", true);
        wrapper.orderByDesc("create_time");
        return tieredPromotionGoodsMapper.selectList(wrapper);
    }
    
    /**
     * 检查推广者是否已购买过指定商品
     * @param promoterId 推广者ID
     * @param goodsId 商品ID
     * @return 是否已购买
     */
    public boolean checkPromoterPurchaseHistory(Integer promoterId, Integer goodsId) {
        try {
            // 使用MyBatis-Plus的原生SQL查询
            return goodsService.selectCount(
                new QueryWrapper<com.logic.code.entity.goods.Goods>()
                    .apply("EXISTS (SELECT 1 FROM weshop_order o " +
                           "INNER JOIN weshop_order_goods og ON o.id = og.order_id " +
                           "WHERE o.user_id = {0} AND og.goods_id = {1} " +
                           "AND o.pay_status = " + PayStatusEnum.PAID.getValue() + " AND o.order_status != " + OrderStatusEnum.CANCELLED.getValue() + ")",
                           promoterId, goodsId)
            ) > 0;
            
        } catch (Exception e) {
            log.error("检查推广者{}购买商品{}历史失败", promoterId, goodsId, e);
            // 异常情况下返回false，要求用户先购买
            return false;
        }
    }
    
    /**
     * 获取推广者对指定商品的购买资格信息
     * @param promoterId 推广者ID
     * @param goodsId 商品ID
     * @return 购买资格信息
     */
    public Map<String, Object> getPromotionQualification(Integer promoterId, Integer goodsId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 检查是否为阶梯推广商品
            boolean isTieredGoods = isTieredPromotionGoods(goodsId);
            result.put("isTieredPromotion", isTieredGoods);
            
            if (!isTieredGoods) {
                result.put("qualified", true);
                result.put("message", "普通推广商品，无需购买资格");
                return result;
            }
            
            // 检查购买历史
            boolean hasPurchased = checkPromoterPurchaseHistory(promoterId, goodsId);
            result.put("qualified", hasPurchased);
            
            if (hasPurchased) {
                result.put("message", "已购买此商品，可参与阶梯推广活动");
                
                // 获取购买信息
                Map<String, Object> purchaseInfo = getPromoterPurchaseInfo(promoterId, goodsId);
                result.put("purchaseInfo", purchaseInfo);
            } else {
                result.put("message", "需要先购买此商品后才可以参与此活动");
                result.put("requirePurchase", true);
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("获取推广者{}对商品{}的推广资格失败", promoterId, goodsId, e);
            result.put("qualified", false);
            result.put("message", "系统异常，请稍后重试");
            return result;
        }
    }
    
    /**
     * 获取推广者的购买信息
     * @param promoterId 推广者ID
     * @param goodsId 商品ID
     * @return 购买信息
     */
    private Map<String, Object> getPromoterPurchaseInfo(Integer promoterId, Integer goodsId) {
        Map<String, Object> info = new HashMap<>();
        
        try {
            // 这里可以查询推广者的购买详情，如购买时间、数量等
            // 简化实现，只返回基本信息
            info.put("hasPurchased", true);
            info.put("purchaseTime", "已购买"); // 可以查询具体的购买时间
            
        } catch (Exception e) {
            log.error("获取推广者{}购买商品{}信息失败", promoterId, goodsId, e);
        }
        
        return info;
    }
}