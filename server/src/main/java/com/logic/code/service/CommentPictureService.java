package com.logic.code.service;

import com.logic.code.entity.CommentPicture;
import com.logic.code.mapper.CommentPictureMapper;
import com.logic.code.mapper.CommonMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/8 10:21
 * @desc
 */
@Service
public class CommentPictureService extends BaseService<CommentPicture> {

    @Resource
    private CommentPictureMapper commentPictureMapper;

    @Override
    protected CommonMapper<CommentPicture> getMapper() {
        return commentPictureMapper;
    }

    public List<String> queryPicUrlByCommentId(Integer commentId) {
        return commentPictureMapper.selectPicUrlByCommentId(commentId);
    }
}
