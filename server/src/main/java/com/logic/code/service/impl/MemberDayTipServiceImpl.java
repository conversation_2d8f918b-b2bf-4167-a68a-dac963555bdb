package com.logic.code.service.impl;

import com.logic.code.entity.MemberDayTip;
import com.logic.code.mapper.MemberDayTipMapper;
import com.logic.code.model.vo.MemberDayTipVO;
import com.logic.code.service.MemberDayTipService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 会员日提示信息服务实现类
 * <AUTHOR>
 */
@Service
public class MemberDayTipServiceImpl implements MemberDayTipService {
    
    @Autowired
    private MemberDayTipMapper memberDayTipMapper;
    
    @Override
    public MemberDayTipVO getActiveByPosition(String displayPosition) {
        MemberDayTip memberDayTip = memberDayTipMapper.getActiveByPosition(displayPosition);
        
        if (memberDayTip == null) {
            return null;
        }

        return MemberDayTipVO.builder()
                .tipText(memberDayTip.getTipText())
                .isEnabled(memberDayTip.getIsEnabled() == 1)
                .displayPosition(memberDayTip.getDisplayPosition())
                .priority(memberDayTip.getPriority())
                .build();
    }
    
    @Override
    public MemberDayTipVO getGoodsPageTip() {
        return getActiveByPosition("goods");
    }
    
}