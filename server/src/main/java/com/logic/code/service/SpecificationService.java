package com.logic.code.service;

import com.logic.code.entity.goods.Specification;
import com.logic.code.mapper.SpecificationMapper;
import logic.orm.WrapperBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 规格服务类
 */
@Service
public class SpecificationService {

    @Autowired
    private SpecificationMapper specificationMapper;

    /**
     * 创建规格
     */
    public Specification create(Specification specification) {
        specificationMapper.insert(specification);
        return specification;
    }

    /**
     * 根据ID查询规格
     */
    public Specification findById(Integer id) {
        return specificationMapper.selectById(id);
    }

    /**
     * 根据名称查询规格
     */
    public Specification findByName(String name) {
        Specification build = Specification.builder().name(name).build();

        return specificationMapper.selectOne(WrapperBuilder.autoWhere( build));
    }

    /**
     * 查询所有启用的规格
     */
    public List<Specification> findAllEnabled() {
        return specificationMapper.selectAllEnabled();
    }

    /**
     * 根据ID列表查询规格
     */
    public List<Specification> findByIds(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            return new ArrayList<>();
        }
        return specificationMapper.selectByIds(ids);
    }

    /**
     * 更新规格
     */
    public void update(Specification specification) {
        specificationMapper.updateById(specification);
    }

    /**
     * 删除规格
     */
    public void delete(Integer id) {
        specificationMapper.deleteById(id);
    }

    /**
     * 获取或创建规格
     * 如果规格不存在则创建新的规格
     */
    public Specification getOrCreate(String name) {
        Specification specification = findByName(name);
        if (specification == null) {
            specification = Specification.builder()
                    .name(name)
                    .sortOrder((byte) 0)
                    .build();
            create(specification);
        }
        return specification;
    }
}
