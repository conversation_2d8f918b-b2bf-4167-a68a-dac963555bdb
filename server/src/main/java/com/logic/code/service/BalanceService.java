package com.logic.code.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.logic.code.entity.BalanceRecord;
import com.logic.code.entity.User;
import com.logic.code.mapper.BalanceRecordMapper;
import com.logic.code.mapper.UserMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 余额服务类
 * 提供余额充值、使用、退款等功能
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
public class BalanceService {

    @Resource
    private BalanceRecordMapper balanceRecordMapper;

    @Resource
    private UserMapper userMapper;

    /**
     * 使用余额（订单支付）
     * 
     * @param userId 用户ID
     * @param amount 使用金额
     * @param orderId 订单ID
     * @param description 描述
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean useBalanceForOrder(Integer userId, BigDecimal amount, Integer orderId, String description) {
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("使用余额失败：金额无效，userId={}, amount={}", userId, amount);
            return false;
        }

        // 获取用户信息
        User user = userMapper.selectById(userId);
        if (user == null) {
            log.warn("使用余额失败：用户不存在，userId={}", userId);
            return false;
        }

        // 检查余额是否足够
        if (user.getBalance().compareTo(amount) < 0) {
            log.warn("使用余额失败：余额不足，userId={}, balance={}, amount={}", 
                userId, user.getBalance(), amount);
            return false;
        }

        // 计算新余额
        BigDecimal balanceBefore = user.getBalance();
        BigDecimal balanceAfter = balanceBefore.subtract(amount);

        // 更新用户余额
        user.setBalance(balanceAfter);
        int updateResult = userMapper.updateById(user);
        if (updateResult <= 0) {
            log.error("使用余额失败：更新用户余额失败，userId={}", userId);
            return false;
        }

        // 创建余额使用记录
        BalanceRecord record = new BalanceRecord()
                .setUserId(userId)
                .setType(BalanceRecord.TYPE_USE)
                .setAmount(amount.negate()) // 使用余额记录为负数
                .setBalanceBefore(balanceBefore)
                .setBalanceAfter(balanceAfter)
                .setSource(BalanceRecord.SOURCE_ORDER)
                .setSourceId(orderId)
                .setDescription(description)
                .setCreateTime(LocalDateTime.now())
                .setUpdateTime(LocalDateTime.now());

        int insertResult = balanceRecordMapper.insert(record);
        if (insertResult <= 0) {
            log.error("使用余额失败：创建余额记录失败，userId={}", userId);
            throw new RuntimeException("创建余额记录失败");
        }

        log.info("使用余额成功：userId={}, amount={}, orderId={}, balanceBefore={}, balanceAfter={}", 
            userId, amount, orderId, balanceBefore, balanceAfter);
        return true;
    }

    /**
     * 充值余额
     * 
     * @param userId 用户ID
     * @param amount 充值金额
     * @param source 来源
     * @param sourceId 来源ID
     * @param description 描述
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean rechargeBalance(Integer userId, BigDecimal amount, String source, Integer sourceId, String description) {
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("充值余额失败：金额无效，userId={}, amount={}", userId, amount);
            return false;
        }

        // 获取用户信息
        User user = userMapper.selectById(userId);
        if (user == null) {
            log.warn("充值余额失败：用户不存在，userId={}", userId);
            return false;
        }

        // 计算新余额
        BigDecimal balanceBefore = user.getBalance();
        BigDecimal balanceAfter = balanceBefore.add(amount);

        // 更新用户余额
        user.setBalance(balanceAfter);
        int updateResult = userMapper.updateById(user);
        if (updateResult <= 0) {
            log.error("充值余额失败：更新用户余额失败，userId={}", userId);
            return false;
        }

        // 创建余额充值记录
        BalanceRecord record = new BalanceRecord()
                .setUserId(userId)
                .setType(BalanceRecord.TYPE_RECHARGE)
                .setAmount(amount)
                .setBalanceBefore(balanceBefore)
                .setBalanceAfter(balanceAfter)
                .setSource(source)
                .setSourceId(sourceId)
                .setDescription(description)
                .setCreateTime(LocalDateTime.now())
                .setUpdateTime(LocalDateTime.now());

        int insertResult = balanceRecordMapper.insert(record);
        if (insertResult <= 0) {
            log.error("充值余额失败：创建余额记录失败，userId={}", userId);
            throw new RuntimeException("创建余额记录失败");
        }

        log.info("充值余额成功：userId={}, amount={}, source={}, sourceId={}, balanceBefore={}, balanceAfter={}", 
            userId, amount, source, sourceId, balanceBefore, balanceAfter);
        return true;
    }

    /**
     * 退款到余额
     *
     * @param userId 用户ID
     * @param amount 退款金额
     * @param orderId 订单ID
     * @param description 描述
     * @return 是否成功
     */
    /*@Transactional(rollbackFor = Exception.class)
    public boolean refundToBalance(Integer userId, BigDecimal amount, Integer orderId, String description) {
        return rechargeBalance(userId, amount, BalanceRecord.SOURCE_REFUND, orderId, description);
    }*/

    /**
     * 退回余额（订单取消时调用）
     *
     * @param userId 用户ID
     * @param orderId 订单ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean refundBalanceFromOrder(Integer userId, Integer orderId) {
        try {
            // 查找该订单使用的余额记录
            QueryWrapper<BalanceRecord> wrapper = new QueryWrapper<>();
            wrapper.eq("user_id", userId)
                   .eq("source_id", orderId)
                   .eq("source", BalanceRecord.SOURCE_ORDER)
                   .eq("type", BalanceRecord.TYPE_USE);

            BalanceRecord useRecord = balanceRecordMapper.selectOne(wrapper);
            if (useRecord != null && useRecord.getAmount().compareTo(BigDecimal.ZERO) < 0) {
                // 退回余额金额（使用记录是负数，退回时变为正数）
                BigDecimal refundAmount = useRecord.getAmount().abs();

                // 获取用户信息
                User user = userMapper.selectById(userId);
                if (user == null) {
                    log.warn("退回余额失败：用户不存在，userId={}", userId);
                    return false;
                }

                // 计算新余额
                BigDecimal balanceBefore = user.getBalance() != null ? user.getBalance() : BigDecimal.ZERO;
                BigDecimal balanceAfter = balanceBefore.add(refundAmount);

                // 更新用户余额
                user.setBalance(balanceAfter);
                int updateResult = userMapper.updateById(user);
                if (updateResult <= 0) {
                    log.error("退回余额失败：更新用户余额失败，userId={}", userId);
                    return false;
                }

                // 创建余额退回记录
                BalanceRecord refundRecord = new BalanceRecord()
                        .setUserId(userId)
                        .setType(BalanceRecord.TYPE_REFUND)
                        .setAmount(refundAmount)
                        .setBalanceBefore(balanceBefore)
                        .setBalanceAfter(balanceAfter)
                        .setSource(BalanceRecord.SOURCE_ORDER_CANCEL)
                        .setSourceId(orderId)
                        .setDescription("订单取消退回余额")
                        .setCreateTime(LocalDateTime.now())
                        .setUpdateTime(LocalDateTime.now());

                int insertResult = balanceRecordMapper.insert(refundRecord);
                if (insertResult <= 0) {
                    log.error("退回余额失败：创建余额记录失败，userId={}", userId);
                    throw new RuntimeException("创建余额记录失败");
                }

                log.info("用户{}订单{}取消，退回余额{}元", userId, orderId, refundAmount);
                return true;
            }
            return true; // 没有使用余额，不算失败
        } catch (Exception e) {
            log.error("用户{}订单{}余额退回失败", userId, orderId, e);
            return false;
        }
    }

    /**
     * 获取用户余额记录
     * 
     * @param userId 用户ID
     * @param limit 限制条数
     * @return 余额记录列表
     */
    public List<BalanceRecord> getUserBalanceRecords(Long userId, Integer limit) {
        return balanceRecordMapper.getByUserId(userId, limit);
    }

    /**
     * 验证用户余额一致性
     * 
     * @param userId 用户ID
     * @return 是否一致
     */
    public boolean validateUserBalance(Long userId) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            return false;
        }

        BigDecimal calculatedBalance = balanceRecordMapper.calculateUserBalance(userId);
        return user.getBalance().compareTo(calculatedBalance) == 0;
    }

    /**
     * 获取用户当前余额
     * 
     * @param userId 用户ID
     * @return 当前余额
     */
    public BigDecimal getUserBalance(Long userId) {
        User user = userMapper.selectById(userId);
        return user != null ? user.getBalance() : BigDecimal.ZERO;
    }
}
