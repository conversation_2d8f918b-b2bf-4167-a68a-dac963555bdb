package com.logic.code.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.logic.code.entity.file.FileAttachment;
import com.logic.code.entity.file.ScanUpload;
import com.logic.code.mapper.FileAttachmentMapper;
import com.logic.code.mapper.ScanUploadMapper;
import com.logic.code.service.ScanUploadService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

/**
 * Scan upload service implementation
 */
@Service
public class ScanUploadServiceImpl implements ScanUploadService {

    private static final Logger logger = LoggerFactory.getLogger(ScanUploadServiceImpl.class);

    @Autowired
    private ScanUploadMapper scanUploadMapper;

    @Autowired
    private FileAttachmentMapper fileAttachmentMapper;

    @Value("${app.upload.dir:./uploads}")
    private String uploadDir;

    @Value("${app.upload.url:/weshop-wjhx/uploads}")
    private String uploadUrl;

    @Value("${app.scan.expiration:30}")
    private int scanExpiration; // Expiration time in minutes

    @Value("${app.scan.url:http://localhost:9999/weshop-wjhx/scan}")
    private String scanUrl;

    @Override
    @Transactional
    public Map<String, String> generateQrCode(Integer categoryId, Integer userId) {
        // Generate a unique token
        String token = UUID.randomUUID().toString();

        // Create scan upload record
        ScanUpload scanUpload = new ScanUpload();
        scanUpload.setScanToken(token);
        scanUpload.setCategoryId(categoryId);
        scanUpload.setUserId(userId);
        scanUpload.setStatus(0); // Pending

        // Set expiration time
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, scanExpiration);
        scanUpload.setExpireTime(calendar.getTime());

        scanUpload.setCreateTime(new Date());
        scanUpload.setUpdateTime(new Date());

        scanUploadMapper.insert(scanUpload);

        // Return QR code info
        Map<String, String> result = new HashMap<>();
        result.put("url", scanUrl + "?token=" + token);
        result.put("token", token);

        return result;
    }

    @Override
    public ScanUpload getByToken(String token) {
        QueryWrapper<ScanUpload> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("scan_token", token);
        return scanUploadMapper.selectOne(queryWrapper);
    }

    @Override
    @Transactional
    public Map<String, String> uploadViaScan(MultipartFile file, String token) {
        // Validate token
        ScanUpload scanUpload = getByToken(token);
        if (scanUpload == null) {
            throw new RuntimeException("Invalid token");
        }

        // Check if token is expired
        if (scanUpload.getExpireTime().before(new Date())) {
            throw new RuntimeException("Token expired");
        }

        if (file == null || file.isEmpty()) {
            throw new RuntimeException("File is empty");
        }

        try {
            // Create upload directory if it doesn't exist
            Path uploadPath = Paths.get(uploadDir);
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
            }

            // Generate a unique file name
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null) {
                originalFilename = "unknown.jpg";
            }

            String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            String filename = UUID.randomUUID() + extension;

            // Save the file
            Path targetLocation = uploadPath.resolve(filename);
            Files.copy(file.getInputStream(), targetLocation);

            // Save file info to database
            FileAttachment attachment = new FileAttachment();
            attachment.setName(originalFilename);
            attachment.setPath(targetLocation.toString());
            attachment.setUrl(uploadUrl + "/" + filename);
            attachment.setSize(file.getSize());
            attachment.setType(extension);
            attachment.setCategoryId(scanUpload.getCategoryId());
            attachment.setUserId(scanUpload.getUserId());
            attachment.setUserName("scan_upload"); // Could be updated with actual user name
            attachment.setUploadSource(2); // Scan upload
            attachment.setIsDelete(false);
            attachment.setCreateTime(new Date());
            attachment.setUpdateTime(new Date());

            fileAttachmentMapper.insert(attachment);

            // Update scan upload status
            scanUpload.setStatus(1); // Completed
            scanUpload.setUpdateTime(new Date());
            scanUploadMapper.updateById(scanUpload);

            // Return file info
            Map<String, String> result = new HashMap<>();
            result.put("src", attachment.getUrl());
            result.put("filename", filename);
            result.put("id", attachment.getId().toString());

            return result;
        } catch (IOException e) {
            logger.error("Failed to upload file via scan", e);
            throw new RuntimeException("Failed to upload file via scan: " + e.getMessage());
        }
    }

    @Override
    public List<FileAttachment> getUploadedFiles(String token) {
        // Validate token
        ScanUpload scanUpload = getByToken(token);
        if (scanUpload == null) {
            throw new RuntimeException("Invalid token");
        }

        // Get files uploaded via this token
        QueryWrapper<FileAttachment> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("category_id", scanUpload.getCategoryId());
        queryWrapper.eq("user_id", scanUpload.getUserId());
        queryWrapper.eq("upload_source", 2); // Scan upload
        queryWrapper.eq("is_delete", false);
        queryWrapper.orderByDesc("create_time");

        return fileAttachmentMapper.selectList(queryWrapper);
    }

    @Override
    @Transactional
    public boolean deleteToken(String token) {
        QueryWrapper<ScanUpload> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("scan_token", token);
        return scanUploadMapper.delete(queryWrapper) > 0;
    }
}
