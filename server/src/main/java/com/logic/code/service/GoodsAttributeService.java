package com.logic.code.service;

import com.logic.code.entity.goods.GoodsAttribute;
import com.logic.code.mapper.CommonMapper;
import com.logic.code.mapper.GoodsAttributeMapper;
import com.logic.code.model.dto.GoodsAttributeDTO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/8 11:29
 * @desc
 */
@Service
public class GoodsAttributeService extends BaseService<GoodsAttribute> {

    @Resource
    private GoodsAttributeMapper goodsAttributeMapper;

    @Override
    protected CommonMapper<GoodsAttribute> getMapper() {
        return goodsAttributeMapper;
    }

    public List<GoodsAttributeDTO> queryGoodsDetailAttributeByGoodsId(Integer goodsId) {
        return goodsAttributeMapper.selectGoodsDetailAttributeByGoodsId(goodsId);
    }

    public List<GoodsAttribute> queryByGoodsId(Integer goodsId) {
        GoodsAttribute query = new GoodsAttribute();
        query.setGoodsId(goodsId);
        return queryList(query);
    }

    public void deleteByGoodsId(Integer goodsId) {
        GoodsAttribute query = new GoodsAttribute();
        query.setGoodsId(goodsId);
        delete(query);
    }

}
