package com.logic.code.service;

import cn.hutool.core.date.DateUtil;
import com.logic.code.common.Cache;
import com.logic.code.common.utils.ExcelUtils;
import com.logic.code.entity.User;
import com.logic.code.entity.goods.GoodsSpecification;
import com.logic.code.entity.goods.Specification;
import com.logic.code.entity.order.Order;
import com.logic.code.entity.order.OrderGoods;
import com.logic.code.model.vo.OrderDetailVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单导出服务类
 * <AUTHOR>
 * @date 2025/7/27
 */
@Service
@Slf4j
public class OrderExportService {

    @Resource
    private Cache cache;

    @Resource
    private OrderService orderService;

    @Resource
    private UserService userService;

    @Resource
    private GoodsSpecificationService goodsSpecificationService;

    @Resource
    private SpecificationService specificationService;

    /**
     * 导出所有订单信息到Excel
     * 参考printOrderInfo方法，遍历所有订单并导出到Excel文件
     * 按时间段分组：上半场（前一日14点-24点）用绿色，下半场（当日0点-14点）用红色
     */
    public void exportAllOrdersToExcel() {
        try {
            log.info("开始执行订单导出任务...");
            cache.init();

            // 获取所有订单
            List<Order> allOrders = orderService.queryAll();
            log.info("找到订单总数: {}", allOrders.size());

            // 计算时间范围：昨天14点到今天14点
            Calendar yesterday14 = Calendar.getInstance();
            yesterday14.add(Calendar.DAY_OF_MONTH, -1);
            yesterday14.set(Calendar.HOUR_OF_DAY, 14);
            yesterday14.set(Calendar.MINUTE, 0);
            yesterday14.set(Calendar.SECOND, 0);
            yesterday14.set(Calendar.MILLISECOND, 0);

            Calendar today14 = Calendar.getInstance();
            today14.set(Calendar.HOUR_OF_DAY, 14);
            today14.set(Calendar.MINUTE, 0);
            today14.set(Calendar.SECOND, 0);
            today14.set(Calendar.MILLISECOND, 0);

            // 过滤订单：只保留昨天14点到今天14点的订单，并按创建时间排序
            List<Order> filteredOrders = allOrders.stream()
                .filter(order -> {
                    if (order.getCreateTime() == null) {
                        return false;
                    }
                    if (order.getPayStatus().getValue() != 2) {
                        return false;
                    }
                    long orderTime = order.getCreateTime().getTime();
                    //return true;
                    return orderTime >= yesterday14.getTimeInMillis();
                    //return orderTime >= yesterday14.getTimeInMillis() && orderTime < today14.getTimeInMillis();
                })
                .sorted((o1, o2) -> {
                    // 按创建时间升序排序（最早的在前面）
                    if (o1.getCreateTime() == null && o2.getCreateTime() == null) {
                        return 0;
                    }
                    if (o1.getCreateTime() == null) {
                        return 1;
                    }
                    if (o2.getCreateTime() == null) {
                        return -1;
                    }
                    return o1.getCreateTime().compareTo(o2.getCreateTime());
                })
                .collect(Collectors.toList());

            log.info("过滤后订单数量: {}", filteredOrders.size());

            if (filteredOrders.isEmpty()) {
                log.info("在指定时间范围内没有找到任何订单数据");
                return;
            }

            // 创建Excel工作簿
            Workbook workbook = ExcelUtils.createWorkbook();
            Sheet sheet = workbook.createSheet("订单信息");

            // 创建样式
            CellStyle headerStyle = ExcelUtils.createHeaderStyle(workbook);
            CellStyle dataStyle = ExcelUtils.createDataStyle(workbook);
            CellStyle dateStyle = ExcelUtils.createDateStyle(workbook);

            // 创建上半场样式（绿色背景）
            CellStyle firstHalfStyle = workbook.createCellStyle();
            firstHalfStyle.setBorderBottom(BorderStyle.THIN);
            firstHalfStyle.setBorderLeft(BorderStyle.THIN);
            firstHalfStyle.setBorderRight(BorderStyle.THIN);
            firstHalfStyle.setBorderTop(BorderStyle.THIN);
            firstHalfStyle.setAlignment(HorizontalAlignment.LEFT);
            firstHalfStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            firstHalfStyle.setWrapText(true);
            firstHalfStyle.setFillForegroundColor(IndexedColors.LIGHT_GREEN.getIndex());
            firstHalfStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            // 创建下半场样式（红色背景）
            CellStyle secondHalfStyle = workbook.createCellStyle();
            secondHalfStyle.setBorderBottom(BorderStyle.THIN);
            secondHalfStyle.setBorderLeft(BorderStyle.THIN);
            secondHalfStyle.setBorderRight(BorderStyle.THIN);
            secondHalfStyle.setBorderTop(BorderStyle.THIN);
            secondHalfStyle.setAlignment(HorizontalAlignment.LEFT);
            secondHalfStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            secondHalfStyle.setWrapText(true);
            secondHalfStyle.setFillForegroundColor(IndexedColors.CORAL.getIndex());
            secondHalfStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            // 创建表头
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                "日期时间段",  "订单编号",
                "收货人", "收货地址", "收货电话",
                "商品信息(含规格)",
                "创建时间", "备注"
            };

            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                ExcelUtils.setCellValue(cell, headers[i], headerStyle);
            }

            // 填充数据
            int rowIndex = 1;
            for (Order order : filteredOrders) {
                try {
                    // 获取用户信息
                    User user = userService.queryById(order.getUserId());

                    // 获取订单详情
                    OrderDetailVO orderDetailVO = orderService.queryOrderDetail(order.getId());
                    List<OrderGoods> orderGoods = orderDetailVO.getOrderGoods();

                    // 判断订单时间段并选择样式
                    CellStyle rowStyle = dataStyle; // 默认样式
                    String timeSegment = "";

                    if (order.getCreateTime() != null) {
                        Calendar orderTime = Calendar.getInstance();
                        orderTime.setTime(order.getCreateTime());

                        Calendar todayMidnight = Calendar.getInstance();
                        todayMidnight.set(Calendar.HOUR_OF_DAY, 0);
                        todayMidnight.set(Calendar.MINUTE, 0);
                        todayMidnight.set(Calendar.SECOND, 0);
                        todayMidnight.set(Calendar.MILLISECOND, 0);

                        Calendar today14Clock = Calendar.getInstance();
                        today14Clock.set(Calendar.HOUR_OF_DAY, 14);
                        today14Clock.set(Calendar.MINUTE, 0);
                        today14Clock.set(Calendar.SECOND, 0);
                        today14Clock.set(Calendar.MILLISECOND, 0);

                        if (orderTime.getTimeInMillis() >= todayMidnight.getTimeInMillis() &&
                            orderTime.getTimeInMillis() < today14Clock.getTimeInMillis()) {
                            // 下半场：当日0点-14点，红色
                            rowStyle = secondHalfStyle;
                            timeSegment = "下半场(0-14点)";
                        } else {
                            // 上半场：前一日14点-24点，绿色
                            rowStyle = firstHalfStyle;
                            timeSegment = "上半场(14-24点)";
                        }
                    }

                    // 构建商品信息字符串（包含规格信息）
                    StringBuilder goodsInfo = new StringBuilder();
                    if (orderGoods != null && !orderGoods.isEmpty()) {
                        for (int i = 0; i < orderGoods.size(); i++) {
                            OrderGoods goods = orderGoods.get(i);
                            if (i > 0) goodsInfo.append("; ");

                            // 商品名称
                            goodsInfo.append(goods.getGoodsName());

                            // 根据goodsSpecificationIds获取规格信息
                            String specifications = getProductSpecifications(goods.getGoodsSpecificationIds());
                            if (!specifications.isEmpty()) {
                                goodsInfo.append(" [").append(specifications).append("]");
                            }

                            // 数量和价格
                            goodsInfo.append(" x").append(goods.getNumber())
                                    .append(" (¥").append(goods.getRetailPrice()).append(")");
                        }
                    }

                    // 构建完整地址
                    String fullAddress = "";
                    if (order.getProvince() != null && order.getCity() != null && order.getDistrict() != null) {
                        String provinceName = cache.region.get(order.getProvince());
                        String cityName = cache.region.get(order.getCity());
                        String districtName = cache.region.get(order.getDistrict());
                        fullAddress = ExcelUtils.safeString(provinceName) +
                                     ExcelUtils.safeString(cityName) +
                                     ExcelUtils.safeString(districtName) +
                                     ExcelUtils.safeString(order.getAddress());
                    }

                    Row dataRow = sheet.createRow(rowIndex++);
                    int cellIndex = 0;

                    // 填充每一列的数据，使用对应时间段的样式
                    ExcelUtils.setCellValue(dataRow.createCell(cellIndex++), timeSegment, rowStyle);
                    ExcelUtils.setCellValue(dataRow.createCell(cellIndex++), order.getOrderSn(), rowStyle);
                    ExcelUtils.setCellValue(dataRow.createCell(cellIndex++), order.getConsignee(), rowStyle);
                    ExcelUtils.setCellValue(dataRow.createCell(cellIndex++), fullAddress, rowStyle);
                    ExcelUtils.setCellValue(dataRow.createCell(cellIndex++), order.getMobile(), rowStyle);
                    ExcelUtils.setCellValue(dataRow.createCell(cellIndex++), goodsInfo.toString(), rowStyle);

                    // 时间字段使用特殊的日期样式，但保持背景色
                    CellStyle timeStyle = workbook.createCellStyle();
                    timeStyle.cloneStyleFrom(dateStyle);
                    if (rowStyle == firstHalfStyle) {
                        timeStyle.setFillForegroundColor(IndexedColors.LIGHT_GREEN.getIndex());
                        timeStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                    } else if (rowStyle == secondHalfStyle) {
                        timeStyle.setFillForegroundColor(IndexedColors.LIGHT_ORANGE.getIndex());
                        timeStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                    }

                    ExcelUtils.setCellValue(dataRow.createCell(cellIndex++), order.getCreateTime(), timeStyle);
                    ExcelUtils.setCellValue(dataRow.createCell(cellIndex++), order.getPostscript(), rowStyle);

                } catch (Exception e) {
                    log.error("处理订单 {} 时出错: {}", order.getId(), e.getMessage(), e);
                }
            }

            // 自动调整列宽
            ExcelUtils.autoSizeColumns(sheet, headers.length);

            // 保存文件
            String date = DateUtil.format(new Date(), "yyyyMMddHHmmss");
            String fileName = "订单导出_" + date + ".xlsx";
            String filePath = System.getProperty("user.dir") + "/" + fileName;
            ExcelUtils.saveWorkbook(workbook, filePath);

            // 关闭工作簿
            workbook.close();

            log.info("订单导出完成！");
            log.info("导出文件路径: {}", filePath);
            log.info("导出订单数量: {}", (rowIndex - 1));

        } catch (IOException e) {
            log.error("导出Excel文件时出错: {}", e.getMessage(), e);
        } catch (Exception e) {
            log.error("导出过程中出现异常: {}", e.getMessage(), e);
        }
    }
    /**
     * 根据商品规格ID字符串获取规格信息
     * @param goodsSpecificationIds 规格ID字符串，格式如 "1,2,3"
     * @return 规格信息字符串，格式如 "颜色:红色,尺寸:L"
     */
    private String getProductSpecifications(String goodsSpecificationIds) {
        if (goodsSpecificationIds == null || goodsSpecificationIds.trim().isEmpty()) {
            return "";
        }

        try {
            String[] specIds = goodsSpecificationIds.split(",");
            List<String> specTexts = new ArrayList<>();

            for (String specIdStr : specIds) {
                try {
                    Integer specId = Integer.parseInt(specIdStr.trim());
                    GoodsSpecification goodsSpec = goodsSpecificationService.queryById(specId);
                    if (goodsSpec != null) {
                        Specification spec = specificationService.findById(goodsSpec.getSpecificationId());
                        if (spec != null) {
                            specTexts.add(spec.getName() + ":" + goodsSpec.getValue());
                        }
                    }
                } catch (NumberFormatException e) {
                    log.warn("无效的规格ID: {}", specIdStr);
                }
            }

            return String.join(",", specTexts);
        } catch (Exception e) {
            log.error("获取商品规格信息时出错: {}", e.getMessage(), e);
            return "";
        }
    }

}