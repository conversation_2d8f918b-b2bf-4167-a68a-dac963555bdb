package com.logic.code.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.logic.code.config.CategoryProperties;
import com.logic.code.entity.file.FileCategory;
import com.logic.code.entity.file.FileAttachment;
import com.logic.code.mapper.FileCategoryMapper;
import com.logic.code.mapper.FileAttachmentMapper;
import com.logic.code.service.FileCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * File category service implementation
 */
@Service
public class FileCategoryServiceImpl implements FileCategoryService {

    @Autowired
    private FileCategoryMapper fileCategoryMapper;

    @Autowired
    private FileAttachmentMapper fileAttachmentMapper;

    @Autowired
    private CategoryProperties categoryProperties;

    @Override
    public List<FileCategory> getCategoryList(Integer pid) {
        QueryWrapper<FileCategory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("parent_id", pid);
        queryWrapper.eq("is_delete", false);
        queryWrapper.orderByAsc("sort_order", "id");
        List<FileCategory> categories = fileCategoryMapper.selectList(queryWrapper);

        // Set file count for each category
        for (FileCategory category : categories) {
            category.setFileCount(getFilesCountInCategory(category.getId()));
        }

        return categories;
    }

    @Override
    public List<FileCategory> getCategoryTree() {
        // Get all categories
        QueryWrapper<FileCategory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_delete", false);
        queryWrapper.orderByAsc("sort_order", "id");
        List<FileCategory> allCategories = fileCategoryMapper.selectList(queryWrapper);

        // Build tree
        List<FileCategory> rootCategories = allCategories.stream()
                .filter(category -> category.getParentId() == 0)
                .collect(Collectors.toList());

        // Recursively build tree
        buildCategoryTree(rootCategories, allCategories);

        return rootCategories;
    }

    private void buildCategoryTree(List<FileCategory> parentCategories, List<FileCategory> allCategories) {
        for (FileCategory parent : parentCategories) {
            List<FileCategory> children = allCategories.stream()
                    .filter(category -> parent.getId().equals(category.getParentId()))
                    .collect(Collectors.toList());

            if (!children.isEmpty()) {
                parent.setChildren(children);
                buildCategoryTree(children, allCategories);
            }

            // Set file count
            parent.setFileCount(getFilesCountInCategory(parent.getId()));
        }
    }

    /**
     * Get files count in category (including subcategories)
     */
    private Integer getFilesCountInCategory(Integer categoryId) {
        if (categoryId == null) {
            return 0;
        }

        try {
            QueryWrapper<FileAttachment> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("category_id", categoryId);
            queryWrapper.eq("is_delete", false);

            Long count = fileAttachmentMapper.selectCount(queryWrapper);
            return count != null ? count.intValue() : 0;
        } catch (Exception e) {
            // Log error and return 0 if there's any issue
            return 0;
        }
    }

    @Override
    public FileCategory getById(Integer id) {
        FileCategory category = fileCategoryMapper.selectById(id);
        if (category != null) {
            category.setFileCount(getFilesCountInCategory(id));
            // Set parent name if has parent
            if (category.getParentId() != null && category.getParentId() > 0) {
                FileCategory parent = fileCategoryMapper.selectById(category.getParentId());
                if (parent != null) {
                    category.setParentName(parent.getTitle());
                }
            }
        }
        return category;
    }

    @Override
    @Transactional
    public FileCategory createCategory(FileCategory category) {
        // Validate category name uniqueness under same parent
        if (existsByTitleAndParentId(category.getTitle(), category.getParentId(), null)) {
            throw new RuntimeException("同级分类名称已存在");
        }

        category.setCreateTime(new Date());
        category.setUpdateTime(new Date());
        category.setIsDelete(false);

        if (category.getParentId() == null) {
            category.setParentId(0);
            category.setLevel(0);
            category.setPath("0");
        } else {
            // Set level and path based on parent
            FileCategory parent = fileCategoryMapper.selectById(category.getParentId());
            if (parent != null) {
                int newLevel = parent.getLevel() + 1;
                // Check max level limit
                if (newLevel > categoryProperties.getMaxLevel()) {
                    throw new RuntimeException("分类层级不能超过 " + categoryProperties.getMaxLevel() + " 级");
                }
                category.setLevel(newLevel);
                category.setPath(parent.getPath() + "," + parent.getId());
            } else {
                category.setLevel(0);
                category.setPath("0");
            }
        }

        if (category.getSortOrder() == null) {
            // Get max sort order under same parent
            QueryWrapper<FileCategory> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("parent_id", category.getParentId());
            queryWrapper.eq("is_delete", false);
            queryWrapper.orderByDesc("sort_order");
            queryWrapper.last("LIMIT 1");
            FileCategory lastCategory = fileCategoryMapper.selectOne(queryWrapper);
            category.setSortOrder(lastCategory != null ? lastCategory.getSortOrder() + 1 : categoryProperties.getDefaultSortOrder());
        }

        fileCategoryMapper.insert(category);
        return category;
    }

    @Override
    @Transactional
    public boolean updateCategory(FileCategory category) {
        FileCategory existingCategory = fileCategoryMapper.selectById(category.getId());
        if (existingCategory == null) {
            return false;
        }

        // Validate category name uniqueness under same parent (exclude current category)
        if (existsByTitleAndParentId(category.getTitle(), category.getParentId(), category.getId())) {
            throw new RuntimeException("同级分类名称已存在");
        }

        // Update level and path if parent changed
        if (!existingCategory.getParentId().equals(category.getParentId())) {
            if (category.getParentId() == 0) {
                category.setLevel(0);
                category.setPath("0");
            } else {
                FileCategory parent = fileCategoryMapper.selectById(category.getParentId());
                if (parent != null) {
                    category.setLevel(parent.getLevel() + 1);
                    category.setPath(parent.getPath() + "," + parent.getId());
                }
            }

            // Update all descendant categories' level and path
            updateDescendantLevelAndPath(category.getId());
        }

        category.setUpdateTime(new Date());
        return fileCategoryMapper.updateById(category) > 0;
    }

    @Override
    @Transactional
    public boolean deleteCategory(Integer id) {
        // Check if category has children
        QueryWrapper<FileCategory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("parent_id", id);
        queryWrapper.eq("is_delete", false);
        long count = fileCategoryMapper.selectCount(queryWrapper);
        if (count > 0 && !categoryProperties.isAllowDeleteWithChildren()) {
            throw new RuntimeException("该分类下还有子分类，无法删除");
        }

        // Check if category has files
        Integer fileCount = getFilesCountInCategory(id);
        if (fileCount > 0 && !categoryProperties.isAllowDeleteWithFiles()) {
            throw new RuntimeException("该分类下还有文件，无法删除");
        }

        // Soft delete
        FileCategory category = fileCategoryMapper.selectById(id);
        if (category == null) {
            return false;
        }
        category.setIsDelete(true);
        category.setUpdateTime(new Date());
        return fileCategoryMapper.updateById(category) > 0;
    }

    @Override
    @Transactional
    public boolean batchDeleteCategories(List<Integer> ids) {
        for (Integer id : ids) {
            if (!deleteCategory(id)) {
                return false;
            }
        }
        return true;
    }

    @Override
    @Transactional
    public boolean moveCategory(Integer id, Integer newParentId) {
        FileCategory category = fileCategoryMapper.selectById(id);
        if (category == null) {
            return false;
        }

        // Check if moving to descendant (would create circular reference)
        if (isDescendant(newParentId, id)) {
            throw new RuntimeException("不能移动到子分类下");
        }

        // Update parent
        category.setParentId(newParentId);

        // Update level and path
        if (newParentId == 0) {
            category.setLevel(0);
            category.setPath("0");
        } else {
            FileCategory parent = fileCategoryMapper.selectById(newParentId);
            if (parent != null) {
                category.setLevel(parent.getLevel() + 1);
                category.setPath(parent.getPath() + "," + parent.getId());
            }
        }

        category.setUpdateTime(new Date());
        boolean result = fileCategoryMapper.updateById(category) > 0;

        if (result) {
            // Update all descendant categories' level and path
            updateDescendantLevelAndPath(id);
        }

        return result;
    }

    @Override
    @Transactional
    public boolean updateSortOrder(Integer id, Integer sortOrder) {
        FileCategory category = fileCategoryMapper.selectById(id);
        if (category == null) {
            return false;
        }

        category.setSortOrder(sortOrder);
        category.setUpdateTime(new Date());
        return fileCategoryMapper.updateById(category) > 0;
    }

    @Override
    public boolean existsByTitleAndParentId(String title, Integer parentId, Integer excludeId) {
        QueryWrapper<FileCategory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("title", title);
        queryWrapper.eq("parent_id", parentId);
        queryWrapper.eq("is_delete", false);
        if (excludeId != null) {
            queryWrapper.ne("id", excludeId);
        }
        return fileCategoryMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    public List<FileCategory> getCategoryPath(Integer id) {
        List<FileCategory> path = new ArrayList<>();
        FileCategory category = fileCategoryMapper.selectById(id);

        while (category != null && category.getParentId() != 0) {
            path.add(0, category);
            category = fileCategoryMapper.selectById(category.getParentId());
        }

        if (category != null) {
            path.add(0, category);
        }

        return path;
    }

    @Override
    public List<FileCategory> getDescendantCategories(Integer id) {
        List<FileCategory> descendants = new ArrayList<>();
        collectDescendants(id, descendants);
        return descendants;
    }

    private void collectDescendants(Integer parentId, List<FileCategory> descendants) {
        QueryWrapper<FileCategory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("parent_id", parentId);
        queryWrapper.eq("is_delete", false);
        List<FileCategory> children = fileCategoryMapper.selectList(queryWrapper);

        for (FileCategory child : children) {
            descendants.add(child);
            collectDescendants(child.getId(), descendants);
        }
    }

    @Override
    public FileCategory getCategoryWithStats(Integer id) {
        FileCategory category = fileCategoryMapper.selectById(id);
        if (category != null) {
            category.setFileCount(getFilesCountInCategory(id));
        }
        return category;
    }

    @Override
    public List<FileCategory> searchCategories(String title) {
        if (!StringUtils.hasText(title)) {
            return new ArrayList<>();
        }

        QueryWrapper<FileCategory> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("title", title);
        queryWrapper.eq("is_delete", false);
        queryWrapper.orderByAsc("sort_order", "id");
        List<FileCategory> categories = fileCategoryMapper.selectList(queryWrapper);

        // Set file count and parent name for each category
        for (FileCategory category : categories) {
            category.setFileCount(getFilesCountInCategory(category.getId()));
            if (category.getParentId() != null && category.getParentId() > 0) {
                FileCategory parent = fileCategoryMapper.selectById(category.getParentId());
                if (parent != null) {
                    category.setParentName(parent.getTitle());
                }
            }
        }

        return categories;
    }

    /**
     * Check if targetId is a descendant of ancestorId
     */
    private boolean isDescendant(Integer targetId, Integer ancestorId) {
        if (targetId == null || ancestorId == null || targetId.equals(ancestorId)) {
            return false;
        }

        FileCategory target = fileCategoryMapper.selectById(targetId);
        while (target != null && target.getParentId() != 0) {
            if (target.getParentId().equals(ancestorId)) {
                return true;
            }
            target = fileCategoryMapper.selectById(target.getParentId());
        }

        return false;
    }

    /**
     * Update level and path for all descendant categories
     */
    private void updateDescendantLevelAndPath(Integer parentId) {
        FileCategory parent = fileCategoryMapper.selectById(parentId);
        if (parent == null) {
            return;
        }

        QueryWrapper<FileCategory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("parent_id", parentId);
        queryWrapper.eq("is_delete", false);
        List<FileCategory> children = fileCategoryMapper.selectList(queryWrapper);

        for (FileCategory child : children) {
            child.setLevel(parent.getLevel() + 1);
            child.setPath(parent.getPath() + "," + parent.getId());
            child.setUpdateTime(new Date());
            fileCategoryMapper.updateById(child);

            // Recursively update descendants
            updateDescendantLevelAndPath(child.getId());
        }
    }
}
