package com.logic.code.service;

import com.logic.code.common.exception.WeshopWechatException;
import com.logic.code.common.utils.JwtHelper;
import com.logic.code.entity.Keywords;
import com.logic.code.entity.SearchHistory;
import com.logic.code.entity.User;
import com.logic.code.model.vo.SearchIndexVO;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/8 9:33
 * @desc
 */
@Service
@Slf4j
public class SearchService {
    @Autowired
    private KeywordsService keywordsService;

    @Autowired
    private SearchHistoryService searchHistoryService;


    public List<String> helper(String keyword) {
        return keywordsService.queryByKeyword(keyword);
    }

    public void clearHistory() {
        User userInfo = JwtHelper.getUserInfo();
        searchHistoryService.delete(new SearchHistory().setUserId(userInfo.getId()));
    }

    public SearchIndexVO index() {
        // 取出输入框默认的关键词
        Keywords defaultKeyword = keywordsService.queryOneByCriteria(Criteria.of(Keywords.class).andEqualTo(Keywords::getIsDefault, true).page(1, 1));
        // 取出热闹关键词
        List<Keywords> hotKeywordList = keywordsService.queryByCriteria(Criteria.of(Keywords.class).andEqualTo(Keywords::getHot, true).page(1, 10));
        List<String> historyKeywordList = Collections.emptyList();
        try {
            User userInfo = JwtHelper.getUserInfo();
            historyKeywordList = searchHistoryService.queryByCriteria(Criteria.of(SearchHistory.class).andEqualTo(SearchHistory::getUserId, userInfo.getId()).page(1, 10)).stream()
                    .map(SearchHistory::getKeyword)
                    .collect(Collectors.toList());
        } catch (WeshopWechatException e) {
            log.info("用户未登陆，不查询热闹关键词");
        }
        return new SearchIndexVO()
                .setDefaultKeyword(defaultKeyword)
                .setHotKeywordList(hotKeywordList)
                .setHistoryKeywordList(historyKeywordList);
    }
}
