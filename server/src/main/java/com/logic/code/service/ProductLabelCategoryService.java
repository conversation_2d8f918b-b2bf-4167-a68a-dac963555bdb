package com.logic.code.service;

import com.logic.code.entity.goods.ProductLabelCategory;
import com.logic.code.mapper.CommonMapper;
import com.logic.code.mapper.ProductLabelCategoryMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 商品标签分类服务
 */
@Service
public class ProductLabelCategoryService extends BaseService<ProductLabelCategory> {

    @Resource
    private ProductLabelCategoryMapper productLabelCategoryMapper;

    @Override
    protected CommonMapper<ProductLabelCategory> getMapper() {
        return productLabelCategoryMapper;
    }

    /**
     * 分页查询标签分类
     */
    public Map<String, Object> queryPage(Map<String, Object> params) {
        Integer page = (Integer) params.get("page");
        Integer limit = (Integer) params.get("limit");
        String name = (String) params.get("name");
        Integer status = (Integer) params.get("status");

        if (page == null || page < 1) page = 1;
        if (limit == null || limit < 1) limit = 10;

        Integer offset = (page - 1) * limit;

        List<Map<String, Object>> list = productLabelCategoryMapper.selectPageList(name, status, offset, limit);
        Long total = productLabelCategoryMapper.selectPageCount(name, status);

        Map<String, Object> result = new HashMap<>();
        result.put("list", list);
        result.put("count", total);
        result.put("page", page);
        result.put("limit", limit);

        return result;
    }

    /**
     * 获取所有启用的分类
     */
    public List<ProductLabelCategory> getEnabledList() {
        return productLabelCategoryMapper.selectEnabledList();
    }

    /**
     * 保存分类
     */
    public ProductLabelCategory saveCategory(ProductLabelCategory category) {
        if (category.getId() != null && category.getId() > 0) {
            // 更新
            updateById(category);
        } else {
            // 新增
            if (category.getSortOrder() == null) {
                category.setSortOrder(0);
            }
            if (category.getStatus() == null) {
                category.setStatus(1);
            }
            insert(category);
        }
        return category;
    }
}
