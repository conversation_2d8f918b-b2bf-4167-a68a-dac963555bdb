package com.logic.code.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logic.code.entity.goods.AttributeCategory;
import com.logic.code.mapper.AttributeCategoryMapper;
import com.logic.code.mapper.CommonMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/5
 * @desc 属性分类服务类
 */
@Service
public class AttributeCategoryService extends BaseService<AttributeCategory> {

    @Resource
    private AttributeCategoryMapper attributeCategoryMapper;

    @Override
    protected CommonMapper<AttributeCategory> getMapper() {
        return attributeCategoryMapper;
    }

    /**
     * 查询所有启用的属性分类
     */
    public List<AttributeCategory> queryAllEnabled() {
        return attributeCategoryMapper.selectAllEnabled();
    }

    /**
     * 根据名称模糊查询属性分类
     */
    public List<AttributeCategory> queryByNameLike(String name) {
        return attributeCategoryMapper.selectByNameLike(name);
    }

    /**
     * 分页查询属性分类
     */
    public Page<AttributeCategory> getPage(String name, Integer page, Integer limit) {
        Page<AttributeCategory> pageData = new Page<>(page, limit);
        QueryWrapper<AttributeCategory> queryWrapper = new QueryWrapper<>();

        if (StringUtils.hasText(name)) {
            queryWrapper.like("name", name);
        }

        queryWrapper.orderByAsc("id");
        return attributeCategoryMapper.selectPage(pageData, queryWrapper);
    }

    /**
     * 根据名称查询属性分类
     */
    public AttributeCategory queryByName(String name) {
        QueryWrapper<AttributeCategory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", name);
        return attributeCategoryMapper.selectOne(queryWrapper);
    }

    /**
     * 保存属性分类
     */
    public AttributeCategory save(AttributeCategory category) {
        attributeCategoryMapper.insert(category);
        return category;
    }

    /**
     * 更新属性分类
     */
    public AttributeCategory update(AttributeCategory category) {
        attributeCategoryMapper.updateById(category);
        return category;
    }
}
