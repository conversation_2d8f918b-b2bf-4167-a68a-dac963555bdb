package com.logic.code.service;

import com.logic.code.common.PageParamRequest;
import com.logic.code.model.income.IncomeDetailsRequest;
import com.logic.code.model.income.IncomeDetailsResponse;
import com.logic.code.model.income.IncomeStatisticsResponse;

/**
 * Income service interface
 */
public interface IncomeService {

    /**
     * Get user income statistics
     * @param userId user ID
     * @return income statistics
     */
    IncomeStatisticsResponse getUserIncomeStatistics(Integer userId);

    /**
     * Get user income details with pagination
     * @param request income details request
     * @param pageParamRequest pagination parameters
     * @return income details response
     */
    IncomeDetailsResponse getUserIncomeDetails(IncomeDetailsRequest request, PageParamRequest pageParamRequest);

    /**
     * Export user income details
     * @param request income details request
     * @return export file URL
     */
    String exportUserIncomeDetails(IncomeDetailsRequest request);
}
