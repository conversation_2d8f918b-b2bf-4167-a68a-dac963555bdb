package com.logic.code.service;

import com.logic.code.entity.order.Order;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.io.FileInputStream;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 订单信息更新服务
 * 用于根据Excel文件中的数据更新订单表的支付时间和交易单号
 */
@Slf4j
@Service
public class OrderUpdateService {

    @Resource
    private OrderService orderService;

    /**
     * 根据Excel文件更新订单信息
     * @param filePath Excel文件路径
     */
    public void updateOrdersFromExcel(String filePath) {
        try (FileInputStream fis = new FileInputStream(filePath);
             Workbook workbook = new XSSFWorkbook(fis)) {

            Sheet sheet = workbook.getSheetAt(0); // 获取第一个工作表
            int rowCount = sheet.getPhysicalNumberOfRows();
            
            log.info("开始处理Excel文件，总行数: {}", rowCount);
            
            // 跳过标题行，从第二行开始处理数据
            for (int i = 1; i < rowCount; i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;
                
                try {
                    // 读取行数据
                    String payTimeStr = getCellValueAsString(row.getCell(0)); // 支付时间
                    String transactionId = getCellValueAsString(row.getCell(1)); // 交易单号
                    String outTradeNo = getCellValueAsString(row.getCell(2)); // 商户单号
                    
                    // 更新订单信息
                    updateOrderInfo(payTimeStr, transactionId, outTradeNo);
                } catch (Exception e) {
                    log.error("处理第{}行数据时出错: {}", i + 1, e.getMessage(), e);
                }
            }
            
            log.info("Excel文件处理完成");
        } catch (IOException e) {
            log.error("读取Excel文件失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 更新单个订单信息
     * @param payTimeStr 支付时间字符串
     * @param transactionId 交易单号
     * @param outTradeNo 商户单号
     */
    private void updateOrderInfo(String payTimeStr, String transactionId, String outTradeNo) {
        if (outTradeNo == null || outTradeNo.isEmpty()) {
            log.warn("商户单号为空，跳过处理");
            return;
        }
        
        try {
            // 根据商户单号查询订单
            Order order = orderService.getByOutTradeNo(outTradeNo);
            if (order == null) {
                log.warn("未找到商户单号为{}的订单", outTradeNo);
                return;
            }
            
            // 更新订单信息
            boolean needUpdate = false;
            
            // 更新支付时间
            if (payTimeStr != null && !payTimeStr.isEmpty()) {
                Date payTime = parseDate(payTimeStr);
                if (payTime != null) {
                    order.setPayTime(payTime);
                    needUpdate = true;
                }
            }
            
            // 更新交易单号
            if (transactionId != null && !transactionId.isEmpty()) {
                order.setTransactionId(transactionId);
                needUpdate = true;
            }
            
            // 执行更新操作
            if (needUpdate) {
                boolean updated = orderService.updateNotNull(order) == 1;
                if (updated) {
                    log.info("订单{}更新成功", outTradeNo);
                } else {
                    log.error("订单{}更新失败", outTradeNo);
                }
            } else {
                log.info("订单{}无需更新", outTradeNo);
            }
        } catch (Exception e) {
            log.error("更新订单{}信息时出错: {}", outTradeNo, e.getMessage(), e);
        }
    }
    
    /**
     * 获取单元格的字符串值
     * @param cell 单元格
     * @return 字符串值
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return null;
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    return sdf.format(cell.getDateCellValue());
                } else {
                    // 数值类型转换为字符串
                    return String.valueOf((long) cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return null;
        }
    }
    
    /**
     * 解析日期字符串
     * @param dateStr 日期字符串
     * @return Date对象
     */
    private Date parseDate(String dateStr) {
        if (dateStr == null || dateStr.isEmpty()) {
            return null;
        }
        
        // 尝试多种日期格式
        String[] patterns = {
            "yyyy-MM-dd HH:mm:ss",
            "yyyy/MM/dd HH:mm:ss",
            "yyyy-MM-dd",
            "yyyy/MM/dd"
        };
        
        for (String pattern : patterns) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat(pattern);
                return sdf.parse(dateStr);
            } catch (ParseException e) {
                // 继续尝试下一种格式
            }
        }
        
        log.warn("无法解析日期字符串: {}", dateStr);
        return null;
    }
}