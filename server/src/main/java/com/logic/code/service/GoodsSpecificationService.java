package com.logic.code.service;

import com.logic.code.entity.goods.GoodsSpecification;
import com.logic.code.mapper.CommonMapper;
import com.logic.code.mapper.GoodsSpecificationMapper;
import com.logic.code.model.dto.GoodsSpecificationDTO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/8 11:22
 * @desc
 */
@Service
public class GoodsSpecificationService extends BaseService<GoodsSpecification> {

    @Resource
    private GoodsSpecificationMapper goodsSpecificationMapper;

    @Override
    protected CommonMapper<GoodsSpecification> getMapper() {
        return goodsSpecificationMapper;
    }

    public List<GoodsSpecificationDTO> queryGoodsDetailSpecificationByGoodsId(Integer goodsId) {
        return goodsSpecificationMapper.selectGoodsDetailSpecificationByGoodsId(goodsId);
    }

    public List<String> queryValueByGoodsIdAndIdIn(Integer goodsId, List<Integer> goodsSpecificationIds) {
        return goodsSpecificationMapper.selectValueByGoodsIdAndIdIn(goodsId, goodsSpecificationIds);
    }

    /**
     * Query specifications by goods ID
     */
    public List<GoodsSpecification> queryByGoodsId(Integer goodsId) {
        GoodsSpecification query = new GoodsSpecification();
        query.setGoodsId(goodsId);
        return queryList(query);
    }

    /**
     * Delete specifications by goods ID
     */
    public void deleteByGoodsId(Integer goodsId) {
        GoodsSpecification query = new GoodsSpecification();
        query.setGoodsId(goodsId);
        delete(query);
    }

    /**
     * Create multiple specifications in batch
     */
}
