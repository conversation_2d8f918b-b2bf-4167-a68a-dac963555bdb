package com.logic.code.service;

import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 优惠券配置服务
 * <AUTHOR>
 * @date 2025/7/25
 */
@Service
public class CouponConfigService {
    
    /**
     * 获取优惠券使用规则配置
     * @return 配置信息
     */
    public Map<String, Object> getCouponRules() {
        Map<String, Object> rules = new HashMap<>();
        
        // 10元券使用规则
        Map<String, Object> coupon10Rules = new HashMap<>();
        coupon10Rules.put("amount", 10);
        coupon10Rules.put("minAmount", 50);
        coupon10Rules.put("description", "满50元可用");
        
        // 20元券使用规则
        Map<String, Object> coupon20Rules = new HashMap<>();
        coupon20Rules.put("amount", 20);
        coupon20Rules.put("minAmount", 100);
        coupon20Rules.put("description", "满100元可用");
        
        rules.put("coupon_10", coupon10Rules);
        rules.put("coupon_20", coupon20Rules);
        
        // 全局规则
        rules.put("maxCouponsPerOrder", 1); // 每单最多使用1张优惠券
        rules.put("canCombineWithBalance", true); // 可以与余额组合使用
        
        return rules;
    }
    
    /**
     * 检查优惠券是否可用
     * @param couponAmount 优惠券金额
     * @param minAmount 最低消费金额
     * @param orderAmount 订单金额
     * @return 是否可用
     */
    public boolean isCouponUsable(BigDecimal couponAmount, BigDecimal minAmount, BigDecimal orderAmount) {
        return orderAmount.compareTo(minAmount) >= 0;
    }
    
    /**
     * 更新优惠券使用规则（可配置）
     * @param couponType 优惠券类型
     * @param minAmount 最低消费金额
     */
    public void updateCouponRule(String couponType, BigDecimal minAmount) {
        // 这里可以实现动态配置功能
        // 例如存储到数据库或配置文件中
        // 暂时使用硬编码规则
    }
}