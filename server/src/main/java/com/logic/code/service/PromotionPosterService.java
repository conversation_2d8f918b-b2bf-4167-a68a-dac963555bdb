package com.logic.code.service;

import com.logic.code.entity.CompanyConfig;
import com.logic.code.entity.PromotionPosterConfig;
import com.logic.code.mapper.CompanyConfigMapper;
import com.logic.code.mapper.PromotionPosterConfigMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 推广海报服务类
 * 
 * <AUTHOR>
 * @date 2025/09/03
 */
@Service
@Slf4j
public class PromotionPosterService {
    
    @Autowired
    private PromotionPosterConfigMapper posterConfigMapper;
    
    @Autowired
    private CompanyConfigMapper companyConfigMapper;
    
    /**
     * 获取海报配置信息
     * @return 海报配置信息
     */
    public Map<String, Object> getPosterConfig() {
        try {
            Map<String, Object> result = new HashMap<>();
            
            // 获取默认海报模板配置
            PromotionPosterConfig defaultConfig = posterConfigMapper.selectDefaultTemplate();
            if (defaultConfig == null) {
                // 如果没有默认配置，创建一个基础配置
                defaultConfig = createDefaultConfig();
            }
            
            // 获取所有启用的模板列表
            List<PromotionPosterConfig> templates = posterConfigMapper.selectActiveTemplates();
            
            // 获取公司信息配置
            CompanyConfig companyConfig = companyConfigMapper.selectActiveConfig();
            if (companyConfig == null) {
                companyConfig = createDefaultCompanyConfig();
            }
            
            // 构建返回数据
            Map<String, Object> config = new HashMap<>();
            config.put("title", defaultConfig.getTitle());
            config.put("description", defaultConfig.getDescription());
            config.put("backgroundImage", defaultConfig.getBackgroundImage());
            config.put("logoImage", defaultConfig.getLogoImage());
            config.put("defaultTemplateId", defaultConfig.getId());
            
            Map<String, Object> company = new HashMap<>();
            company.put("name", companyConfig.getCompanyName());
            company.put("address", companyConfig.getCompanyAddress());
            company.put("phone", companyConfig.getContactPhone());
            company.put("wechat", companyConfig.getContactWechat());
            company.put("logo", companyConfig.getCompanyLogo());
            company.put("workTime", companyConfig.getWorkTime());
            company.put("description", companyConfig.getDescription());
            
            result.put("config", config);
            result.put("templates", templates);
            result.put("company", company);
            
            log.info("获取海报配置成功，模板数量: {}", templates.size());
            return result;
            
        } catch (Exception e) {
            log.error("获取海报配置失败", e);
            throw new RuntimeException("获取海报配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建默认海报配置
     */
    private PromotionPosterConfig createDefaultConfig() {
        PromotionPosterConfig config = new PromotionPosterConfig();
        config.setId(1);
        config.setName("默认模板");
        config.setTitle("伍俊惠选");
        config.setDescription("精选优质商品，品质生活选择");
        config.setBackgroundImage("/static/images/poster/default-bg.jpg");
        config.setLogoImage("/static/images/logo.png");
        config.setThumbnailUrl("/static/images/poster/thumb-default.jpg");
        config.setTemplateType("default");
        config.setIsDefault(true);
        config.setIsActive(true);
        config.setSortOrder(1);
        return config;
    }
    
    /**
     * 创建默认公司配置
     */
    private CompanyConfig createDefaultCompanyConfig() {
        CompanyConfig config = new CompanyConfig();
        config.setId(1);
        config.setCompanyName("伍俊惠选");
        config.setCompanyAddress("陕西省西安市雁塔区科技路");
        config.setContactPhone("136 2929 5757");
        config.setContactWechat("WJSY-029-888");
        config.setCompanyLogo("/static/images/logo.png");
        config.setWorkTime("工作时间：7:00-24:00");
        config.setDescription("专注于为用户提供优质商品和服务的电商平台");
        config.setIsActive(true);
        return config;
    }
}
