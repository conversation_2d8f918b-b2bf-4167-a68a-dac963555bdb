package com.logic.code.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logic.code.common.utils.JwtHelper;
import com.logic.code.entity.Admin;
import com.logic.code.mapper.AdminMapper;
import com.logic.code.model.dto.AdminFormResponse;
import com.logic.code.model.dto.AdminRequest;
import com.logic.code.model.dto.AdminResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 管理员服务类
 * 
 * <AUTHOR>
 * @date 2025/1/27
 */
@Service
@Slf4j
public class AdminService extends BaseService<Admin> {

    @Resource
    private AdminMapper adminMapper;
    
    @Resource
    private SystemMenuService systemMenuService;
    
    @Resource
    private MenuPermissionService menuPermissionService;
    
    /**
     * Token黑名单，用于存储已退出的token
     * 在生产环境中建议使用Redis等缓存方案
     */
    private final Set<String> tokenBlacklist = ConcurrentHashMap.newKeySet();

    @Override
    protected AdminMapper getMapper() {
        return adminMapper;
    }

    /**
     * 管理员登录验证
     * 
     * @param username 用户名
     * @param password 密码
     * @return 登录结果
     */
    public Map<String, Object> adminLogin(String username, String password) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 根据用户名查询管理员
            QueryWrapper<Admin> wrapper = new QueryWrapper<>();
            wrapper.eq("username", username);
            Admin admin = adminMapper.selectOne(wrapper);
            
            if (admin == null) {
                result.put("success", false);
                result.put("message", "用户名不存在");
                return result;
            }
            
            // 验证密码
            String encryptedPassword = encryptPassword(password, admin.getPasswordSalt());
            if (!encryptedPassword.equals(admin.getPassword())) {
                result.put("success", false);
                result.put("message", "密码错误");
                return result;
            }
            
            // 更新最后登录信息
            admin.setLastLoginTime(new Date());
            // 这里可以设置获取客户端IP的逻辑
            // admin.setLastLoginIp(getClientIp());
            adminMapper.updateById(admin);
            
            // 生成JWT token
            Map<String, Object> tokenPayload = new HashMap<>();
            tokenPayload.put("id", admin.getId());
            tokenPayload.put("username", admin.getUsername());
            tokenPayload.put("type", "admin");
            
            String token = JwtHelper.createJWT("admin", 
                com.logic.code.common.utils.JsonUtils.toJson(tokenPayload), 
                7 * 24 * 60 * 60 * 1000L); // 7天过期
            
            result.put("success", true);
            result.put("token", token);
            result.put("admin", admin);
            
            return result;
            
        } catch (Exception e) {
            log.error("管理员登录失败", e);
            result.put("success", false);
            result.put("message", "登录失败：" + e.getMessage());
            return result;
        }
    }

    /**
     * 获取管理员完整登录响应数据
     * 
     * @param admin 管理员信息
     * @param token JWT token
     * @return 完整的登录响应数据
     */
    public Map<String, Object> buildLoginResponse(Admin admin, String token) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 设置token和过期时间
            response.put("token", token);
            response.put("expires_time", System.currentTimeMillis() / 1000 + (7 * 24 * 60 * 60)); // 7天后过期
            
            // 从数据库获取菜单数据
            response.put("menus", systemMenuService.getMenuTree());
            
            // 添加管理员用户信息
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("id", admin.getId());
            userInfo.put("account", admin.getUsername());
            userInfo.put("head_pic", admin.getAvatar() != null ? admin.getAvatar() : 
                "https://v5.crme222b.net/uploads/attach/2023/09/********/f41769bef07f62a2d3d5e876aba2eb4f.png");
            userInfo.put("level", 1);
            userInfo.put("real_name", admin.getUsername());
            response.put("user_info", userInfo);
            
            // 添加系统配置信息
            response.put("logo", "https://v5.crmeb.net/statics/system_images/admin_logo_big.png");
            response.put("logo_square", "https://v5.crmeb.net/statics/system_images/admin_logo_small.png");
            response.put("version", "WJSY-SHOP v1.0.0");
            response.put("newOrderAudioLink", "");
            response.put("queue", true);
            response.put("timer", false);
            response.put("site_name", "WJSY商城管理系统");
            
            // 添加站点功能配置
            response.put("site_func", java.util.Arrays.asList("seckill", "bargain", "combination"));
            
            // 获取所有权限列表
            response.put("unique_auth", getAllPermissions());
            
            log.info("管理员登录成功，用户：{}", admin.getUsername());
            
        } catch (Exception e) {
            log.error("构建登录响应数据失败", e);
            throw new RuntimeException("构建登录响应数据失败：" + e.getMessage());
        }
        
        return response;
    }

    /**
     * 获取所有权限列表
     * 
     * @return 权限列表
     */
    private java.util.List<String> getAllPermissions() {
        try {
            // 获取所有菜单的权限
            java.util.List<com.logic.code.entity.system.MenuPermission> permissions = 
                menuPermissionService.getAllPermissions();
            
            return permissions.stream()
                .map(com.logic.code.entity.system.MenuPermission::getPermission)
                .distinct()
                .collect(java.util.stream.Collectors.toList());
                
        } catch (Exception e) {
            log.warn("获取权限列表失败，使用默认权限", e);
            // 返回基础权限列表
            return java.util.Arrays.asList(
                "admin-product", "admin-order", "admin-user", "admin-setting",
                "admin-system", "admin-marketing", "admin-finance", "admin-cms"
            );
        }
    }

    /**
     * 默认的管理员登录方法（无需用户名密码验证）
     * 用于测试和开发环境
     * 
     * @return 登录响应数据
     */
    public Map<String, Object> defaultAdminLogin() {
        try {
            // 创建默认管理员信息
            Admin defaultAdmin = new Admin();
            defaultAdmin.setId(1);
            defaultAdmin.setUsername("admin");
            
            // 生成token
            Map<String, Object> tokenPayload = new HashMap<>();
            tokenPayload.put("id", 1);
            tokenPayload.put("username", "admin");
            tokenPayload.put("type", "admin");
            
            String token = JwtHelper.createJWT("admin", 
                com.logic.code.common.utils.JsonUtils.toJson(tokenPayload), 
                7 * 24 * 60 * 60 * 1000L);
            
            return buildLoginResponse(defaultAdmin, token);
            
        } catch (Exception e) {
            log.error("默认管理员登录失败", e);
            throw new RuntimeException("登录失败：" + e.getMessage());
        }
    }

    /**
     * 密码加密
     * 
     * @param password 原始密码
     * @param salt 盐值
     * @return 加密后的密码
     */
    private String encryptPassword(String password, String salt) {
        return DigestUtils.md5DigestAsHex((password + salt).getBytes());
    }

    /**
     * 验证管理员权限
     * 
     * @param token JWT token
     * @return 是否为管理员
     */
    public boolean isAdmin(String token) {
        try {
            if (token == null || token.isEmpty()) {
                return false;
            }
            
            // 检查token是否在黑名单中
            if (isTokenBlacklisted(token)) {
                log.warn("尝试使用已退出的token: {}", token.substring(0, Math.min(20, token.length())) + "...");
                return false;
            }
            
            io.jsonwebtoken.Claims claims = JwtHelper.parseJWT(token);
            String subject = claims.getSubject();
            
            if (subject == null) {
                return false;
            }
            
            Map<String, Object> tokenData = com.logic.code.common.utils.JsonUtils.toObject(subject, Map.class);
            return "admin".equals(tokenData.get("type"));
            
        } catch (Exception e) {
            log.warn("验证管理员权限失败", e);
            return false;
        }
    }
    
    /**
     * 将token加入黑名单
     * 
     * @param token JWT token
     */
    public void addTokenToBlacklist(String token) {
        if (token != null && !token.isEmpty()) {
            tokenBlacklist.add(token);
            log.info("已将token加入黑名单: {}", token.substring(0, Math.min(20, token.length())) + "...");
        }
    }
    
    /**
     * 检查token是否在黑名单中
     * 
     * @param token JWT token
     * @return 是否在黑名单中
     */
    public boolean isTokenBlacklisted(String token) {
        return token != null && tokenBlacklist.contains(token);
    }
    
    /**
     * 清理过期的token黑名单
     * 建议定时执行此方法清理内存
     */
    public void cleanExpiredTokensFromBlacklist() {
        tokenBlacklist.removeIf(token -> {
            try {
                JwtHelper.parseJWT(token);
                return false; // token仍然有效，不移除
            } catch (Exception e) {
                return true; // token已过期，移除
            }
        });
        log.info("已清理过期的token黑名单，当前黑名单大小: {}", tokenBlacklist.size());
    }
    
    /**
     * 分页查询管理员列表
     * 
     * @param request 查询请求参数
     * @return 分页结果
     */
    public Map<String, Object> getAdminList(AdminRequest request) {
        try {
            // 构建查询条件
            QueryWrapper<Admin> wrapper = new QueryWrapper<>();
            
            // 状态筛选
            if (request.getStatus() != null) {
                wrapper.eq("status", request.getStatus());
            }
            
            // 姓名或账号搜索
            if (StringUtils.hasText(request.getName())) {
                wrapper.and(qw -> qw.like("username", request.getName())
                        .or().like("real_name", request.getName()));
            }
            
            // 分页查询
            Page<Admin> page = new Page<>(request.getPage(), request.getLimit());
            IPage<Admin> pageResult = adminMapper.selectPage(page, wrapper);
            
            // 转换为响应格式
            List<AdminResponse> adminList = new ArrayList<>();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            
            for (Admin admin : pageResult.getRecords()) {
                AdminResponse response = convertToResponse(admin, dateFormat);
                adminList.add(response);
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("list", adminList);
            result.put("count", pageResult.getTotal());
            result.put("page", request.getPage());
            result.put("limit", request.getLimit());
            
            return result;
            
        } catch (Exception e) {
            log.error("查询管理员列表失败", e);
            throw new RuntimeException("查询管理员列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取新增管理员表单数据
     * 
     * @return 表单数据
     */
    public AdminFormResponse getCreateFormData() {
        AdminFormResponse response = new AdminFormResponse();
        
        // 设置角色选项
        List<Map<String, Object>> roles = Arrays.asList(
            createOption("1", "超级管理员"),
            createOption("2", "普通管理员"),
            createOption("3", "客服")
        );
        response.setRoles(roles);
        
        // 设置状态选项
        List<Map<String, Object>> statusOptions = Arrays.asList(
            createOption("1", "启用"),
            createOption("0", "禁用")
        );
        response.setStatusOptions(statusOptions);
        
        return response;
    }
    
    /**
     * 获取编辑管理员表单数据
     * 
     * @param id 管理员ID
     * @return 表单数据
     */
    public AdminFormResponse getEditFormData(Integer id) {
        try {
            Admin admin = adminMapper.selectById(id);
            if (admin == null) {
                throw new RuntimeException("管理员不存在");
            }
            
            AdminFormResponse response = getCreateFormData();
            
            // 设置管理员信息
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            AdminResponse adminResponse = convertToResponse(admin, dateFormat);
            response.setAdmin(adminResponse);
            
            return response;
            
        } catch (Exception e) {
            log.error("获取管理员编辑表单数据失败，ID: {}", id, e);
            throw new RuntimeException("获取管理员信息失败：" + e.getMessage());
        }
    }
    
    /**
     * 创建管理员
     * 
     * @param request 管理员请求数据
     * @return 创建结果
     */
    public Map<String, Object> createAdmin(AdminRequest request) {
        try {
            // 检查用户名是否已存在
            QueryWrapper<Admin> wrapper = new QueryWrapper<>();
            wrapper.eq("username", request.getUsername());
            if (adminMapper.selectOne(wrapper) != null) {
                throw new RuntimeException("用户名已存在");
            }
            
            // 创建管理员实体
            Admin admin = new Admin();
            admin.setUsername(request.getUsername());
            admin.setRealName(request.getRealName());
            admin.setAvatar(request.getAvatar());
            admin.setAdminRoleId(request.getAdminRoleId());
            admin.setStatus(request.getStatus() != null ? request.getStatus() : 1);
            admin.setRoles(request.getRoles());
            admin.setCreateTime(new Date());
            admin.setUpdateTime(new Date());
            
            // 设置密码
            if (StringUtils.hasText(request.getPassword())) {
                String salt = generateSalt();
                admin.setPasswordSalt(salt);
                admin.setPassword(encryptPassword(request.getPassword(), salt));
            }
            
            // 保存到数据库
            adminMapper.insert(admin);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "创建管理员成功");
            result.put("id", admin.getId());
            
            return result;
            
        } catch (Exception e) {
            log.error("创建管理员失败", e);
            throw new RuntimeException("创建管理员失败：" + e.getMessage());
        }
    }
    
    /**
     * 更新管理员
     * 
     * @param id 管理员ID
     * @param request 管理员请求数据
     * @return 更新结果
     */
    public Map<String, Object> updateAdmin(Integer id, AdminRequest request) {
        try {
            Admin admin = adminMapper.selectById(id);
            if (admin == null) {
                throw new RuntimeException("管理员不存在");
            }
            
            // 如果修改了用户名，检查是否已存在
            if (!admin.getUsername().equals(request.getUsername())) {
                QueryWrapper<Admin> wrapper = new QueryWrapper<>();
                wrapper.eq("username", request.getUsername());
                wrapper.ne("id", id);
                if (adminMapper.selectOne(wrapper) != null) {
                    throw new RuntimeException("用户名已存在");
                }
            }
            
            // 更新字段
            admin.setUsername(request.getUsername());
            admin.setRealName(request.getRealName());
            admin.setAvatar(request.getAvatar());
            admin.setAdminRoleId(request.getAdminRoleId());
            admin.setStatus(request.getStatus());
            admin.setRoles(request.getRoles());
            admin.setUpdateTime(new Date());
            
            // 更新密码（如果提供了新密码）
            if (StringUtils.hasText(request.getPassword())) {
                String salt = generateSalt();
                admin.setPasswordSalt(salt);
                admin.setPassword(encryptPassword(request.getPassword(), salt));
            }
            
            // 保存到数据库
            adminMapper.updateById(admin);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "更新管理员成功");
            
            return result;
            
        } catch (Exception e) {
            log.error("更新管理员失败，ID: {}", id, e);
            throw new RuntimeException("更新管理员失败：" + e.getMessage());
        }
    }
    
    /**
     * 删除管理员
     * 
     * @param id 管理员ID
     * @return 删除结果
     */
    public Map<String, Object> deleteAdmin(Integer id) {
        try {
            Admin admin = adminMapper.selectById(id);
            if (admin == null) {
                throw new RuntimeException("管理员不存在");
            }
            
            // 不能删除自己（这里简单检查ID为1的超级管理员）
            if (id == 1) {
                throw new RuntimeException("不能删除超级管理员");
            }
            
            // 删除管理员
            adminMapper.deleteById(id);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "删除管理员成功");
            
            return result;
            
        } catch (Exception e) {
            log.error("删除管理员失败，ID: {}", id, e);
            throw new RuntimeException("删除管理员失败：" + e.getMessage());
        }
    }
    
    /**
     * 修改管理员状态
     * 
     * @param id 管理员ID
     * @param status 状态值
     * @return 修改结果
     */
    public Map<String, Object> updateAdminStatus(Integer id, Integer status) {
        try {
            Admin admin = adminMapper.selectById(id);
            if (admin == null) {
                throw new RuntimeException("管理员不存在");
            }
            
            admin.setStatus(status);
            admin.setUpdateTime(new Date());
            adminMapper.updateById(admin);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", status == 1 ? "启用管理员成功" : "禁用管理员成功");
            
            return result;
            
        } catch (Exception e) {
            log.error("修改管理员状态失败，ID: {}, status: {}", id, status, e);
            throw new RuntimeException("修改管理员状态失败：" + e.getMessage());
        }
    }
    
    /**
     * 将Admin实体转换为AdminResponse
     * 
     * @param admin 管理员实体
     * @param dateFormat 日期格式化器
     * @return AdminResponse
     */
    private AdminResponse convertToResponse(Admin admin, SimpleDateFormat dateFormat) {
        AdminResponse response = new AdminResponse();
        response.setId(admin.getId());
        response.setAccount(admin.getUsername());
        response.setRealName(admin.getRealName());
        response.setAvatar(admin.getAvatar());
        response.setAdminRoleId(admin.getAdminRoleId());
        response.setStatus(admin.getStatus() != null ? admin.getStatus() : 1);
        response.setRoles(admin.getRoles() != null ? admin.getRoles() : "普通管理员");
        response.setLastIp(admin.getLastLoginIp());
        
        // 格式化时间
        if (admin.getLastLoginTime() != null) {
            String formattedTime = dateFormat.format(admin.getLastLoginTime());
            response.setLastTime(formattedTime);
            response.set_last_time(formattedTime);
        }
        
        return response;
    }
    
    /**
     * 创建选项映射
     * 
     * @param value 值
     * @param label 标签
     * @return 选项映射
     */
    private Map<String, Object> createOption(String value, String label) {
        Map<String, Object> option = new HashMap<>();
        option.put("value", value);
        option.put("label", label);
        return option;
    }
    
    /**
     * 生成随机盐值
     * 
     * @return 盐值
     */
    private String generateSalt() {
        return "salt" + System.currentTimeMillis();
    }
}