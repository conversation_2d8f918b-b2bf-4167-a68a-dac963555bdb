package com.logic.code.service;

import com.logic.code.entity.goods.GoodsLabel;
import com.logic.code.entity.goods.ProductLabel;
import com.logic.code.mapper.CommonMapper;
import com.logic.code.mapper.GoodsLabelMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 商品标签关联服务
 */
@Service
public class GoodsLabelService extends BaseService<GoodsLabel> {

    @Resource
    private GoodsLabelMapper goodsLabelMapper;

    @Override
    protected CommonMapper<GoodsLabel> getMapper() {
        return goodsLabelMapper;
    }

    /**
     * 根据商品ID查询关联的标签
     */
    public List<ProductLabel> getLabelsByGoodsId(Integer goodsId) {
        return goodsLabelMapper.selectLabelsByGoodsId(goodsId);
    }

    /**
     * 更新商品标签关联
     */
    @Transactional
    public void updateGoodsLabels(Integer goodsId, List<Integer> labelIds) {
        // 先删除原有关联
        goodsLabelMapper.deleteByGoodsId(goodsId);
        
        // 如果有新的标签，则插入
        if (labelIds != null && !labelIds.isEmpty()) {
            for (Integer labelId : labelIds) {
                GoodsLabel goodsLabel = new GoodsLabel();
                goodsLabel.setGoodsId(goodsId);
                goodsLabel.setLabelId(labelId);
                insert(goodsLabel);
            }
        }
    }

    /**
     * 根据商品ID删除关联
     */
    public int deleteByGoodsId(Integer goodsId) {
        return goodsLabelMapper.deleteByGoodsId(goodsId);
    }

    /**
     * 根据标签ID删除关联
     */
    public int deleteByLabelId(Integer labelId) {
        return goodsLabelMapper.deleteByLabelId(labelId);
    }
}
