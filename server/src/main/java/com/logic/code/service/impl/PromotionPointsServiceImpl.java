package com.logic.code.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.logic.code.common.response.Result;
import com.logic.code.entity.PromotionLevelConfig;
import com.logic.code.entity.PromotionPointsRecord;
import com.logic.code.entity.User;
import com.logic.code.mapper.PromotionLevelConfigMapper;
import com.logic.code.mapper.PromotionPointsRecordMapper;
import com.logic.code.mapper.UserMapper;
import com.logic.code.model.vo.PromotionPointsRecordVO;
import com.logic.code.model.vo.PromotionPointsStatsVO;
import com.logic.code.service.PromotionPointsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 推广积分服务实现
 */
@Service
public class PromotionPointsServiceImpl implements PromotionPointsService {
    
  /*  @Autowired
    private PromotionPointsRecordMapper promotionPointsRecordMapper;
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private PromotionLevelConfigMapper promotionLevelConfigMapper;
    
    @Override
    public ResponseUtil getPromotionPointsRecords(Integer userId, Integer page, Integer size) {
        try {
            // 计算偏移量
            int offset = (page - 1) * size;
            
            // 获取推广积分记录
            List<PromotionPointsRecord> records = promotionPointsRecordMapper.getPromoterRecords(userId, offset, size);
            
            // 转换为VO
            List<PromotionPointsRecordVO> recordVOs = records.stream().map(record -> {
                PromotionPointsRecordVO vo = new PromotionPointsRecordVO();
                vo.setId(record.getId())
                  .setPoints(record.getRewardPoints())
                  .setType("promotion")
                  .setPromotionLevel(record.getPromotionLevel())
                  .setCreateTime(record.getPromotionTime());
                
                // 获取被推广用户信息
                User promotedUser = userMapper.selectById(record.getPromotedUserId());
                if (promotedUser != null) {
                    vo.setPromotedUserNickname(promotedUser.getNickname())
                      .setPromotedUserAvatar(promotedUser.getAvatar());
                    vo.setDescription("推广用户 " + promotedUser.getNickname() + " 获得积分奖励");
                } else {
                    vo.setDescription("推广用户获得积分奖励");
                }
                
                // 获取等级信息
                PromotionLevelConfig levelConfig = promotionLevelConfigMapper.selectOne(
                    new QueryWrapper<PromotionLevelConfig>().eq("level", record.getPromotionLevel())
                );
                if (levelConfig != null) {
                    vo.setPromotionLevelName(levelConfig.getLevelName());
                }
                
                return vo;
            }).collect(Collectors.toList());
            
            // 获取总记录数
            Integer totalCount = promotionPointsRecordMapper.getPromoterRecordsCount(userId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("records", recordVOs);
            result.put("totalCount", totalCount);
            result.put("hasMore", offset + size < totalCount);
            
            return Result.success(result);
            
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseUtil.fail("获取推广积分记录失败");
        }
    }
    
    @Override
    public ResponseUtil addPromotionPointsRecord(Integer promoterId, Integer promotedUserId, 
                                               Integer promotionLevel, Integer rewardPoints) {
        try {
            PromotionPointsRecord record = new PromotionPointsRecord();
            record.setPromoterId(promoterId)
                  .setPromotedUserId(promotedUserId)
                  .setPromotionLevel(promotionLevel)
                  .setRewardPoints(rewardPoints)
                  .setPromotionTime(new Date())
                  .setCreateTime(new Date());
            
            int result = promotionPointsRecordMapper.insert(record);
            
            if (result > 0) {
                return ResponseUtil.ok("添加推广积分记录成功");
            } else {
                return ResponseUtil.fail("添加推广积分记录失败");
            }
            
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseUtil.fail("添加推广积分记录失败");
        }
    }
    
    @Override
    public Integer getPromoterTotalPoints(Integer promoterId) {
        try {
            return promotionPointsRecordMapper.getPromoterTotalPoints(promoterId);
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }
    
    @Override
    public ResponseUtil getPromotionPointsStats(Integer userId) {
        try {
            PromotionPointsStatsVO stats = new PromotionPointsStatsVO();
            
            // 获取总积分
            Integer totalPoints = promotionPointsRecordMapper.getPromoterTotalPoints(userId);
            stats.setTotalPoints(totalPoints != null ? totalPoints : 0);
            
            // 获取今日积分
            Date todayStart = Date.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant());
            Date todayEnd = Date.from(LocalDate.now().plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
            
            QueryWrapper<PromotionPointsRecord> todayQuery = new QueryWrapper<>();
            todayQuery.eq("promoter_id", userId)
                     .ge("promotion_time", todayStart)
                     .lt("promotion_time", todayEnd);
            
            List<PromotionPointsRecord> todayRecords = promotionPointsRecordMapper.selectList(todayQuery);
            Integer todayPoints = todayRecords.stream()
                .mapToInt(PromotionPointsRecord::getRewardPoints)
                .sum();
            stats.setTodayPoints(todayPoints);
            stats.setTodayPromotionCount(todayRecords.size());
            
            // 获取本月积分
            Date monthStart = Date.from(LocalDate.now().withDayOfMonth(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
            Date monthEnd = Date.from(LocalDate.now().plusMonths(1).withDayOfMonth(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
            
            QueryWrapper<PromotionPointsRecord> monthQuery = new QueryWrapper<>();
            monthQuery.eq("promoter_id", userId)
                     .ge("promotion_time", monthStart)
                     .lt("promotion_time", monthEnd);
            
            List<PromotionPointsRecord> monthRecords = promotionPointsRecordMapper.selectList(monthQuery);
            Integer monthPoints = monthRecords.stream()
                .mapToInt(PromotionPointsRecord::getRewardPoints)
                .sum();
            stats.setMonthPoints(monthPoints);
            stats.setMonthPromotionCount(monthRecords.size());
            
            // 获取总推广人数
            QueryWrapper<PromotionPointsRecord> totalQuery = new QueryWrapper<>();
            totalQuery.eq("promoter_id", userId);
            Integer totalPromotionCount = promotionPointsRecordMapper.selectCount(totalQuery);
            stats.setTotalPromotionCount(totalPromotionCount);
            
            // 获取当前等级信息
            List<PromotionLevelConfig> levelConfigs = promotionLevelConfigMapper.selectList(
                new QueryWrapper<PromotionLevelConfig>().orderByAsc("level")
            );
            
            PromotionLevelConfig currentLevel = null;
            PromotionLevelConfig nextLevel = null;
            
            for (PromotionLevelConfig config : levelConfigs) {
                if (totalPromotionCount >= config.getMinPromotionCount() && 
                    (config.getMaxPromotionCount() == null || totalPromotionCount <= config.getMaxPromotionCount())) {
                    currentLevel = config;
                } else if (totalPromotionCount < config.getMinPromotionCount() && nextLevel == null) {
                    nextLevel = config;
                }
            }
            
            if (currentLevel != null) {
                stats.setCurrentLevel(currentLevel.getLevel());
                stats.setCurrentLevelName(currentLevel.getLevelName());
            }
            
            if (nextLevel != null) {
                stats.setNextLevelRequiredCount(nextLevel.getMinPromotionCount() - totalPromotionCount);
            }
            
            return ResponseUtil.ok(stats);
            
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseUtil.fail("获取推广积分统计失败");
        }
    }*/
}