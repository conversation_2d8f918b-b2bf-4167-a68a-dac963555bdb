package com.logic.code.service;

import com.logic.code.entity.Ad;
import com.logic.code.mapper.AdMapper;
import com.logic.code.mapper.CommonMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/5/7 23:31
 * @desc
 */
@Service
public class AdService extends BaseService<Ad> {

    @Resource
    private AdMapper adMapper;

    @Override
    protected CommonMapper<Ad> getMapper() {
        return adMapper;
    }
}
