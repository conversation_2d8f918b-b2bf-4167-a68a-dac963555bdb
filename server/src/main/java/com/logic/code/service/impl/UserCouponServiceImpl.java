package com.logic.code.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logic.code.entity.UserCoupon;
import com.logic.code.mapper.UserCouponMapper;
import com.logic.code.service.UserCouponService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 用户优惠券服务实现类
 * <AUTHOR>
 * @date 2025/7/25
 */
@Service
@Slf4j
public class UserCouponServiceImpl implements UserCouponService {

    @Autowired
    private UserCouponMapper userCouponMapper;

    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

    @Override
    public Map<String, Object> getCouponStats(Integer userId) {
        try {
            Date now = new Date();
            
            // 查询可用优惠券数量
            QueryWrapper<UserCoupon> availableWrapper = new QueryWrapper<>();
            availableWrapper.eq("user_id", userId)
                           .eq("status", 0)
                           .le("start_time", now)
                           .ge("end_time", now);
            Long availableCount = userCouponMapper.selectCount(availableWrapper);

            // 查询已使用优惠券数量
            QueryWrapper<UserCoupon> usedWrapper = new QueryWrapper<>();
            usedWrapper.eq("user_id", userId).eq("status", 1);
            Long usedCount = userCouponMapper.selectCount(usedWrapper);

            // 查询已过期优惠券数量
            QueryWrapper<UserCoupon> expiredWrapper = new QueryWrapper<>();
            expiredWrapper.eq("user_id", userId)
                         .and(wrapper -> wrapper.eq("status", 2)
                                               .or()
                                               .lt("end_time", now));
            Long expiredCount = userCouponMapper.selectCount(expiredWrapper);

            // 总数量
            Long totalCount = availableCount + usedCount + expiredCount;

            Map<String, Object> stats = new HashMap<>();
            stats.put("totalCount", totalCount);
            stats.put("availableCount", availableCount);
            stats.put("usedCount", usedCount);
            stats.put("expiredCount", expiredCount);

            return stats;
        } catch (Exception e) {
            log.error("获取用户优惠券统计失败", e);
            Map<String, Object> defaultStats = new HashMap<>();
            defaultStats.put("totalCount", 0);
            defaultStats.put("availableCount", 0);
            defaultStats.put("usedCount", 0);
            defaultStats.put("expiredCount", 0);
            return defaultStats;
        }
    }

    @Override
    public List<UserCoupon> getUserCoupons(Integer userId, String status, Integer page, Integer size) {
        try {
            Page<UserCoupon> pageObj = new Page<>(page, size);
            QueryWrapper<UserCoupon> wrapper = new QueryWrapper<>();
            wrapper.eq("user_id", userId);

            Date now = new Date();
            
            // 根据状态筛选
            switch (status) {
                case "available":
                    wrapper.eq("status", 0)
                           .le("start_time", now)
                           .ge("end_time", now);
                    break;
                case "used":
                    wrapper.eq("status", 1);
                    break;
                case "expired":
                    wrapper.and(w -> w.eq("status", 2)
                                     .or()
                                     .lt("end_time", now));
                    break;
            }

            wrapper.orderByDesc("create_time");
            
            Page<UserCoupon> result = userCouponMapper.selectPage(pageObj, wrapper);
            List<UserCoupon> coupons = result.getRecords();
            
            // 格式化时间和设置状态
            for (UserCoupon coupon : coupons) {
                formatCouponData(coupon, now);
            }
            
            return coupons;
        } catch (Exception e) {
            log.error("获取用户优惠券列表失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public boolean useCoupon(Integer userId, Integer couponId, Integer orderId) {
        try {
            UserCoupon coupon = userCouponMapper.selectById(couponId);
            if (coupon == null || !coupon.getUserId().equals(userId)) {
                log.warn("优惠券不存在或不属于当前用户");
                return false;
            }

            if (coupon.getStatus() != 0) {
                log.warn("优惠券状态不可用");
                return false;
            }

            Date now = new Date();
            if (now.before(coupon.getStartTime()) || now.after(coupon.getEndTime())) {
                log.warn("优惠券不在有效期内");
                return false;
            }

            // 更新优惠券状态，同时保存历史记录
            coupon.setStatus(1);
            coupon.setUseTime(now);
            coupon.setOrderId(orderId);
            // 保存历史记录
            coupon.setLastUsedOrderId(orderId);
            coupon.setLastUseTime(now);
            coupon.setIsRefunded(false);
            coupon.setRefundTime(null);

            int updateResult = userCouponMapper.updateById(coupon);
            if (updateResult > 0) {
                log.info("优惠券使用成功，订单ID：{}，优惠券ID：{}", orderId, couponId);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("使用优惠券失败", e);
            return false;
        }
    }

    @Override
    public boolean addCouponsToUser(Integer userId, Integer couponTemplateId, Integer count) {
        try {
            for (int i = 0; i < count; i++) {
                UserCoupon userCoupon = createCouponFromTemplate(userId, couponTemplateId);
                if (userCoupon != null) {
                    userCouponMapper.insert(userCoupon);
                }
            }
            return true;
        } catch (Exception e) {
            log.error("为用户添加优惠券失败", e);
            return false;
        }
    }

    /**
     * 根据模板创建用户优惠券
     */
    private UserCoupon createCouponFromTemplate(Integer userId, Integer couponTemplateId) {
        UserCoupon userCoupon = new UserCoupon();
        userCoupon.setUserId(userId);
        userCoupon.setCouponId(couponTemplateId);
        userCoupon.setCouponNumber("RECHARGE_" + System.currentTimeMillis() + "_" + userId + "_" + UUID.randomUUID().toString().substring(0, 8));
        userCoupon.setStatus(0);
        userCoupon.setCreateTime(new Date());

        // 根据模板ID设置优惠券信息
        switch (couponTemplateId) {
            case 1: // 10元券
                userCoupon.setTitle("10元消费券");
                userCoupon.setDescription("充值500元赠送，满50元可用");
                userCoupon.setType(1);
                userCoupon.setTypeName("满减券");
                userCoupon.setAmount(new BigDecimal("10"));
                userCoupon.setMinAmount(new BigDecimal("50"));
                break;
            case 2: // 20元券
                userCoupon.setTitle("20元消费券");
                userCoupon.setDescription("充值1000元赠送，满100元可用");
                userCoupon.setType(1);
                userCoupon.setTypeName("满减券");
                userCoupon.setAmount(new BigDecimal("20"));
                userCoupon.setMinAmount(new BigDecimal("100"));
                break;
            default:
                userCoupon.setTitle("优惠券");
                userCoupon.setDescription("系统赠送");
                userCoupon.setType(1);
                userCoupon.setTypeName("满减券");
                userCoupon.setAmount(new BigDecimal("5"));
                userCoupon.setMinAmount(new BigDecimal("30"));
                break;
        }

        // 设置有效期（90天，充值赠送的优惠券有效期更长）
        Calendar calendar = Calendar.getInstance();
        userCoupon.setStartTime(calendar.getTime());
        calendar.add(Calendar.DAY_OF_MONTH, 90);
        userCoupon.setEndTime(calendar.getTime());

        return userCoupon;
    }

    /**
     * 格式化优惠券数据
     */
    private void formatCouponData(UserCoupon coupon, Date now) {
        // 格式化开始时间和结束时间字符串，用于前端显示
        String startTimeStr = "";
        String endTimeStr = "";

        if (coupon.getStartTime() != null) {
            startTimeStr = dateFormat.format(coupon.getStartTime());
        }

        if (coupon.getEndTime() != null) {
            endTimeStr = dateFormat.format(coupon.getEndTime());
        }

        // 将格式化的时间存储在remark字段中，前端可以直接使用
        // 格式：startTime,endTime
        if (!startTimeStr.isEmpty() && !endTimeStr.isEmpty()) {
            coupon.setRemark(startTimeStr + "," + endTimeStr);
        } else if (!endTimeStr.isEmpty()) {
            // 如果只有结束时间，也要保持逗号格式以便前端解析
            coupon.setRemark("," + endTimeStr);
        }

        // 检查并更新过期状态
        if (coupon.getStatus() == 0 && coupon.getEndTime() != null && now.after(coupon.getEndTime())) {
            coupon.setStatus(2);
            userCouponMapper.updateById(coupon);
        }
    }

    @Override
    public boolean refundCoupon(Integer orderId) {
        try {
            // 查找该订单使用的优惠券
            QueryWrapper<UserCoupon> wrapper = new QueryWrapper<>();
            wrapper.eq("order_id", orderId).eq("status", 1);

            UserCoupon coupon = userCouponMapper.selectOne(wrapper);
            if (coupon != null) {
                // 检查优惠券是否还在有效期内
                Date now = new Date();
                if (coupon.getEndTime() != null && now.before(coupon.getEndTime())) {
                    // 恢复优惠券状态为可用，但保留历史记录
                    coupon.setStatus(0);
                    coupon.setUseTime(null);
                    coupon.setOrderId(null);
                    // 标记为已退回，保留历史记录
                    coupon.setIsRefunded(true);
                    coupon.setRefundTime(now);

                    int updateResult = userCouponMapper.updateById(coupon);
                    if (updateResult > 0) {
                        log.info("订单{}的优惠券{}已退回", orderId, coupon.getId());
                        return true;
                    }
                } else {
                    // 过期的优惠券也标记为已退回，但不恢复可用状态
                    coupon.setIsRefunded(true);
                    coupon.setRefundTime(now);
                    userCouponMapper.updateById(coupon);
                    log.info("订单{}的优惠券{}已过期，标记为已退回但不恢复可用", orderId, coupon.getId());
                    return true;
                }
            }
            return true; // 没有使用优惠券，不算失败
        } catch (Exception e) {
            log.error("退回订单{}的优惠券失败", orderId, e);
            return false;
        }
    }
}