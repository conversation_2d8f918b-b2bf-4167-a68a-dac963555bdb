package com.logic.code.service;

import com.logic.code.entity.PromotionPointsRecord;
import com.logic.code.model.vo.PromotionPointsRecordVO;

import java.util.List;

/**
 * 推广积分服务接口
 */
public interface PromotionPointsService {
    
    /**
     * 获取推广积分记录
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 积分记录列表
     */
   // ResponseUtil getPromotionPointsRecords(Integer userId, Integer page, Integer size);
    
    /**
     * 添加推广积分记录
     * @param promoterId 推广者ID
     * @param promotedUserId 被推广用户ID
     * @param promotionLevel 推广等级
     * @param rewardPoints 奖励积分
     * @return 操作结果
     */
   // ResponseUtil addPromotionPointsRecord(Integer promoterId, Integer promotedUserId,Integer promotionLevel, Integer rewardPoints);
    
    /**
     * 获取推广者总积分
     * @param promoterId 推广者ID
     * @return 总积分
     */
  //  Integer getPromoterTotalPoints(Integer promoterId);
    
    /**
     * 获取推广积分统计
     * @param userId 用户ID
     * @return 积分统计信息
     */
   // ResponseUtil getPromotionPointsStats(Integer userId);
}