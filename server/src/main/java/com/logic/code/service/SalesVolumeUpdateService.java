package com.logic.code.service;

import com.logic.code.entity.goods.Goods;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * 销量更新服务
 * 用于定时随机增加商品销量
 * 
 * <AUTHOR>
 * @date 2025/7/27
 */
@Service
@Slf4j
public class SalesVolumeUpdateService {

    @Resource
    private GoodsService goodsService;

    private final Random random = new Random();

    /**
     * 随机更新商品销量
     * 每次执行时随机选择部分在售商品，为每个选中的商品随机增加1-9的销量
     */
    @Transactional(rollbackFor = Exception.class)
    public void randomUpdateSalesVolume() {
        try {
            log.info("开始执行随机销量更新...");
            
            // 获取所有在售商品
            List<Goods> allGoods = goodsService.queryAll();
            List<Goods> onSaleGoods = allGoods.stream()
                    .filter(goods -> goods.getIsOnSale() != null && goods.getIsOnSale())
                    .filter(goods -> goods.getIsDelete() != null && !goods.getIsDelete())
                    .collect(Collectors.toList());
            
            if (onSaleGoods.isEmpty()) {
                log.info("没有找到在售商品，跳过销量更新");
                return;
            }
            
            log.info("找到在售商品总数: {}", onSaleGoods.size());
            
            // 随机选择30%-70%的商品进行销量更新
            int minSelectCount = Math.max(1, (int) (onSaleGoods.size() * 0.3));
            int maxSelectCount = Math.max(minSelectCount, (int) (onSaleGoods.size() * 0.7));
            int selectCount = random.nextInt(maxSelectCount - minSelectCount + 1) + minSelectCount;
            
            log.info("本次将更新 {} 个商品的销量", selectCount);
            
            // 随机选择商品
            List<Goods> selectedGoods = onSaleGoods.stream()
                    .sorted((a, b) -> random.nextInt(3) - 1) // 随机排序
                    .limit(selectCount)
                    .collect(Collectors.toList());
            
            int updatedCount = 0;
            int totalIncrease = 0;
            
            // 为每个选中的商品随机增加销量
            for (Goods goods : selectedGoods) {
                try {
                    // 随机增加1-9的销量
                    int increaseAmount = random.nextInt(9) + 1;
                    
                    // 获取当前销量，如果为null则设为0
                    int currentSellVolume = goods.getSellVolume() != null ? goods.getSellVolume() : 0;
                    int newSellVolume = currentSellVolume + increaseAmount;
                    
                    // 更新商品销量
                    goods.setSellVolume(newSellVolume);
                    goodsService.updateById(goods);
                    
                    updatedCount++;
                    totalIncrease += increaseAmount;
                    
                    log.debug("商品[{}] {} 销量从 {} 增加到 {} (+{})", 
                            goods.getId(), goods.getName(), currentSellVolume, newSellVolume, increaseAmount);
                    
                } catch (Exception e) {
                    log.error("更新商品[{}]销量失败: {}", goods.getId(), e.getMessage(), e);
                }
            }
            
            log.info("销量更新完成！成功更新 {} 个商品，总计增加销量 {}", updatedCount, totalIncrease);
            
        } catch (Exception e) {
            log.error("执行随机销量更新时发生异常: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 手动触发销量更新（用于测试）
     */
    public void manualUpdateSalesVolume() {
        log.info("手动触发销量更新...");
        randomUpdateSalesVolume();
    }
}
