package com.logic.code.service;

import com.logic.code.entity.ExpressCompany;
import com.logic.code.model.vo.ExpressCompanyVO;

import java.util.List;

/**
 * 快递公司服务接口
 * <AUTHOR>
 * @date 2025/1/27
 */
public interface ExpressCompanyService {
    
    /**
     * 获取启用的快递公司列表
     * @return 快递公司VO列表
     */
    List<ExpressCompanyVO> getEnabledExpressCompanies();
    
    /**
     * 获取所有快递公司列表
     * @return 快递公司VO列表
     */
    List<ExpressCompanyVO> getAllExpressCompanies();
    
    /**
     * 根据ID查询快递公司
     * @param id 快递公司ID
     * @return 快递公司实体
     */
    ExpressCompany getById(Integer id);
    
    /**
     * 根据编码查询快递公司
     * @param code 快递公司编码
     * @return 快递公司实体
     */
    ExpressCompany getByCode(String code);
}