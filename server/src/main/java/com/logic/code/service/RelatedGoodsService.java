package com.logic.code.service;

import com.logic.code.entity.goods.RelatedGoods;
import com.logic.code.mapper.CommonMapper;
import com.logic.code.mapper.RelatedGoodsMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/5/8 11:35
 * @desc
 */

@Service
public class RelatedGoodsService extends BaseService<RelatedGoods>{

    @Resource
    private RelatedGoodsMapper relatedGoodsMapper;
    @Override
    protected CommonMapper<RelatedGoods> getMapper() {
        return relatedGoodsMapper;
    }
}
