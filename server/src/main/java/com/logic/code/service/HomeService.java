package com.logic.code.service;

import com.logic.code.entity.Ad;
import com.logic.code.entity.Topic;
import com.logic.code.entity.goods.Brand;
import com.logic.code.entity.goods.Category;
import com.logic.code.entity.goods.Channel;
import com.logic.code.entity.goods.Goods;
import com.logic.code.model.vo.HomeCategoryVO;
import com.logic.code.model.vo.HomeIndexVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/7 23:25
 * @desc
 */
@Service
public class HomeService {
    @Autowired
    private AdService adService;

    @Autowired
    private ChannelService channelService;

    @Autowired
    private GoodsService goodsService;

    @Autowired
    private BrandService brandService;

    @Autowired
    private TopicService topicService;

    @Autowired
    private CategoryService categoryService;

    //    @Cacheable("index")
    public HomeIndexVO index() {

        List<Ad> bannerList = adService.queryByCriteria(Criteria.of(Ad.class).fields(Ad::getId, Ad::getLink, Ad::getImageUrl).andEqualTo(Ad::getAdPositionId, 1));

        List<Channel> channelList = channelService.queryByCriteria(Criteria.of(Channel.class).fields(Channel::getId, Channel::getIconUrl, Channel::getName, Channel::getUrl).sort(Channel::getSortOrder));

        List<Goods> newGoodsList = goodsService.queryByCriteria(Criteria.of(Goods.class).fields(Goods::getId, Goods::getListPicUrl, Goods::getName, Goods::getRetailPrice).page(1, 4).andEqualTo(Goods::getIsNewly, true));

        List<Goods> hotGoodsList = goodsService.queryByCriteria(Criteria.of(Goods.class).fields(Goods::getId, Goods::getListPicUrl, Goods::getName, Goods::getGoodsBrief, Goods::getRetailPrice).page(1, 4).andEqualTo(Goods::getIsHot, true));

        List<Brand> brandList = brandService.queryByCriteria(Criteria.of(Brand.class).fields(Brand::getId, Brand::getNewPicUrl, Brand::getName, Brand::getFloorPrice).andEqualTo(Brand::getIsNewly, 1).sort(Brand::getNewSortOrder));

        List<Topic> topicList = topicService.queryByCriteria(Criteria.of(Topic.class).fields(Topic::getId, Topic::getScenePicUrl, Topic::getTitle, Topic::getPriceInfo, Topic::getSubtitle).page(1, 10));

        List<HomeCategoryVO> categoryList = new LinkedList<>();

        categoryService.queryByCriteria(
                Criteria.of(Category.class).fields(Category::getId, Category::getName).andEqualTo(Category::getParentId, 0)
        ).forEach(c -> {

            List<Integer> categoryIdList = categoryService.queryByCriteria(Criteria.of(Category.class).fields(Category::getId).andEqualTo(Category::getParentId, c.getId())).stream()
                    .map(Category::getId)
                    .collect(Collectors.toList());

            List<Goods> goodsList = goodsService.queryByCriteria(Criteria.of(Goods.class).fields(Goods::getId, Goods::getListPicUrl, Goods::getName, Goods::getRetailPrice).andIn(Goods::getCategoryId, categoryIdList).page(1, 3));
            categoryList.add(new HomeCategoryVO(c.getId(), c.getName(), goodsList));
        });

        return new HomeIndexVO().setBannerList(bannerList)
                .setChannelList(channelList)
                .setNewGoodsList(newGoodsList)
                .setHotGoodsList(hotGoodsList)
                .setBrandList(brandList)
                .setTopicList(topicList)
                .setCategoryList(categoryList);
    }
}
