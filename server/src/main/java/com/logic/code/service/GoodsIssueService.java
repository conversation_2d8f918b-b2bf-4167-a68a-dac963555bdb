package com.logic.code.service;

import com.logic.code.entity.goods.GoodsIssue;
import com.logic.code.mapper.CommonMapper;
import com.logic.code.mapper.GoodsIssueMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/5/8 11:31
 * @desc
 */
@Service
public class GoodsIssueService extends BaseService<GoodsIssue> {

    @Resource
    private GoodsIssueMapper goodsIssueMapper;

    @Override
    protected CommonMapper<GoodsIssue> getMapper() {
        return goodsIssueMapper;
    }
}
