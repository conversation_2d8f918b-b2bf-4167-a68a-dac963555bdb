package com.logic.code.service;

import com.logic.code.common.utils.JwtHelper;
import com.logic.code.entity.Collect;
import com.logic.code.entity.User;
import com.logic.code.mapper.CollectMapper;
import com.logic.code.mapper.CommonMapper;
import com.logic.code.model.dto.GoodsCollectDTO;
import com.logic.code.model.vo.CollectAddOrDeleteParamVO;
import com.logic.code.model.vo.CollectAddOrDeleteResultVO;
import jakarta.annotation.Resource;
import logic.orm.WrapperBuilder;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/8 10:31
 * @desc
 */
@Service
public class CollectService extends BaseService<Collect> {

    @Resource
    private CollectMapper collectMapper;

    @Override
    protected CommonMapper<Collect> getMapper() {
        return collectMapper;
    }

    public List<GoodsCollectDTO> queryGoodsCollectList(Integer userId) {
        return collectMapper.selectGoodsCollectByUserId(userId);
    }


    public CollectAddOrDeleteResultVO addOrDelete(CollectAddOrDeleteParamVO dto) {
        User userInfo = JwtHelper.getUserInfo();
        List<Collect> data = queryByCriteria(
                Criteria.of(Collect.class).andEqualTo(Collect::getTypeId, dto.getTypeId())
                        .andEqualTo(Collect::getValueId, dto.getValueId())
                        .andEqualTo(Collect::getUserId, userInfo.getId()));
        //添加收藏
        if (data.size() == 0) {
            create(Collect.builder().typeId(dto.getTypeId()).valueId(dto.getValueId()).userId(userInfo.getId()).build());
            return new CollectAddOrDeleteResultVO(true);
        } else {
            delete(Collect.builder().typeId(dto.getTypeId()).valueId(dto.getValueId()).userId(userInfo.getId()).build());
            return new CollectAddOrDeleteResultVO(false);
        }

    }

    public Long getCollectNum() {
        return collectMapper.selectCount(WrapperBuilder.autoWhere(Collect.builder().userId(JwtHelper.getUserInfo().getId()).build()));
    }

    public List<GoodsCollectDTO> queryGoodsCollectList() {
        User userInfo = JwtHelper.getUserInfo();
        return queryGoodsCollectList(userInfo.getId());
    }
}
