package com.logic.code.service;

import com.logic.code.common.enmus.WeshopWechatResultStatus;
import com.logic.code.common.exception.WeshopWechatException;
import com.logic.code.common.utils.JwtHelper;
import com.logic.code.common.utils.StringUtils;
import com.logic.code.common.utils.TokenUtils;
import com.logic.code.config.TokenConfig;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * Token管理服务
 * 
 * <AUTHOR>
 * @date 2025/7/29
 */
@Slf4j
@Service
public class TokenService {

    @Autowired
    private TokenConfig tokenConfig;

    /**
     * Token黑名单缓存（简单实现，生产环境建议使用Redis）
     * key: token的jti（JWT ID）
     * value: 过期时间戳
     */
    private final Map<String, Long> tokenBlacklist = new ConcurrentHashMap<>();

    /**
     * 创建token
     * 
     * @param userId 用户ID
     * @param userInfo 用户信息JSON
     * @return token字符串
     */
    public String createToken(String userId, String userInfo) {
        if (StringUtils.isBlank(userId) || StringUtils.isBlank(userInfo)) {
            throw new WeshopWechatException(WeshopWechatResultStatus.WECHAT_LOGIN_ERROR);
        }
        
        String token = JwtHelper.createJWT(userId, userInfo, tokenConfig.getTtl());
        
        if (tokenConfig.isLogEnabled()) {
            TokenUtils.logTokenOperation("创建", userId, true, "新token已生成");
        }
        
        return token;
    }

    /**
     * 验证token
     * 
     * @param token token字符串
     * @return Claims对象
     * @throws WeshopWechatException 如果token无效
     */
    public Claims validateToken(String token) {
        if (StringUtils.isBlank(token)) {
            throw new WeshopWechatException(WeshopWechatResultStatus.WECHAT_LOGIN_ERROR);
        }
        
        Claims claims = JwtHelper.parseJWT(token);
        
        // 检查token是否在黑名单中
        if (isTokenBlacklisted(claims.getId())) {
            throw new WeshopWechatException(WeshopWechatResultStatus.TOKEN_INVALID);
        }
        
        return claims;
    }

    /**
     * 刷新token
     * 
     * @param oldToken 旧token
     * @return 新token
     */
    public String refreshToken(String oldToken) {
        if (!tokenConfig.isManualRefreshEnabled()) {
            throw new WeshopWechatException(WeshopWechatResultStatus.TOKEN_REFRESH_FAILED);
        }
        
        Claims claims = null;
        boolean isExpired = false;
        
        try {
            claims = validateToken(oldToken);
        } catch (WeshopWechatException e) {
            if (WeshopWechatResultStatus.TOKEN_EXPIRED.equals(e.getStatus()) && 
                tokenConfig.isExpiredRefreshEnabled()) {
                // 尝试解析过期token
                claims = JwtHelper.parseExpiredJWT(oldToken);
                if (claims != null) {
                    // 检查是否在允许的刷新窗口内
                    long expiredTime = System.currentTimeMillis() - claims.getExpiration().getTime();
                    if (expiredTime > tokenConfig.getExpiredRefreshWindow()) {
                        throw new WeshopWechatException(WeshopWechatResultStatus.TOKEN_REFRESH_FAILED);
                    }
                    isExpired = true;
                } else {
                    throw e;
                }
            } else {
                throw e;
            }
        }
        
        if (claims == null) {
            throw new WeshopWechatException(WeshopWechatResultStatus.TOKEN_REFRESH_FAILED);
        }
        
        // 将旧token加入黑名单
        addToBlacklist(claims.getId(), claims.getExpiration().getTime());
        
        // 生成新token
        String newToken = JwtHelper.refreshToken(claims);
        
        if (tokenConfig.isLogEnabled()) {
            String userId = claims.get("uid", String.class);
            String message = isExpired ? "过期token刷新成功" : "token刷新成功";
            TokenUtils.logTokenOperation("刷新", userId, true, message);
        }
        
        return newToken;
    }

    /**
     * 撤销token（加入黑名单）
     * 
     * @param token token字符串
     */
    public void revokeToken(String token) {
        try {
            Claims claims = JwtHelper.parseExpiredJWT(token);
            if (claims != null) {
                addToBlacklist(claims.getId(), claims.getExpiration().getTime());
                
                if (tokenConfig.isLogEnabled()) {
                    String userId = claims.get("uid", String.class);
                    TokenUtils.logTokenOperation("撤销", userId, true, "token已撤销");
                }
            }
        } catch (Exception e) {
            log.warn("撤销token失败: {}", e.getMessage());
        }
    }

    /**
     * 获取token状态信息
     * 
     * @param token token字符串
     * @return 状态信息
     */
    public Map<String, Object> getTokenStatus(String token) {
        Map<String, Object> status = new HashMap<>();
        
        if (StringUtils.isBlank(token)) {
            status.put("valid", false);
            status.put("error", "Token为空");
            return status;
        }
        
        try {
            Claims claims = validateToken(token);
            
            long remainingTime = TokenUtils.getRemainingTime(claims);
            boolean shouldRefresh = JwtHelper.shouldRefreshToken(claims);
            boolean expiringSoon = TokenUtils.isTokenExpiringSoon(claims);
            
            status.put("valid", true);
            status.put("userId", claims.get("uid"));
            status.put("issuedAt", claims.getIssuedAt());
            status.put("expiresAt", claims.getExpiration());
            status.put("remainingTime", remainingTime);
            status.put("remainingTimeDesc", TokenUtils.getRemainingTimeDescription(claims));
            status.put("shouldRefresh", shouldRefresh);
            status.put("expiringSoon", expiringSoon);
            status.put("canRefresh", tokenConfig.isManualRefreshEnabled());
            status.put("statusDesc", TokenUtils.getTokenStatusDescription(claims));
            
        } catch (WeshopWechatException e) {
            status.put("valid", false);
            status.put("error", e.getStatus().getMsg());
            status.put("errorCode", e.getStatus().getCode());
            
            // 检查是否可以刷新
            if (WeshopWechatResultStatus.TOKEN_EXPIRED.equals(e.getStatus()) && 
                tokenConfig.isExpiredRefreshEnabled()) {
                Claims expiredClaims = JwtHelper.parseExpiredJWT(token);
                if (expiredClaims != null) {
                    long expiredTime = System.currentTimeMillis() - expiredClaims.getExpiration().getTime();
                    boolean canRefresh = expiredTime <= tokenConfig.getExpiredRefreshWindow();
                    
                    status.put("canRefresh", canRefresh);
                    status.put("userId", expiredClaims.get("uid"));
                    status.put("expiredAt", expiredClaims.getExpiration());
                    status.put("expiredTime", expiredTime);
                } else {
                    status.put("canRefresh", false);
                }
            } else {
                status.put("canRefresh", false);
            }
        }
        
        return status;
    }

    /**
     * 检查token是否在黑名单中
     * 
     * @param jti JWT ID
     * @return 是否在黑名单中
     */
    private boolean isTokenBlacklisted(String jti) {
        if (StringUtils.isBlank(jti)) {
            return false;
        }
        
        Long expireTime = tokenBlacklist.get(jti);
        if (expireTime == null) {
            return false;
        }
        
        // 如果黑名单中的token已过期，则移除
        if (System.currentTimeMillis() > expireTime) {
            tokenBlacklist.remove(jti);
            return false;
        }
        
        return true;
    }

    /**
     * 将token加入黑名单
     * 
     * @param jti JWT ID
     * @param expireTime 过期时间戳
     */
    private void addToBlacklist(String jti, long expireTime) {
        if (StringUtils.isNotBlank(jti)) {
            tokenBlacklist.put(jti, expireTime);
        }
    }

    /**
     * 清理过期的黑名单条目
     */
    public void cleanupBlacklist() {
        long currentTime = System.currentTimeMillis();
        tokenBlacklist.entrySet().removeIf(entry -> entry.getValue() < currentTime);
        
        if (tokenConfig.isLogEnabled()) {
            log.debug("黑名单清理完成，当前大小: {}", tokenBlacklist.size());
        }
    }

    /**
     * 获取配置信息
     * 
     * @return 配置信息
     */
    public Map<String, Object> getConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("ttl", tokenConfig.getTtl());
        config.put("refreshThreshold", tokenConfig.getRefreshThreshold());
        config.put("autoRefreshEnabled", tokenConfig.isAutoRefreshEnabled());
        config.put("expiredRefreshEnabled", tokenConfig.isExpiredRefreshEnabled());
        config.put("expiredRefreshWindow", tokenConfig.getExpiredRefreshWindow());
        config.put("manualRefreshEnabled", tokenConfig.isManualRefreshEnabled());
        config.put("statusCheckEnabled", tokenConfig.isStatusCheckEnabled());
        return config;
    }
}
