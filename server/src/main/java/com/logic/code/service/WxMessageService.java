package com.logic.code.service;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaKefuMessage;
import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import cn.binarywang.wx.miniapp.constant.WxMaConstants;
import com.logic.code.common.Cache;
import com.logic.code.config.WxMaConfiguration;
import com.logic.code.entity.Card;
import com.logic.code.entity.User;
import com.logic.code.entity.order.Order;
import com.logic.code.entity.order.OrderGoods;
import com.logic.code.model.vo.OrderDetailVO;
import jakarta.annotation.Resource;
import logic.orm.utils.ListUtils;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 微信消息服务
 * 处理微信小程序消息发送，包括客服消息和订阅消息
 */
@Service
@Slf4j
public class WxMessageService {

    @Value("${wx.miniapp.configs[0].appid}")
    private String appid;

    @Value("${wx.miniapp.subscribe-template-id:}")
    private String subscribeTemplateId;

    @Value("${wx.miniapp.order-success-template-id:}")
    private String orderSuccessTemplateId;

    @Resource
    private Cache cache;

    @Resource
    private UserService userService;

    @Resource
    private CardService cardService;



    public boolean printOrderInfo(Order order, User user, List<OrderGoods> orderGoods, boolean isCardOrder) {
        if (user == null || user.getWechatOpenId() == null || user.getWechatOpenId().isEmpty()) {
            log.warn("用户信息不完整，无法发送订阅通知，orderId: {}", order.getId());
            return false;
        }

        // 检查订阅消息模板ID是否配置
        String templateId = orderSuccessTemplateId;
        if (templateId == null || templateId.isEmpty()) {
            log.warn("未配置订单成功通知模板ID，无法发送订阅消息");
            return false;
        }

        try {

            // 订单商品名称
            String goodsName = orderGoods.isEmpty() ? (isCardOrder ? "卡券" : "商品") : orderGoods.get(0).getGoodsName();
            if (orderGoods.size() > 1) {
                goodsName += isCardOrder ? "等多张卡券" : "等多件商品";
            }

            // 裁剪商品名称，避免超出模板限制
            if (goodsName.length() > 20) {
                goodsName = goodsName.substring(0, 17) + "...";
            }

            // 格式化订单创建时间
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String orderTime = sdf.format(order.getCreateTime() != null ? order.getCreateTime() : new Date());

            // 订单金额，卡券订单通常为0元
            String amount = isCardOrder ? "0.00" : order.getActualPrice().toString();


            StringBuffer messageContent = new StringBuffer();
            StringBuffer address = new StringBuffer();
            address.append(cache.region.get(order.getProvince())).append(cache.region.get(order.getCity())).append(cache.region.get(order.getDistrict())).append(order.getAddress());
            messageContent.append("订单编码：").append(order.getOrderSn()).append("\n");
            if(isCardOrder){
                Card card = cardService.queryById(order.getCouponId());
                messageContent.append("卡劵编码：").append(card.getNo()).append("\n");
            }
            messageContent.append("订单类型：").append(isCardOrder ? "卡券订单" : "商品订单").append("\n");
            messageContent.append("商品信息：").append(goodsName).append("\n");
            messageContent.append("收件人：").append(order.getConsignee()).append("\n");
            messageContent.append("电话：").append(order.getMobile()).append("\n");
            messageContent.append("地址：").append(address).append("\n");
            System.err.println(messageContent);
            return true;
        } catch (Exception e) {
            log.error("发送订单订阅通知失败，订单号：{}，错误：{}", order.getOrderSn(), e.getMessage(), e);
            return false;
        }
    }
    /**
     * 发送订单订阅消息通知
     * 使用微信订阅消息，不受48小时限制，但需要用户授权订阅
     *
     * @param order       订单信息
     * @param user        用户信息
     * @param orderGoods  订单商品信息
     * @param isCardOrder 是否为卡券订单
     * @return 是否发送成功
     */
    public boolean sendOrderSubscribeMessage(Order order, User user, List<OrderGoods> orderGoods, boolean isCardOrder) {
        if (user == null || user.getWechatOpenId() == null || user.getWechatOpenId().isEmpty()) {
            log.warn("用户信息不完整，无法发送订阅通知，orderId: {}", order.getId());
            return false;
        }

        // 检查订阅消息模板ID是否配置
        String templateId = orderSuccessTemplateId;
        if (templateId == null || templateId.isEmpty()) {
            log.warn("未配置订单成功通知模板ID，无法发送订阅消息");
            return false;
        }

        try {

            // 订单商品名称
            String goodsName = orderGoods.isEmpty() ? (isCardOrder ? "卡券" : "商品") : orderGoods.get(0).getGoodsName();
            if (orderGoods.size() > 1) {
                goodsName += isCardOrder ? "等多张卡券" : "等多件商品";
            }

            // 裁剪商品名称，避免超出模板限制
            if (goodsName.length() > 20) {
                goodsName = goodsName.substring(0, 17) + "...";
            }

            // 格式化订单创建时间
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String orderTime = sdf.format(order.getCreateTime() != null ? order.getCreateTime() : new Date());

            // 订单金额，卡券订单通常为0元
            String amount = isCardOrder ? "0.00" : order.getActualPrice().toString();


            StringBuffer messageContent = new StringBuffer();
            StringBuffer address = new StringBuffer();
            messageContent.append("").append(isCardOrder ? "卡券" : "商品").append("");
            messageContent.append(" ").append(goodsName).append("");
            address.append(cache.region.get(order.getProvince())).append(cache.region.get(order.getCity())).append(cache.region.get(order.getDistrict())).append(order.getAddress());

            User userParam = new User();
            userParam.setUserLevelId(1);
            List<User> users = userService.queryList(userParam);
            if (ListUtils.isNotBlank(users)) {
                for (User u : users) {
                    WxMaSubscribeMessage subscribeMessage = WxMaSubscribeMessage.builder()
                            .toUser(u.getWechatOpenId())  // 接收者的openid
                            .templateId("jAbBQuG3Uc_VxNPTWdhOLeZNeXd7qLhm-hwXytogu1A")  // 模板ID
                            .page("pages/ucenter/orderDetail/orderDetail?id=" + order.getId())  // 跳转页面
                            .data(new ArrayList<>())
                            .miniprogramState(WxMaConstants.MiniProgramState.FORMAL)
                            .build();

                    // 根据实际模板格式添加数据
                    // 注意：这里的字段名需要与微信平台申请的模板一致
                   /* subscribeMessage.addData(new WxMaSubscribeMessage.MsgData("character_string1", order.getOrderSn()));  // 订单编号
                    subscribeMessage.addData(new WxMaSubscribeMessage.MsgData("thing2", messageContent.toString()));  // 商品名称
                    subscribeMessage.addData(new WxMaSubscribeMessage.MsgData("amount3", amount));  // 订单金额
                    subscribeMessage.addData(new WxMaSubscribeMessage.MsgData("date4", orderTime));  // 下单时间*/
                    String addressVal = address.toString();
                    if(addressVal.length()>20)addressVal = addressVal.substring(0,20);
                    subscribeMessage.addData(new WxMaSubscribeMessage.MsgData("character_string2", order.getOrderSn()));  // 订单编号
                    subscribeMessage.addData(new WxMaSubscribeMessage.MsgData("thing4", amount));  // 商品名称
                    subscribeMessage.addData(new WxMaSubscribeMessage.MsgData("thing7", messageContent.toString()));  // 备注
                    subscribeMessage.addData(new WxMaSubscribeMessage.MsgData("date3", orderTime));  // 下单时间
                    subscribeMessage.addData(new WxMaSubscribeMessage.MsgData("thing8", addressVal));  // 地址
                    WxMaService wxService = WxMaConfiguration.getMaService(appid);
                    // 发送订阅消息
                    try {
                        wxService.getMsgService().sendSubscribeMsg(subscribeMessage);
                    } catch (WxErrorException e) {
                        e.printStackTrace();
                    }

                    log.info("发送订单订阅通知成功，订单号：{}，接收用户：{}", order.getOrderSn(), u.getWechatOpenId());
                }

            }
            return true;
        } catch (Exception e) {
            log.error("发送订单订阅通知失败，订单号：{}，错误：{}", order.getOrderSn(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 发送订单通知给用户
     * 尝试使用客服消息，如果失败（如48小时限制），则不发送通知
     * 注意：客服消息仅在用户48小时内与小程序有交互时才能发送
     *
     * @param order       订单信息
     * @param user        用户信息
     * @param orderGoods  订单商品信息
     * @param isCardOrder 是否为卡券订单
     * @return 是否发送成功
     */
    public boolean sendOrderNotice(Order order, User user, List<OrderGoods> orderGoods, boolean isCardOrder) {
        if (user == null || user.getWechatOpenId() == null || user.getWechatOpenId().isEmpty()) {
            log.warn("用户信息不完整，无法发送通知，orderId: {}", order.getId());
            return false;
        }

        try {
            final WxMaService wxService = WxMaConfiguration.getMaService(appid);

            // 订单商品名称
            String goodsName = orderGoods.isEmpty() ? (isCardOrder ? "卡券" : "商品") : orderGoods.get(0).getGoodsName();
            if (orderGoods.size() > 1) {
                goodsName += isCardOrder ? "等多张卡券" : "等多件商品";
            }

            // 裁剪商品名称，避免过长
            if (goodsName.length() > 20) {
                goodsName = goodsName.substring(0, 17) + "...";
            }

            // 格式化订单创建时间
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String orderTime = sdf.format(order.getCreateTime() != null ? order.getCreateTime() : new Date());

            // 订单金额，卡券订单通常为0元
            String amount = isCardOrder ? "0.00" : order.getActualPrice().toString();

            // 构建通知消息内容
            StringBuilder messageContent = new StringBuilder();
            messageContent.append("订单创建成功通知\n\n");
            messageContent.append("订单编号：").append(order.getOrderSn()).append("\n");
            messageContent.append("商品信息：").append(goodsName).append("\n");
            messageContent.append("订单金额：").append(amount).append("元\n");
            messageContent.append("创建时间：").append(orderTime).append("\n\n");

            if (isCardOrder) {
                messageContent.append("您的卡券订单已创建成功，感谢您的使用！");
            } else {
                messageContent.append("您的订单已创建成功，我们将尽快为您发货，感谢您的购买！");
            }

            // 创建客服消息
            WxMaKefuMessage kefuMessage = WxMaKefuMessage.newTextBuilder()
                    .toUser(user.getWechatOpenId())
                    .content(messageContent.toString())
                    .build();

            try {
                // 尝试发送客服消息
                wxService.getMsgService().sendKefuMsg(kefuMessage);
                log.info("发送订单客服通知成功，订单号：{}，接收用户：{}", order.getOrderSn(), user.getWechatOpenId());
                return true;
            } catch (Exception e) {
                // 如果是超时错误（45015），记录日志后继续
                if (e.getMessage().contains("45015") || e.getMessage().contains("response out of time limit")) {
                    log.warn("发送客服消息失败（48小时限制），订单号：{}，错误：{}", order.getOrderSn(), e.getMessage());
                    // 当用户信息未录入数据库时，可以在这里记录通知发送失败信息，用于后续处理
                    // 例如添加到消息队列，或者记录到数据库等
                    return false;
                } else {
                    // 其他错误，直接抛出
                    throw e;
                }
            }
        } catch (Exception e) {
            log.error("发送订单通知完全失败，订单号：{}，错误：{}", order.getOrderSn(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 发送订单创建成功通知
     *
     * @param order      订单信息
     * @param user       用户信息
     * @param orderGoods 订单商品信息
     * @return 是否发送成功
     */
    public boolean sendOrderSuccessNotice(Order order, User user, List<OrderGoods> orderGoods) {
        return sendOrderNotice(order, user, orderGoods, false);
    }

    /**
     * 发送卡券订单创建成功通知
     *
     * @param order      订单信息
     * @param user       用户信息
     * @param orderGoods 订单商品信息
     * @return 是否发送成功
     */
    public boolean sendCardOrderSuccessNotice(Order order, User user, List<OrderGoods> orderGoods) {
        return sendOrderNotice(order, user, orderGoods, true);
    }

    /**
     * 发送订单创建成功订阅消息
     *
     * @param order      订单信息
     * @param user       用户信息
     * @param orderGoods 订单商品信息
     * @return 是否发送成功
     */
    public boolean sendOrderSuccessSubscribeMessage(Order order, User user, List<OrderGoods> orderGoods) {
        return sendOrderSubscribeMessage(order, user, orderGoods, false);
    }

    /**
     * 发送卡券订单创建成功订阅消息
     *
     * @param order      订单信息
     * @param user       用户信息
     * @param orderGoods 订单商品信息
     * @return 是否发送成功
     */
    public boolean sendCardOrderSuccessSubscribeMessage(Order order, User user, List<OrderGoods> orderGoods) {
        return sendOrderSubscribeMessage(order, user, orderGoods, true);
    }
}
