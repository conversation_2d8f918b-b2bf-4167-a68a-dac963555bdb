package com.logic.code.service;

import com.logic.code.entity.goods.Brand;
import com.logic.code.mapper.BrandMapper;
import com.logic.code.mapper.CommonMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/5/7 22:19
 * @desc
 */
@Service
public class BrandService extends BaseService<Brand>{

    @Resource
    BrandMapper brandMapper;
    @Override
    protected CommonMapper<Brand> getMapper() {
        return brandMapper;
    }
}
