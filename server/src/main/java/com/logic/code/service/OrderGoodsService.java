package com.logic.code.service;

import com.logic.code.entity.order.OrderGoods;
import com.logic.code.mapper.CommonMapper;
import com.logic.code.mapper.OrderGoodsMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/5/7 23:07
 * @desc
 */
@Service
public class OrderGoodsService extends BaseService<OrderGoods> {

    @Resource
    private OrderGoodsMapper orderGoodsMapper;

    @Override
    protected CommonMapper<OrderGoods> getMapper() {
        return orderGoodsMapper;
    }
}

