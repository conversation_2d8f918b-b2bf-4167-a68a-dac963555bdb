package com.logic.code.service;

import com.logic.code.entity.order.Order;
import com.logic.code.mapper.OrderMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 支付事件处理器
 * 处理支付成功、失败、取消等事件
 */
@Service
@Slf4j
public class PaymentEventHandler {
    
    @Autowired
    private OrderMapper orderMapper;
    
    @Autowired
    private PromotionCommissionService promotionCommissionService;
    
    /**
     * 处理支付成功事件
     * @param orderId 订单ID
     */
    @Transactional
    public void handlePaymentSuccess(Integer orderId) {
        try {
            log.info("处理订单{}支付成功事件", orderId);
            
            // 1. 验证订单状态
            Order order = orderMapper.selectById(orderId);
            if (order == null) {
                log.warn("订单不存在：{}", orderId);
                return;
            }
            
            // 2. 处理推广佣金计算
            boolean commissionProcessed = promotionCommissionService.processPromotionCommissionAfterPayment(orderId);
            if (commissionProcessed) {
                log.info("订单{}推广佣金处理成功", orderId);
            } else {
                log.warn("订单{}推广佣金处理失败", orderId);
            }
            
            // 3. 其他支付成功后的业务逻辑
            // 例如：发送通知、更新库存、积分奖励等
            
        } catch (Exception e) {
            log.error("处理订单{}支付成功事件失败", orderId, e);
            // 不抛出异常，避免影响支付流程
        }
    }
    
    /**
     * 处理订单取消事件
     * @param orderId 订单ID
     */
    @Transactional
    public void handleOrderCancel(Integer orderId) {
        try {
            log.info("处理订单{}取消事件", orderId);
            
            // 1. 回滚推广佣金
            boolean rollbackProcessed = promotionCommissionService.rollbackPromotionCommissionAfterCancel(orderId);
            if (rollbackProcessed) {
                log.info("订单{}推广佣金回滚成功", orderId);
            } else {
                log.warn("订单{}推广佣金回滚失败", orderId);
            }
            
            // 2. 其他订单取消后的业务逻辑
            // 例如：恢复库存、退还积分、发送通知等
            
        } catch (Exception e) {
            log.error("处理订单{}取消事件失败", orderId, e);
        }
    }
    
    /**
     * 处理确认收货事件
     * @param orderId 订单ID
     */
    @Transactional
    public void handleOrderConfirm(Integer orderId) {
        try {
            log.info("处理订单{}确认收货事件", orderId);
            
            // 确认收货后，将推广收益状态从pending改为confirmed
            promotionCommissionService.confirmEarningsAfterOrderConfirm(orderId);
            
        } catch (Exception e) {
            log.error("处理订单{}确认收货事件失败", orderId, e);
        }
    }
}