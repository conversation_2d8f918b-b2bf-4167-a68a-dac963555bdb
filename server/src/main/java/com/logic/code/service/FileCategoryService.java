package com.logic.code.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logic.code.entity.file.FileCategory;

import java.util.List;

/**
 * File category service interface
 */
public interface FileCategoryService {

    /**
     * Get category list
     * @param pid Parent category ID
     * @return List of categories
     */
    List<FileCategory> getCategoryList(Integer pid);

    /**
     * Get category tree
     * @return Category tree
     */
    List<FileCategory> getCategoryTree();

    /**
     * Get category by ID
     * @param id Category ID
     * @return Category
     */
    FileCategory getById(Integer id);

    /**
     * Create category
     * @param category Category to create
     * @return Created category
     */
    FileCategory createCategory(FileCategory category);

    /**
     * Update category
     * @param category Category to update
     * @return Updated category
     */
    boolean updateCategory(FileCategory category);

    /**
     * Delete category
     * @param id Category ID
     * @return True if deleted successfully
     */
    boolean deleteCategory(Integer id);

    /**
     * Batch delete categories
     * @param ids Category IDs
     * @return True if deleted successfully
     */
    boolean batchDeleteCategories(List<Integer> ids);

    /**
     * Move category to another parent
     * @param id Category ID
     * @param newParentId New parent category ID
     * @return True if moved successfully
     */
    boolean moveCategory(Integer id, Integer newParentId);

    /**
     * Update category sort order
     * @param id Category ID
     * @param sortOrder New sort order
     * @return True if updated successfully
     */
    boolean updateSortOrder(Integer id, Integer sortOrder);

    /**
     * Check if category name exists under the same parent
     * @param title Category name
     * @param parentId Parent category ID
     * @param excludeId Exclude category ID (for update)
     * @return True if exists
     */
    boolean existsByTitleAndParentId(String title, Integer parentId, Integer excludeId);

    /**
     * Get category path
     * @param id Category ID
     * @return Category path list
     */
    List<FileCategory> getCategoryPath(Integer id);

    /**
     * Get all descendant categories
     * @param id Category ID
     * @return List of descendant categories
     */
    List<FileCategory> getDescendantCategories(Integer id);

    /**
     * Get category statistics
     * @param id Category ID
     * @return Category with file count
     */
    FileCategory getCategoryWithStats(Integer id);

    /**
     * Search categories by title
     * @param title Search keyword
     * @return List of matching categories
     */
    List<FileCategory> searchCategories(String title);
}
