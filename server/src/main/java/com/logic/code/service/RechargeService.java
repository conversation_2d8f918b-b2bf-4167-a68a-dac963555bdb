package com.logic.code.service;

import com.github.binarywang.wxpay.bean.result.WxPayUnifiedOrderV3Result;
import com.logic.code.entity.RechargeRecord;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 充值服务接口
 * <AUTHOR>
 * @date 2025/7/24
 */
public interface RechargeService {
    
    /**
     * 创建充值订单
     * @param userId 用户ID
     * @param amount 充值金额
     * @return 充值记录
     */
    RechargeRecord createRechargeOrder(Integer userId, BigDecimal amount);
    
    /**
     * 获取充值预支付信息
     * @param rechargeId 充值记录ID
     * @return 微信支付预支付结果
     */
    WxPayUnifiedOrderV3Result.JsapiResult prepayRecharge(Integer rechargeId);
    
    /**
     * 处理充值支付成功回调
     * @param rechargeId 充值记录ID
     * @param wxTransactionId 微信交易号
     * @return 处理结果
     */
    boolean handleRechargePaySuccess(Integer rechargeId, String wxTransactionId);
    
    /**
     * 获取用户充值记录
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 充值记录列表
     */
    List<RechargeRecord> getUserRechargeRecords(Integer userId, Integer page, Integer size);
    
    /**
     * 获取充值配置信息
     * @return 充值配置
     */
    Map<String, Object> getRechargeConfig();
    
    /**
     * 取消充值订单
     * @param rechargeId 充值记录ID
     * @param userId 用户ID
     * @return 取消结果
     */
    boolean cancelRechargeOrder(Integer rechargeId, Integer userId);
}