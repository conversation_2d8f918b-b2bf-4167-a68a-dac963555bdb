package com.logic.code.service;

import com.logic.code.entity.Address;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * 运费计算服务
 * <AUTHOR>
 * @date 2025/8/3
 */
@Service
@Slf4j
public class FreightService {

    @Resource
    private RegionService regionService;

    /**
     * 需要加收运费的特殊地区省份名称
     */
    private static final List<String> SPECIAL_REGIONS = Arrays.asList(
        "香港", "澳门", "台湾", "新疆", "西藏", "海南"
    );

    /**
     * 特殊地区运费加收金额
     */
    private static final BigDecimal SPECIAL_REGION_FREIGHT = new BigDecimal("5.00");

    /**
     * 计算运费
     * @param address 收货地址
     * @param goodsTotalPrice 商品总价
     * @return 运费金额
     */
    public BigDecimal calculateFreight(Address address, BigDecimal goodsTotalPrice) {
        if (address == null) {
            log.warn("地址为空，返回默认运费0");
            return BigDecimal.ZERO;
        }

        // 获取省份名称
        String provinceName = regionService.queryNameById(address.getProvinceId());
        if (provinceName == null) {
            log.warn("无法获取省份名称，provinceId: {}", address.getProvinceId());
            return BigDecimal.ZERO;
        }

        log.info("计算运费 - 省份: {}, 商品总价: {}", provinceName, goodsTotalPrice);

        // 检查是否为特殊地区
        if (isSpecialRegion(provinceName)) {
            log.info("特殊地区运费计算 - 省份: {}, 加收运费: {}", provinceName, SPECIAL_REGION_FREIGHT);
            return SPECIAL_REGION_FREIGHT;
        }

        // 其他地区默认免运费
        log.info("普通地区运费计算 - 省份: {}, 运费: 0", provinceName);
        return BigDecimal.ZERO;
    }

    /**
     * 判断是否为特殊地区
     * @param provinceName 省份名称
     * @return 是否为特殊地区
     */
    private boolean isSpecialRegion(String provinceName) {
        if (provinceName == null) {
            return false;
        }

        // 精确匹配或包含匹配
        for (String specialRegion : SPECIAL_REGIONS) {
            if (provinceName.equals(specialRegion) || 
                provinceName.contains(specialRegion) || 
                specialRegion.contains(provinceName)) {
                return true;
            }
        }

        // 特殊处理一些可能的变体
        if (provinceName.contains("香港") || provinceName.contains("澳门") || 
            provinceName.contains("台湾") || provinceName.contains("新疆") || 
            provinceName.contains("西藏") || provinceName.contains("海南")) {
            return true;
        }

        return false;
    }

    /**
     * 获取特殊地区运费金额
     * @return 特殊地区运费金额
     */
    public BigDecimal getSpecialRegionFreight() {
        return SPECIAL_REGION_FREIGHT;
    }

    /**
     * 获取特殊地区列表
     * @return 特殊地区列表
     */
    public List<String> getSpecialRegions() {
        return SPECIAL_REGIONS;
    }
}