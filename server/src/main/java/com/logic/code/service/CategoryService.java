package com.logic.code.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.logic.code.common.enmus.CategoryLevelEnum;
import com.logic.code.entity.goods.Category;
import com.logic.code.mapper.CategoryMapper;
import com.logic.code.mapper.CommonMapper;
import com.logic.code.model.query.admin.CategoryParam;
import com.logic.code.model.vo.CategoryCascaderVO;
import com.logic.code.model.vo.CategoryIndexVO;
import com.logic.code.model.vo.CategoryVO;
import jakarta.annotation.Resource;
import logic.orm.WrapperBuilder;
import logic.orm.utils.ListUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/7 22:45
 * @desc
 */
@Service
public class CategoryService extends BaseService<Category> {

    @Resource
    private CategoryMapper categoryMapper;

    @Override
    protected CommonMapper<Category> getMapper() {
        return categoryMapper;
    }

    public List<Integer> queryIdsByParentId(Integer parentId) {
        return categoryMapper.selectIdsByParentId(parentId);
    }

    public List<Integer> queryParentIdsByIdIn(List<Integer> ids) {
        return categoryMapper.selectParentIdsByIdIn(ids);
    }

    public List<Category> queryByIdIn(List<Integer> ids) {
        return categoryMapper.selectByIds(ids);
    }


    public List<Category> queryCategoryByLevel(CategoryLevelEnum categoryLevel) {
        return queryList(Category.builder().level(categoryLevel).build());
    }

    public CategoryIndexVO index(Integer categoryId) {
        List<CategoryVO> categoryList = new LinkedList<>();
        queryByCriteria(Criteria.of(Category.class).andEqualTo(Category::getParentId, 0).page(1, 10)).forEach(c -> {
            CategoryVO categoryDTO = new CategoryVO(c);
            List<Category> subCategoryList = queryList(Category.builder().parentId(c.getId()).build());
            categoryDTO.setSubCategoryList(subCategoryList);
            categoryList.add(categoryDTO);
        });

        CategoryVO currentCategory;
        if (categoryId == null) {
            currentCategory = categoryList.get(0);
        } else {
            currentCategory = new CategoryVO(queryById(categoryId));
        }
        return new CategoryIndexVO(categoryList, currentCategory);
    }


    public CategoryVO current(Integer id) {
        CategoryVO categoryDTO = new CategoryVO(queryById(id));
        List<Category> subCategoryList = queryList(Category.builder().parentId(id).build());
        categoryDTO.setSubCategoryList(subCategoryList);
        return categoryDTO;
    }


    public List<CategoryVO> queryCategoryList(CategoryParam param) {
        List<CategoryVO> categoryList = new LinkedList<>();
        if(Objects.isNull(param.getParentId()))param.setParentId(0);
        List<Category> list = queryList(param);
        if (ListUtils.isNotBlank(list)) {
            for (Category category : list) {
                CategoryVO vo = new CategoryVO(category);
                List<Category> subCategoryList = queryList(Category.builder().parentId(category.getId()).build());
                vo.setSubCategoryList(subCategoryList);
                categoryList.add(vo);
            }
        }
        return categoryList;
    }

    public List<Category> list(Integer parentId) {
        List<Category> list = queryList(Category.builder().parentId(parentId).build());
        return list;
    }
    
    /**
     * Build category tree for el-cascader component
     * @param type Category type
     * @return List of CategoryCascaderVO with nested children
     */
    public List<CategoryCascaderVO> buildCascaderTree(Integer type) {
        // Get all categories of the specified type
        List<Category> allCategories = queryList(Category.builder().type(type).build());
        if (ListUtils.isBlank(allCategories)) {
            return new ArrayList<>();
        }
        
        // Filter root categories (parentId = 0)
        List<CategoryCascaderVO> result = allCategories.stream()
                .filter(c -> c.getParentId() == 0)
                .map(this::convertToCascaderVO)
                .collect(Collectors.toList());
        
        // Build tree recursively
        for (CategoryCascaderVO vo : result) {
            buildChildrenTree(vo, allCategories);
        }
        
        return result;
    }
    
    /**
     * Convert Category to CategoryCascaderVO
     */
    private CategoryCascaderVO convertToCascaderVO(Category category) {
        return new CategoryCascaderVO(category);
    }
    
    /**
     * Build children tree recursively
     */
    private void buildChildrenTree(CategoryCascaderVO parent, List<Category> allCategories) {
        List<CategoryCascaderVO> children = allCategories.stream()
                .filter(c -> Objects.equals(c.getParentId(), parent.getValue()))
                .map(this::convertToCascaderVO)
                .collect(Collectors.toList());
        
        if (!children.isEmpty()) {
            parent.setChildren(children);
            
            // Process each child recursively
            for (CategoryCascaderVO child : children) {
                buildChildrenTree(child, allCategories);
            }
        }
    }
}
