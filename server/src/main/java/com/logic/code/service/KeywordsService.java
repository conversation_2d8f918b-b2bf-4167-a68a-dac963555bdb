package com.logic.code.service;

import com.logic.code.entity.Keywords;
import com.logic.code.mapper.CommonMapper;
import com.logic.code.mapper.KeywordsMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/8 9:52
 * @desc
 */
@Service
public class KeywordsService extends BaseService<Keywords>{

    @Resource
    private KeywordsMapper keywordsMapper;

    @Override
    protected CommonMapper<Keywords> getMapper() {
        return keywordsMapper;
    }

    public List<String> queryByKeyword(String keyword) {
        return keywordsMapper.selectByKeywordLike(keyword);
    }
}
