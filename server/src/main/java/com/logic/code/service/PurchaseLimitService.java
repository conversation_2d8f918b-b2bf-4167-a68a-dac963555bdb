package com.logic.code.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.logic.code.common.enmus.OrderStatusEnum;
import com.logic.code.common.enmus.PayStatusEnum;
import com.logic.code.entity.goods.Goods;
import com.logic.code.entity.order.Order;
import com.logic.code.entity.order.OrderGoods;
import com.logic.code.mapper.CommonMapper;
import com.logic.code.mapper.OrderGoodsMapper;
import com.logic.code.mapper.OrderMapper;
import com.logic.code.model.vo.PurchaseLimitVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 商品限购服务
 * <AUTHOR>
 * @date 2025/8/17
 */
@Service
@Slf4j
public class PurchaseLimitService {

    @Resource
    private GoodsService goodsService;

    @Resource
    private OrderMapper orderMapper;

    @Resource
    private OrderGoodsMapper orderGoodsMapper;

    /**
     * 检查商品限购状态
     * @param goodsId 商品ID
     * @param userId 用户ID
     * @return 限购信息
     */
    public PurchaseLimitVO checkPurchaseLimit(Integer goodsId, Integer userId) {
        // 获取商品信息
        Goods goods = goodsService.queryById(goodsId);
        if (goods == null) {
            return PurchaseLimitVO.builder()
                .isLimited(false)
                .canPurchase(false)
                .message("商品不存在")
                .build();
        }

        // 检查是否为限购商品
        if (!Boolean.TRUE.equals(goods.getIsLimited()) || goods.getLimitNum() == null || goods.getLimitNum() <= 0) {
            return PurchaseLimitVO.builder()
                .isLimited(false)
                .canPurchase(true)
                .message("非限购商品")
                .build();
        }

        // 统计用户已购买数量
        Integer purchasedCount = getUserPurchasedCount(goodsId, userId);
        Integer limitNum = goods.getLimitNum();
        Integer remainingCount = limitNum - purchasedCount;

        boolean canPurchase = remainingCount > 0;
        String message = canPurchase ? 
            String.format("限购%d件，您已购买%d件，还可购买%d件", limitNum, purchasedCount, remainingCount) :
            String.format("限购%d件，您已购买%d件，已达购买上限", limitNum, purchasedCount);

        return PurchaseLimitVO.builder()
            .isLimited(true)
            .limitNum(limitNum)
            .purchasedCount(purchasedCount)
            .remainingCount(Math.max(0, remainingCount))
            .canPurchase(canPurchase)
            .message(message)
            .build();
    }

    /**
     * 验证购买数量是否超出限制
     * @param goodsId 商品ID
     * @param userId 用户ID
     * @param purchaseCount 本次购买数量
     * @return 是否可以购买
     */
    public boolean validatePurchaseCount(Integer goodsId, Integer userId, Integer purchaseCount) {
        PurchaseLimitVO limitInfo = checkPurchaseLimit(goodsId, userId);
        
        if (!limitInfo.getIsLimited()) {
            return true; // 非限购商品，可以购买
        }

        return limitInfo.getRemainingCount() >= purchaseCount;
    }

    /**
     * 获取用户对指定商品的已购买数量
     * @param goodsId 商品ID
     * @param userId 用户ID
     * @return 已购买数量
     */
    private Integer getUserPurchasedCount(Integer goodsId, Integer userId) {
        // 查询用户已支付订单中该商品的购买数量
        QueryWrapper<Order> orderWrapper = new QueryWrapper<>();
        orderWrapper.eq("user_id", userId)
                   .eq("pay_status", PayStatusEnum.PAID.getValue())
                   .ne("order_status", OrderStatusEnum.CANCELLED.getValue()); // 排除已取消的订单

        List<Order> paidOrders = orderMapper.selectList(orderWrapper);
        
        if (paidOrders.isEmpty()) {
            return 0;
        }

        // 统计这些订单中该商品的购买数量
        Integer totalCount = 0;
        for (Order order : paidOrders) {
            QueryWrapper<OrderGoods> goodsWrapper = new QueryWrapper<>();
            goodsWrapper.eq("order_id", order.getId())
                       .eq("goods_id", goodsId);
            
            List<OrderGoods> orderGoodsList = orderGoodsMapper.selectList(goodsWrapper);
            for (OrderGoods orderGoods : orderGoodsList) {
                totalCount += orderGoods.getNumber();
            }
        }

        return totalCount;
    }

    /**
     * 获取商品的限购信息（用于前端显示）
     * @param goodsId 商品ID
     * @return 限购信息
     */
    public PurchaseLimitVO getGoodsLimitInfo(Integer goodsId) {
        Goods goods = goodsService.queryById(goodsId);
        if (goods == null) {
            return PurchaseLimitVO.builder()
                .isLimited(false)
                .canPurchase(false)
                .message("商品不存在")
                .build();
        }

        if (!Boolean.TRUE.equals(goods.getIsLimited()) || goods.getLimitNum() == null || goods.getLimitNum() <= 0) {
            return PurchaseLimitVO.builder()
                .isLimited(false)
                .canPurchase(true)
                .message("非限购商品")
                .build();
        }

        return PurchaseLimitVO.builder()
            .isLimited(true)
            .limitNum(goods.getLimitNum())
            .canPurchase(true)
            .message(String.format("限购商品，每人限购%d件", goods.getLimitNum()))
            .build();
    }
}