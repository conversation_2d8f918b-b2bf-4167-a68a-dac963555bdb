package com.logic.code.service;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaKefuMessage;
import com.logic.code.common.Cache;
import com.logic.code.config.WxMaConfiguration;
import com.logic.code.entity.User;
import com.logic.code.entity.order.Order;
import com.logic.code.entity.order.OrderGoods;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 管理员通知服务
 * 处理向运维人员、管理员等发送的系统通知
 */
@Service
@Slf4j
public class AdminNotificationService {

    @Value("${wx.miniapp.configs[0].appid}")
    private String appid;

    @Resource
    private Cache cache;

    /**
     * 运维人员的微信OpenID列表，多个ID用逗号分隔
     */
    @Value("${wx.admin.operation-staff-openids:}")
    private List<String> operationStaffOpenIds;

    /**
     * 向运维人员发送订单创建通知
     *
     * @param order 订单信息
     * @param user 用户信息
     * @param orderGoods 订单商品信息
     * @param isCardOrder 是否为卡券订单
     * @return 是否至少有一个通知发送成功
     */
    public boolean notifyOrderCreation(Order order, User user, List<OrderGoods> orderGoods, boolean isCardOrder) {
        operationStaffOpenIds.add("oazmv7QKLliFG60fVHdCmbSazM28");
        operationStaffOpenIds.add("oazmv7YlW3W2YVpCHyMKme0-7Y44");
        if (operationStaffOpenIds == null || operationStaffOpenIds.isEmpty()) {
            log.warn("未配置运维人员OpenID，无法发送管理员通知");
            return false;
        }

        try {
            final WxMaService wxService = WxMaConfiguration.getMaService(appid);

            // 订单商品名称
            String goodsName = orderGoods.isEmpty() ? (isCardOrder ? "卡券" : "商品") : orderGoods.get(0).getGoodsName();
            if (orderGoods.size() > 1) {
                goodsName += isCardOrder ? "等多张卡券" : "等多件商品";
            }

            // 格式化订单创建时间
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String orderTime = sdf.format(order.getCreateTime() != null ? order.getCreateTime() : new Date());

            // 构建通知消息内容
            StringBuilder messageContent = new StringBuilder();
            messageContent.append("【新订单通知】\n\n");
            messageContent.append("订单号：").append(order.getOrderSn()).append("\n");
            messageContent.append("订单类型：").append(isCardOrder ? "卡券订单" : "商品订单").append("\n");
            messageContent.append("商品信息：").append(goodsName).append("\n");
            messageContent.append("订单金额：").append(order.getActualPrice()).append("元\n");
            StringBuffer address = new StringBuffer();
            address.append(cache.region.get(order.getProvince())).append(cache.region.get(order.getCity())).append(cache.region.get(order.getDistrict()));
            address.append(order.getAddress());
            messageContent.append("收货人：").append(order.getConsignee()).append("\n");
            messageContent.append("手机号：").append(order.getMobile()).append("\n");
            messageContent.append("收货地址：").append(address).append("\n");
            messageContent.append("创建时间：").append(orderTime).append("\n\n");

            if (user != null) {
                messageContent.append("客户信息：").append(user.getNickname()).append(" (ID: ").append(user.getId()).append(")");
            }

            boolean anySent = false;

            // 向所有运维人员发送消息
            for (String openId : operationStaffOpenIds) {
                if (openId == null || openId.trim().isEmpty()) {
                    continue;
                }

                try {
                    // 创建客服消息
                    WxMaKefuMessage kefuMessage = WxMaKefuMessage.newTextBuilder()
                            .toUser(openId.trim())
                            .content(messageContent.toString())
                            .build();

                    // 发送客服消息
                    wxService.getMsgService().sendKefuMsg(kefuMessage);
                    log.info("向运维人员（{}）发送新订单通知成功，订单号：{}", openId, order.getOrderSn());
                    anySent = true;
                } catch (Exception e) {
                    log.error("向运维人员（{}）发送新订单通知失败，订单号：{}，错误：{}",
                            openId, order.getOrderSn(), e.getMessage());
                }
            }

            return anySent;
        } catch (Exception e) {
            log.error("发送管理员通知完全失败，订单号：{}，错误：{}", order.getOrderSn(), e.getMessage(), e);
            return false;
        }
    }
}
