package com.logic.code.service;

import com.logic.code.entity.GoodsCardConfig;
import com.logic.code.mapper.GoodsCardConfigMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/4 10:12
 * @desc
 */

@Service
public class GoodsCardConfigService {

    @Resource
    private GoodsCardConfigMapper goodsCardConfigMapper;

    public List<GoodsCardConfig> list() {
        return goodsCardConfigMapper.selectList(null);
    }

}
