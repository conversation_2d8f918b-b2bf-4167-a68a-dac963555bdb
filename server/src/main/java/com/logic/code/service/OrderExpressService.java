package com.logic.code.service;

import com.logic.code.entity.order.OrderExpress;
import com.logic.code.mapper.CommonMapper;
import com.logic.code.mapper.OrderExpressMapper;
import jakarta.annotation.Resource;
import logic.orm.WrapperBuilder;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/5/7 23:08
 * @desc
 */
@Service
public class OrderExpressService extends BaseService<OrderExpress> {

    @Resource
    private OrderExpressMapper orderExpressMapper;

    @Override
    protected CommonMapper<OrderExpress> getMapper() {
        return orderExpressMapper;
    }

    public OrderExpress queryByOrderId(Integer orderId) {
        return queryOne(OrderExpress.builder().orderId(orderId).build());
    }
}
