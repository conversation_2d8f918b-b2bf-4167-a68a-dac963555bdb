package com.logic.code.service;

import com.logic.code.common.utils.JwtHelper;
import com.logic.code.entity.Address;
import com.logic.code.entity.User;
import com.logic.code.mapper.AddressMapper;
import com.logic.code.mapper.CommonMapper;
import com.logic.code.model.vo.AddressVO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/7 21:45
 * @desc
 */
@Service
public class AddressService extends BaseService<Address> {

    @Resource
    private AddressMapper addressMapper;

    @Resource
    private RegionService regionService;

    @Override
    protected CommonMapper<Address> getMapper() {
        return addressMapper;
    }


    public AddressVO queryDetail(Integer id) {
        Address address = queryById(id);
        AddressVO addressDTO = null;
        if (address != null) {
            addressDTO = new AddressVO(address)
                    .setProvinceName(
                            regionService.queryNameById(address.getProvinceId())
                    )
                    .setCityName(
                            regionService.queryNameById(address.getCityId())
                    )
                    .setDistrictName(
                            regionService.queryNameById(address.getDistrictId())
                    );

            addressDTO.setFullRegion(
                    addressDTO.getProvinceName() + addressDTO.getCityName() + addressDTO.getDistrictName()
            );
        }
        return addressDTO;
    }

    public List<AddressVO> queryDetailList() {
        User userInfo = JwtHelper.getUserInfo();
        List<Address> addressList = queryList(new Address().setUserId(userInfo.getId()));
        LinkedList<AddressVO> addressDTOList = new LinkedList<>();
        for (Address address : addressList) {
            AddressVO addressDTO = new AddressVO(address)
                    .setProvinceName(
                            regionService.queryNameById(address.getProvinceId())
                    )
                    .setCityName(
                            regionService.queryNameById(address.getCityId())
                    )
                    .setDistrictName(
                            regionService.queryNameById(address.getDistrictId())
                    ).setIsDefault(address.getIsDefault());
            addressDTO.setFullRegion(
                    addressDTO.getProvinceName() + addressDTO.getCityName() + addressDTO.getDistrictName()
            );
            addressDTOList.add(addressDTO);
        }
        return addressDTOList;
    }


    public boolean serDefault(Integer id) {

        Address address = addressMapper.selectById(id);
        if (address != null) {
            address.setIsDefault(true);
            return addressMapper.updateById(address) == 1;
        }
        return false;
    }


    public Address createEntity(Address address) {
        int insert = addressMapper.insert(address);
        return address;
    }
}
