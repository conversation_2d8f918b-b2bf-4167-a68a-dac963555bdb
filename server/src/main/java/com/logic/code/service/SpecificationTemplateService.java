package com.logic.code.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.logic.code.entity.goods.SpecificationTemplate;
import com.logic.code.mapper.CommonMapper;
import com.logic.code.mapper.SpecificationTemplateMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 规格模板服务类
 */
@Service
@Slf4j
public class SpecificationTemplateService extends BaseService<SpecificationTemplate> {

    @Resource
    private SpecificationTemplateMapper specificationTemplateMapper;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    protected CommonMapper<SpecificationTemplate> getMapper() {
        return specificationTemplateMapper;
    }

    /**
     * 分页查询规格模板
     */
    public Page<SpecificationTemplate> getPage(String ruleName, Integer page, Integer limit) {
        Page<SpecificationTemplate> pageInfo = new Page<>(page, limit);
        return specificationTemplateMapper.selectPage(pageInfo, ruleName);
    }

    /**
     * 查询所有启用的规格模板
     */
    public List<SpecificationTemplate> queryAllEnabled() {
        return specificationTemplateMapper.selectAllEnabled();
    }

    /**
     * 根据规格模板名称查询
     */
    public SpecificationTemplate findByRuleName(String ruleName) {
        return specificationTemplateMapper.selectByRuleName(ruleName);
    }

    /**
     * 保存规格模板
     */
    public SpecificationTemplate saveTemplate(Map<String, Object> templateData, Integer id) {
        SpecificationTemplate template = new SpecificationTemplate();
        
        if (id != null && id > 0) {
            // 更新
            template = queryById(id);
            if (template == null) {
                throw new RuntimeException("规格模板不存在");
            }
            template.setUpdateTime(new Date());
        } else {
            // 新增
            template.setCreateTime(new Date());
            template.setUpdateTime(new Date());
            template.setStatus(1); // 默认启用
        }

        // 设置基本信息
        template.setRuleName((String) templateData.get("rule_name"));
        
        // 处理规格数据
        List<Map<String, Object>> specs = (List<Map<String, Object>>) templateData.get("spec");
        if (specs != null && !specs.isEmpty()) {
            // 取第一个规格作为主规格名称
            Map<String, Object> firstSpec = specs.get(0);
            template.setAttrName((String) firstSpec.get("value"));
            
            // 将所有规格数据转换为JSON存储
            try {
                String attrValueJson = objectMapper.writeValueAsString(specs);
                template.setAttrValue(attrValueJson);
            } catch (JsonProcessingException e) {
                log.error("Failed to serialize specification data", e);
                throw new RuntimeException("规格数据序列化失败");
            }
        }

        if (template.getId() != null && template.getId() > 0) {
            updateById(template);
        } else {
            create(template);
        }

        return template;
    }

    /**
     * 获取规格模板详情，转换为前端需要的格式
     */
    public Map<String, Object> getTemplateInfo(Integer id) {
        SpecificationTemplate template = queryById(id);
        if (template == null) {
            throw new RuntimeException("规格模板不存在");
        }

        Map<String, Object> result = new HashMap<>();
        result.put("rule_name", template.getRuleName());
        
        // 解析规格数据
        try {
            if (template.getAttrValue() != null && !template.getAttrValue().isEmpty()) {
                List<Map<String, Object>> specs = objectMapper.readValue(
                    template.getAttrValue(), 
                    objectMapper.getTypeFactory().constructCollectionType(List.class, Map.class)
                );
                result.put("spec", specs);
            } else {
                result.put("spec", new ArrayList<>());
            }
        } catch (JsonProcessingException e) {
            log.error("Failed to deserialize specification data for template {}", id, e);
            result.put("spec", new ArrayList<>());
        }

        return result;
    }

    /**
     * 获取规格模板列表，转换为前端需要的格式
     */
    public List<Map<String, Object>> getTemplateList(String ruleName, Integer page, Integer limit) {
        Page<SpecificationTemplate> pageResult = getPage(ruleName, page, limit);
        List<Map<String, Object>> result = new ArrayList<>();

        for (SpecificationTemplate template : pageResult.getRecords()) {
            Map<String, Object> item = new HashMap<>();
            item.put("id", template.getId());
            item.put("rule_name", template.getRuleName());
            item.put("attr_name", template.getAttrName());

            // 解析规格值，转换为前端期望的格式
            try {
                if (template.getAttrValue() != null && !template.getAttrValue().isEmpty()) {
                    List<Map<String, Object>> specs = objectMapper.readValue(
                        template.getAttrValue(),
                        objectMapper.getTypeFactory().constructCollectionType(List.class, Map.class)
                    );

                    // 转换为前端期望的格式
                    List<Map<String, Object>> frontendSpecs = new ArrayList<>();
                    for (Map<String, Object> spec : specs) {
                        Map<String, Object> frontendSpec = new HashMap<>();

                        // 获取规格名称
                        String specName = (String) spec.get("name");
                        if (specName == null) {
                            specName = (String) spec.get("value");
                        }
                        frontendSpec.put("value", specName);
                        frontendSpec.put("add_pic", 0);

                        // 转换规格值
                        List<Map<String, Object>> details = new ArrayList<>();
                        List<String> values = (List<String>) spec.get("values");
                        if (values == null) {
                            values = (List<String>) spec.get("detail");
                        }

                        if (values != null) {
                            for (String value : values) {
                                Map<String, Object> detail = new HashMap<>();
                                detail.put("value", value);
                                detail.put("pic", "");
                                details.add(detail);
                            }
                        }

                        frontendSpec.put("detail", details);
                        frontendSpecs.add(frontendSpec);
                    }

                    item.put("rule_value", frontendSpecs);
                } else {
                    item.put("rule_value", new ArrayList<>());
                }
            } catch (JsonProcessingException e) {
                log.error("Failed to deserialize specification data for template {}", template.getId(), e);
                item.put("rule_value", new ArrayList<>());
            }

            result.add(item);
        }

        return result;
    }

    /**
     * 获取分页总数
     */
    public long getPageTotal(String ruleName) {
        Page<SpecificationTemplate> pageInfo = new Page<>(1, 1);
        Page<SpecificationTemplate> result = specificationTemplateMapper.selectPage(pageInfo, ruleName);
        return result.getTotal();
    }
}
