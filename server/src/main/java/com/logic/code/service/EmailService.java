package com.logic.code.service;

import com.logic.code.config.EmailProperties;
import jakarta.mail.internet.MimeMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.File;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 邮件发送服务
 *
 * <AUTHOR>
 * @date 2025/7/13
 */
@Slf4j
@Service
public class EmailService {

    @Autowired
    private JavaMailSender mailSender;

    @Autowired
    private EmailProperties emailProperties;

    /**
     * 发送简单文本邮件
     *
     * @param to 收件人
     * @param subject 主题
     * @param content 内容
     * @return 是否发送成功
     */
    public boolean sendSimpleEmail(String to, String subject, String content) {
        return sendSimpleEmail(new String[]{to}, subject, content);
    }

    /**
     * 发送简单文本邮件（多个收件人）
     *
     * @param to 收件人数组
     * @param subject 主题
     * @param content 内容
     * @return 是否发送成功
     */
    public boolean sendSimpleEmail(String[] to, String subject, String content) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(emailProperties.getDefaultFrom());
            message.setTo(to);
            message.setSubject(subject);
            message.setText(content);
            message.setSentDate(new Date());

            mailSender.send(message);
            log.info("简单邮件发送成功，收件人：{}，主题：{}", Arrays.toString(to), subject);
            return true;
        } catch (Exception e) {
            log.error("简单邮件发送失败，收件人：{}，主题：{}，错误：{}", Arrays.toString(to), subject, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 发送HTML邮件
     *
     * @param to 收件人
     * @param subject 主题
     * @param htmlContent HTML内容
     * @return 是否发送成功
     */
    public boolean sendHtmlEmail(String to, String subject, String htmlContent) {
        return sendHtmlEmail(new String[]{to}, subject, htmlContent);
    }

    /**
     * 发送HTML邮件（多个收件人）
     *
     * @param to 收件人数组
     * @param subject 主题
     * @param htmlContent HTML内容
     * @return 是否发送成功
     */
    public boolean sendHtmlEmail(String[] to, String subject, String htmlContent) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            helper.setFrom(emailProperties.getDefaultFrom(), emailProperties.getDefaultFromName());
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(htmlContent, true);
            helper.setSentDate(new Date());

            mailSender.send(message);
            log.info("HTML邮件发送成功，收件人：{}，主题：{}", Arrays.toString(to), subject);
            return true;
        } catch (Exception e) {
            log.error("HTML邮件发送失败，收件人：{}，主题：{}，错误：{}", Arrays.toString(to), subject, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 发送带附件的邮件
     *
     * @param to 收件人
     * @param subject 主题
     * @param content 内容
     * @param attachmentPath 附件文件路径
     * @return 是否发送成功
     */
    public boolean sendEmailWithAttachment(String to, String subject, String content, String attachmentPath) {
        return sendEmailWithAttachment(new String[]{to}, subject, content, new String[]{attachmentPath});
    }

    /**
     * 发送带附件的邮件（多个收件人，多个附件）
     *
     * @param to 收件人数组
     * @param subject 主题
     * @param content 内容
     * @param attachmentPaths 附件文件路径数组
     * @return 是否发送成功
     */
    public boolean sendEmailWithAttachment(String[] to, String subject, String content, String[] attachmentPaths) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            helper.setFrom(emailProperties.getDefaultFrom(), emailProperties.getDefaultFromName());
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(content, true);
            helper.setSentDate(new Date());

            // 添加附件
            if (attachmentPaths != null && attachmentPaths.length > 0) {
                for (String attachmentPath : attachmentPaths) {
                    if (StringUtils.hasText(attachmentPath)) {
                        File file = new File(attachmentPath);
                        if (file.exists() && file.isFile()) {
                            // 验证文件大小
                            if (file.length() > emailProperties.getAttachment().getMaxSize() * 1024 * 1024) {
                                log.warn("附件文件过大，跳过：{}，大小：{}MB", attachmentPath, file.length() / 1024 / 1024);
                                continue;
                            }

                            FileSystemResource fileResource = new FileSystemResource(file);
                            helper.addAttachment(file.getName(), fileResource);
                            log.debug("添加附件：{}", attachmentPath);
                        } else {
                            log.warn("附件文件不存在或不是文件：{}", attachmentPath);
                        }
                    }
                }
            }

            mailSender.send(message);
            log.info("带附件邮件发送成功，收件人：{}，主题：{}，附件数量：{}",
                    Arrays.toString(to), subject, attachmentPaths != null ? attachmentPaths.length : 0);
            return true;
        } catch (Exception e) {
            log.error("带附件邮件发送失败，收件人：{}，主题：{}，错误：{}", Arrays.toString(to), subject, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 发送系统通知邮件（发送给默认收件人）
     *
     * @param subject 主题
     * @param content 内容
     * @return 是否发送成功
     */
    public boolean sendSystemNotification(String subject, String content) {
        return sendHtmlEmail(emailProperties.getDefaultRecipients(), subject, content);
    }

    /**
     * 发送系统通知邮件带附件（发送给默认收件人）
     *
     * @param subject 主题
     * @param content 内容
     * @param attachmentPaths 附件路径数组
     * @return 是否发送成功
     */
    public boolean sendSystemNotificationWithAttachment(String subject, String content, String[] attachmentPaths) {
        return sendEmailWithAttachment(emailProperties.getDefaultRecipients(), subject, content, attachmentPaths);
    }

    /**
     * 验证邮件地址格式
     *
     * @param email 邮件地址
     * @return 是否有效
     */
    public boolean isValidEmail(String email) {
        if (!StringUtils.hasText(email)) {
            return false;
        }
        return email.matches("^[A-Za-z0-9+_.-]+@([A-Za-z0-9.-]+\\.[A-Za-z]{2,})$");
    }

    /**
     * 发送生成的文件（专门用于发送导出的Excel、PDF等文件）
     *
     * @param recipients 收件人邮箱数组
     * @param fileType 文件类型（如：订单报表、商品统计等）
     * @param filePath 生成的文件路径
     * @param description 文件描述
     * @return 是否发送成功
     */
    public boolean sendGeneratedFile(String[] recipients, String fileType, String filePath, String description) {
        String subject = String.format("[%s] %s - %s",
                emailProperties.getDefaultFromName(),
                fileType,
                new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm").format(new Date()));

        String content = buildFileEmailContent(fileType, description, filePath);

        return sendEmailWithAttachment(recipients, subject, content, new String[]{filePath});
    }

    /**
     * 发送生成的文件给默认收件人
     *
     * @param fileType 文件类型
     * @param filePath 文件路径
     * @param description 文件描述
     * @return 是否发送成功
     */
    public boolean sendGeneratedFileToDefault(String fileType, String filePath, String description) {
        return sendGeneratedFile(emailProperties.getDefaultRecipients(), fileType, filePath, description);
    }

    /**
     * 发送订单报表文件
     *
     * @param recipients 收件人
     * @param filePath 文件路径
     * @param dateRange 日期范围
     * @param recordCount 记录数量
     * @return 是否发送成功
     */
    public boolean sendOrderReport(String[] recipients, String filePath, String dateRange, int recordCount) {
        String description = String.format("订单报表包含 %d 条记录，时间范围：%s", recordCount, dateRange);
        return sendGeneratedFile(recipients, "订单报表", filePath, description);
    }

    /**
     * 发送商品统计文件
     *
     * @param recipients 收件人
     * @param filePath 文件路径
     * @param dateRange 日期范围
     * @param recordCount 记录数量
     * @return 是否发送成功
     */
    public boolean sendProductStatistics(String[] recipients, String filePath, String dateRange, int recordCount) {
        String description = String.format("商品统计报表包含 %d 条记录，时间范围：%s", recordCount, dateRange);
        return sendGeneratedFile(recipients, "商品统计报表", filePath, description);
    }

    /**
     * 发送用户统计文件
     *
     * @param recipients 收件人
     * @param filePath 文件路径
     * @param dateRange 日期范围
     * @param recordCount 记录数量
     * @return 是否发送成功
     */
    public boolean sendUserStatistics(String[] recipients, String filePath, String dateRange, int recordCount) {
        String description = String.format("用户统计报表包含 %d 条记录，时间范围：%s", recordCount, dateRange);
        return sendGeneratedFile(recipients, "用户统计报表", filePath, description);
    }

    /**
     * 构建文件邮件的HTML内容
     *
     * @param fileType 文件类型
     * @param description 文件描述
     * @param filePath 文件路径
     * @return HTML内容
     */
    private String buildFileEmailContent(String fileType, String description, String filePath) {
        File file = new File(filePath);
        String fileName = file.getName();
        long fileSize = file.length();
        String fileSizeStr = formatFileSize(fileSize);

        StringBuilder content = new StringBuilder();
        content.append("<h3>").append(fileType).append("</h3>");
        content.append("<p><strong>文件描述：</strong>").append(description).append("</p>");
        content.append("<p><strong>文件名称：</strong>").append(fileName).append("</p>");
        content.append("<p><strong>文件大小：</strong>").append(fileSizeStr).append("</p>");
        content.append("<p><strong>生成时间：</strong>").append(new Date()).append("</p>");
        content.append("<hr>");
        content.append("<p>请查看附件中的详细数据。如有任何问题，请联系系统管理员。</p>");

        return buildHtmlContent(fileType + " - 文件发送", content.toString());
    }

    /**
     * 格式化文件大小
     *
     * @param size 文件大小（字节）
     * @return 格式化后的文件大小
     */
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.2f KB", size / 1024.0);
        } else {
            return String.format("%.2f MB", size / (1024.0 * 1024.0));
        }
    }

    /**
     * 构建HTML邮件内容
     *
     * @param title 邮件标题
     * @param content 邮件内容
     * @return HTML格式的邮件内容
     */
    public String buildHtmlContent(String title, String content) {
        StringBuilder html = new StringBuilder();
        html.append("<!DOCTYPE html>");
        html.append("<html>");
        html.append("<head>");
        html.append("<meta charset='UTF-8'>");
        html.append("<title>").append(title).append("</title>");
        html.append("<style>");
        html.append("body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }");
        html.append(".container { max-width: 600px; margin: 0 auto; padding: 20px; }");
        html.append(".header { background-color: #f8f9fa; padding: 20px; text-align: center; border-radius: 5px; }");
        html.append(".content { padding: 20px; background-color: #ffffff; border: 1px solid #dee2e6; border-radius: 5px; margin-top: 10px; }");
        html.append(".footer { text-align: center; padding: 10px; color: #6c757d; font-size: 12px; margin-top: 20px; }");
        html.append("</style>");
        html.append("</head>");
        html.append("<body>");
        html.append("<div class='container'>");
        html.append("<div class='header'>");
        html.append("<h2>").append(title).append("</h2>");
        html.append("</div>");
        html.append("<div class='content'>");
        html.append(content);
        html.append("</div>");
        html.append("<div class='footer'>");
        html.append("<p>此邮件由").append(emailProperties.getDefaultFromName()).append("系统自动发送，请勿回复。</p>");
        html.append("<p>发送时间：").append(new Date()).append("</p>");
        html.append("</div>");
        html.append("</div>");
        html.append("</body>");
        html.append("</html>");
        return html.toString();
    }
}
