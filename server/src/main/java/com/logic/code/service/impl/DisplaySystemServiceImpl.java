package com.logic.code.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.logic.code.entity.goods.DisplaySystem;
import com.logic.code.mapper.DisplaySystemMapper;
import com.logic.code.service.DisplaySystemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 展示系统服务实现类
 * 
 * <AUTHOR>
 * @date 2025/7/6
 */
@Service
public class DisplaySystemServiceImpl implements DisplaySystemService {
    
    @Autowired
    private DisplaySystemMapper displaySystemMapper;
    
    @Override
    public List<DisplaySystem> getEnabledSystems() {
        return displaySystemMapper.selectEnabledSystems();
    }
    
    @Override
    public DisplaySystem getById(Integer id) {
        return displaySystemMapper.selectById(id);
    }
    
    @Override
    public List<DisplaySystem> getAllSystems() {
        QueryWrapper<DisplaySystem> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByAsc("sort_order", "id");
        return displaySystemMapper.selectList(queryWrapper);
    }
}
