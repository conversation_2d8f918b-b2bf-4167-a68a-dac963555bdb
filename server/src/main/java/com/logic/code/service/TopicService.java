package com.logic.code.service;

import com.logic.code.entity.Topic;
import com.logic.code.mapper.CommonMapper;
import com.logic.code.mapper.TopicMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/5/7 23:34
 * @desc
 */
@Service
public class TopicService extends BaseService<Topic>{

    @Resource
    private TopicMapper topicMapper;

    @Override
    protected CommonMapper<Topic> getMapper() {
        return topicMapper;
    }
}
