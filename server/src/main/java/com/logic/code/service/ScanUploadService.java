package com.logic.code.service;

import com.logic.code.entity.file.FileAttachment;
import com.logic.code.entity.file.ScanUpload;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * Scan upload service interface
 */
public interface ScanUploadService {
    
    /**
     * Generate QR code for scan upload
     * @param categoryId Category ID
     * @param userId User ID
     * @return QR code info
     */
    Map<String, String> generateQrCode(Integer categoryId, Integer userId);
    
    /**
     * Get scan upload by token
     * @param token Scan token
     * @return Scan upload
     */
    ScanUpload getByToken(String token);
    
    /**
     * Upload file via scan
     * @param file File to upload
     * @param token Scan token
     * @return Uploaded file info
     */
    Map<String, String> uploadViaScan(MultipartFile file, String token);
    
    /**
     * Get uploaded files by token
     * @param token Scan token
     * @return List of uploaded files
     */
    List<FileAttachment> getUploadedFiles(String token);
    
    /**
     * Delete scan upload token
     * @param token Scan token
     * @return True if deleted successfully
     */
    boolean deleteToken(String token);
} 