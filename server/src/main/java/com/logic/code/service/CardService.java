package com.logic.code.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logic.code.common.utils.JwtHelper;
import com.logic.code.entity.Card;
import com.logic.code.entity.goods.Goods;
import com.logic.code.mapper.CardMapper;
import com.logic.code.mapper.CommonMapper;
import com.logic.code.mapper.GoodsMapper;
import com.logic.code.model.query.CardParams;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import logic.orm.WrapperBuilder;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/3 11:46
 * @desc
 */
@Service
public class CardService extends BaseService<Card>{

    @Resource
    private CardMapper cardMapper;

    @Resource
    private GoodsMapper goodsMapper;

    @Override
    protected CommonMapper<Card> getMapper() {
        return cardMapper;
    }


    public Card save(Card card) {
        Card param = new Card();
        param.setNo(card.getNo());
        param.setCode(card.getCode());
        param.setStatus(-1);
        Card one = cardMapper.selectOne(WrapperBuilder.autoWhere(param));
        if (one != null) {
            one.setCreateUser(JwtHelper.getUserInfo().getId());
            one.setStartDate(new Date());
            one.setCreateDate(new Date());
            one.setEndDate(DateUtils.addYears(one.getStartDate(), 2));
            one.setStatus(0);
            cardMapper.updateById(one);
            return one;
        }

        return null;
    }

    public List<Card> list() {
        CardParams param = new CardParams();
        param.setCreateUser(JwtHelper.getUserInfo().getId());
        param.setNoEqStatus(-1);
        return cardMapper.selectList(WrapperBuilder.autoWhere(param));
    }

    public long count() {
        Card param = new Card();
        param.setCreateUser(JwtHelper.getUserInfo().getId());
        return cardMapper.selectCount(WrapperBuilder.autoWhere(param));
    }

    public Card getById(Integer id) {
        Card param = new Card();
        param.setId(id);
        param.setCreateUser(JwtHelper.getUserInfo().getId());
        return cardMapper.selectOne(WrapperBuilder.autoWhere(param));
    }

    /**
     * 分页查询卡券列表
     * @param params 查询参数
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @return 分页结果
     */
    public Page<Card> getPage(CardParams params, Integer pageNum, Integer pageSize) {
        // 设置当前用户ID，确保只查询当前用户的卡券
        /*if (params.getCreateUser() == null) {
            params.setCreateUser(JwtHelper.getUserInfo().getId());
        }*/

        Page<Card> page = new Page<>(pageNum, pageSize);
        Page<Card> result = cardMapper.selectPage(page, WrapperBuilder.autoWhere(params));

        // 为每个卡券添加关联商品名称
        if (result.getRecords() != null && !result.getRecords().isEmpty()) {
            for (Card card : result.getRecords()) {
                if (StringUtils.hasText(card.getGoodsId())) {
                    List<String> goodsIds = Arrays.asList(card.getGoodsId().split(","));
                    List<String> goodsNames = getGoodsNamesByIds(goodsIds);
                    // 将商品名称存储在一个临时字段中，前端可以使用
                    // 由于Card实体没有goodsNames字段，我们需要在Controller层处理
                }
            }
        }

        return result;
    }

    /**
     * 根据商品ID列表获取商品名称列表
     * @param goodsIds 商品ID列表
     * @return 商品名称列表
     */
    public List<String> getGoodsNamesByIds(List<String> goodsIds) {
        if (goodsIds == null || goodsIds.isEmpty()) {
            return new ArrayList<>();
        }

        try {
            List<Integer> ids = goodsIds.stream()
                    .filter(StringUtils::hasText)
                    .map(String::trim)
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());

            if (ids.isEmpty()) {
                return new ArrayList<>();
            }

            List<Goods> goodsList = goodsMapper.selectBatchIds(ids);
            return goodsList.stream()
                    .map(Goods::getName)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            // 如果解析失败，返回空列表
            return new ArrayList<>();
        }
    }

    /**
     * 根据商品ID列表获取商品信息
     * @param goodsIds 商品ID列表
     * @return 商品信息列表
     */
    public List<Map<String, Object>> getGoodsByIds(List<String> goodsIds) {
        if (goodsIds == null || goodsIds.isEmpty()) {
            return new ArrayList<>();
        }

        try {
            List<Integer> ids = goodsIds.stream()
                    .filter(StringUtils::hasText)
                    .map(String::trim)
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());

            if (ids.isEmpty()) {
                return new ArrayList<>();
            }

            List<Goods> goodsList = goodsMapper.selectBatchIds(ids);
            return goodsList.stream()
                    .map(goods -> {
                        Map<String, Object> map = new HashMap<>();
                        map.put("id", goods.getId());
                        map.put("name", goods.getName());
                        map.put("primaryPicUrl", goods.getPrimaryPicUrl());
                        map.put("listPicUrl", goods.getListPicUrl());
                        map.put("retailPrice", goods.getRetailPrice());
                        map.put("categoryId", goods.getCategoryId());
                        map.put("isOnSale", goods.getIsOnSale());
                        return map;
                    })
                    .collect(Collectors.toList());
        } catch (Exception e) {
            // 如果解析失败，返回空列表
            return new ArrayList<>();
        }
    }

    /**
     * 导出卡券数据为CSV格式
     * @param params 查询参数
     * @param response HTTP响应
     */
    public void exportCards(CardParams params, HttpServletResponse response) {
        try {
            // 设置当前用户ID
            /*if (params.getCreateUser() == null) {
                params.setCreateUser(JwtHelper.getUserInfo().getId());
            }*/

            // 查询所有数据（不分页）
            List<Card> cards = cardMapper.selectList(WrapperBuilder.autoWhere(params));

            // 设置响应头
            response.setContentType("text/csv;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=卡券数据_" +
                new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date()) + ".csv");

            // 写入CSV数据
            try (PrintWriter writer = new PrintWriter(new OutputStreamWriter(response.getOutputStream(), StandardCharsets.UTF_8))) {
                // 写入CSV头部
                writer.println("ID,卡券编号,卡券代码,卡券名称,手机号,类型,状态,开始日期,结束日期,创建时间");

                // 写入数据行
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                for (Card card : cards) {
                    writer.printf("%d,%s,%s,%s,%s,%s,%s,%s,%s,%s%n",
                        card.getId(),
                        escapeCsvField(card.getNo()),
                        escapeCsvField(card.getCode()),
                        escapeCsvField(card.getName()),
                        escapeCsvField(card.getPhone()),
                        getCardTypeName(card.getType()),
                        getCardStatusName(card.getStatus()),
                        card.getStartDate() != null ? dateFormat.format(card.getStartDate()) : "",
                        card.getEndDate() != null ? dateFormat.format(card.getEndDate()) : "",
                        card.getCreateDate() != null ? dateFormat.format(card.getCreateDate()) : ""
                    );
                }
            }
        } catch (IOException e) {
            throw new RuntimeException("导出失败", e);
        }
    }

    /**
     * 转义CSV字段
     * @param field 字段值
     * @return 转义后的字段值
     */
    private String escapeCsvField(String field) {
        if (field == null) {
            return "";
        }
        // 如果字段包含逗号、引号或换行符，需要用引号包围并转义内部引号
        if (field.contains(",") || field.contains("\"") || field.contains("\n")) {
            return "\"" + field.replace("\"", "\"\"") + "\"";
        }
        return field;
    }

    /**
     * 获取卡券类型名称
     * @param type 类型值
     * @return 类型名称
     */
    private String getCardTypeName(Integer type) {
        if (type == null) return "未知";
        switch (type) {
            case 0: return "折扣券";
            case 1: return "满减券";
            case 2: return "代金券";
            default: return "未知类型";
        }
    }

    /**
     * 获取卡券状态名称
     * @param status 状态值
     * @return 状态名称
     */
    private String getCardStatusName(Integer status) {
        if (status == null) return "未知";
        switch (status) {
            case -1: return "已作废";
            case 0: return "未使用";
            case 1: return "已使用";
            case 2: return "已过期";
            default: return "未知状态";
        }
    }
}
