package com.logic.code.service;

import com.logic.code.entity.goods.Product;
import com.logic.code.mapper.CommonMapper;
import com.logic.code.mapper.ProductMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/8 11:21
 * @desc
 */
@Service
public class ProductService extends BaseService<Product> {

    @Resource
    private ProductMapper productMapper;

    @Override
    protected CommonMapper<Product> getMapper() {
        return productMapper;
    }

    /**
     * Query products by goods ID
     */
    public List<Product> queryByGoodsId(Integer goodsId) {
        Product query = new Product();
        query.setGoodsId(goodsId);
        return queryList(query);
    }

    /**
     * Delete products by goods ID
     */
    public void deleteByGoodsId(Integer goodsId) {
        Product query = new Product();
        query.setGoodsId(goodsId);
        delete(query);
    }

    /**
     * Create a new product
     */

}
