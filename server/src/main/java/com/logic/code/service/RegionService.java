package com.logic.code.service;

import com.logic.code.entity.Region;
import com.logic.code.mapper.CommonMapper;
import com.logic.code.mapper.RegionMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/5/7 22:04
 * @desc
 */
@Service
public class RegionService extends BaseService<Region>{

    @Resource
    private RegionMapper regionMapper;

    @Override
    protected CommonMapper<Region> getMapper() {
        return regionMapper;
    }

    public String queryNameById(Short id){
        return regionMapper.selectNameById(id);
    }

}
