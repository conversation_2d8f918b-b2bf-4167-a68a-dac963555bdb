package com.logic.code.service;

import com.logic.code.entity.goods.GoodsGallery;
import com.logic.code.mapper.CommonMapper;
import com.logic.code.mapper.GoodsGalleryMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/8 11:30
 * @desc
 */
@Service
public class GoodsGalleryService extends BaseService<GoodsGallery> {

    @Resource
    private GoodsGalleryMapper goodsGalleryMapper;

    @Override
    protected CommonMapper<GoodsGallery> getMapper() {
        return goodsGalleryMapper;
    }

    /**
     * Query galleries by goods ID
     */
    public List<GoodsGallery> queryByGoodsId(Integer goodsId) {
        GoodsGallery query = new GoodsGallery();
        query.setGoodsId(goodsId);
        return queryList(query);
    }

    /**
     * Delete galleries by goods ID
     */
    public void deleteByGoodsId(Integer goodsId) {
        GoodsGallery query = new GoodsGallery();
        query.setGoodsId(goodsId);
        delete(query);
    }

}
