package com.logic.code.service;


import java.io.Serializable;
import java.util.List;

/**
 * 通用接口
 *
 * @param <T>
 * <AUTHOR>
 */
public interface IService<T> {

    List<T> queryAll();

    List<T> queryList(T entity);

    T queryById(Serializable id);

    List<T> queryByCriteria(Criteria<T, Object> criteria);

    T queryOneByCriteria(Criteria<T, Object> criteria);

    int countByCriteria(Criteria<T, Object> criteria);

    T queryOne(T entity);


    int create(T entity);

    int createBatch(List<T> list);

    int updateAll(T entity);

    int updateNotNull(T entity);

    int delete(T entity);


    int deleteById(Serializable id);

    int count(T entity);

}
