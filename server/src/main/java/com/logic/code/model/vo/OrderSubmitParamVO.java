package com.logic.code.model.vo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class OrderSubmitParamVO {

    @NotNull
    private Integer addressId;

    private Integer couponId;

    private String postscript;

    private Integer goodsId;

    private Integer productId;

    private Integer type;
    private Integer number;
    
    /**
     * 使用的积分数量
     */
    private Integer usePoints;
    
    /**
     * 使用的余额金额
     */
    private java.math.BigDecimal useBalance;
    
    /**
     * 推广者ID（从商品页面传递）
     */
    private Integer promoterId;


    public Integer getAddressId() {
        return addressId;
    }

    public OrderSubmitParamVO setAddressId(Integer addressId) {
        this.addressId = addressId;
        return this;
    }

    public Integer getCouponId() {
        return couponId;
    }

    public OrderSubmitParamVO setCouponId(Integer couponId) {
        this.couponId = couponId;
        return this;
    }

    public String getPostscript() {
        return postscript;
    }

    public OrderSubmitParamVO setPostscript(String postscript) {
        this.postscript = postscript;
        return this;
    }

    public Integer getGoodsId() {
        return goodsId;
    }

    public OrderSubmitParamVO setGoodsId(Integer goodsId) {
        this.goodsId = goodsId;
        return this;
    }

    public Integer getProductId() {
        return productId;
    }

    public OrderSubmitParamVO setProductId(Integer productId) {
        this.productId = productId;
        return this;
    }

    public Integer getType() {
        return type;
    }

    public OrderSubmitParamVO setType(Integer type) {
        this.type = type;
        return this;
    }
    
    public Integer getUsePoints() {
        return usePoints;
    }
    
    public OrderSubmitParamVO setUsePoints(Integer usePoints) {
        this.usePoints = usePoints;
        return this;
    }
    
    public java.math.BigDecimal getUseBalance() {
        return useBalance;
    }
    
    public OrderSubmitParamVO setUseBalance(java.math.BigDecimal useBalance) {
        this.useBalance = useBalance;
        return this;
    }
    
    public Integer getPromoterId() {
        return promoterId;
    }
    
    public OrderSubmitParamVO setPromoterId(Integer promoterId) {
        this.promoterId = promoterId;
        return this;
    }
}
