package com.logic.code.model.vo;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 推广积分统计VO
 */
@Data
@Accessors(chain = true)
public class PromotionPointsStatsVO {
    
    /**
     * 总积分
     */
    private Integer totalPoints;
    
    /**
     * 今日积分
     */
    private Integer todayPoints;
    
    /**
     * 本月积分
     */
    private Integer monthPoints;
    
    /**
     * 总推广人数
     */
    private Integer totalPromotionCount;
    
    /**
     * 今日推广人数
     */
    private Integer todayPromotionCount;
    
    /**
     * 本月推广人数
     */
    private Integer monthPromotionCount;
    
    /**
     * 当前等级
     */
    private Integer currentLevel;
    
    /**
     * 当前等级名称
     */
    private String currentLevelName;
    
    /**
     * 下一等级所需推广人数
     */
    private Integer nextLevelRequiredCount;
}