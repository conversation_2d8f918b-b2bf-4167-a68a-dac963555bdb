package com.logic.code.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 商品限购信息VO
 * <AUTHOR>
 * @date 2025/8/17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseLimitVO {
    
    /**
     * 是否为限购商品
     */
    private Boolean isLimited;
    
    /**
     * 限购数量
     */
    private Integer limitNum;
    
    /**
     * 用户已购买数量
     */
    private Integer purchasedCount;
    
    /**
     * 剩余可购买数量
     */
    private Integer remainingCount;
    
    /**
     * 是否可以购买
     */
    private Boolean canPurchase;
    
    /**
     * 限购提示信息
     */
    private String message;
}