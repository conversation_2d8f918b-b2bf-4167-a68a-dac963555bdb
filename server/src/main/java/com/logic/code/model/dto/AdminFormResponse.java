package com.logic.code.model.dto;

import java.util.List;
import java.util.Map;

/**
 * 管理员表单响应DTO类
 * 
 * <AUTHOR>
 * @date 2025/8/25
 */
public class AdminFormResponse {
    
    // 编辑时的管理员信息
    private AdminResponse admin;
    
    // 角色选项列表
    private List<Map<String, Object>> roles;
    
    // 状态选项列表
    private List<Map<String, Object>> statusOptions;
    
    public AdminResponse getAdmin() {
        return admin;
    }
    
    public AdminFormResponse setAdmin(AdminResponse admin) {
        this.admin = admin;
        return this;
    }
    
    public List<Map<String, Object>> getRoles() {
        return roles;
    }
    
    public AdminFormResponse setRoles(List<Map<String, Object>> roles) {
        this.roles = roles;
        return this;
    }
    
    public List<Map<String, Object>> getStatusOptions() {
        return statusOptions;
    }
    
    public AdminFormResponse setStatusOptions(List<Map<String, Object>> statusOptions) {
        this.statusOptions = statusOptions;
        return this;
    }
}