package com.logic.code.model.vo;

import com.logic.code.common.utils.JwtHelper;
import com.logic.code.entity.Comment;
import com.logic.code.entity.User;
import jakarta.validation.constraints.NotNull;

import java.util.Base64;
import java.util.List;

public class CommentPostVO {

    @NotNull
    private Integer typeId;

    @NotNull
    private Integer valueId;

    /**
     * 储存为base64编码
     */
    @NotNull
    private String content;

    private Integer userId;

    /**
     * 图片URL列表
     */
    private List<String> picList;

    /**
     * 星级评分(1-5星)
     */
    private Integer rating;

    public Comment toPO() {
        User user = JwtHelper.getUserInfo();
        Comment comment = new Comment();
        comment.setTypeId(typeId);
        comment.setValueId(valueId);
        comment.setContent(Base64.getEncoder().encodeToString(content.getBytes()));
        comment.setUserId(user.getId());
        comment.setRating(rating != null ? rating : (byte) 5); // 默认5星
        return comment;
    }

    public Integer getTypeId() {
        return typeId;
    }

    public CommentPostVO setTypeId(Integer typeId) {
        this.typeId = typeId;
        return this;
    }

    public Integer getValueId() {
        return valueId;
    }

    public CommentPostVO setValueId(Integer valueId) {
        this.valueId = valueId;
        return this;
    }

    public String getContent() {
        return content;
    }

    public CommentPostVO setContent(String content) {
        this.content = content;
        return this;
    }

    public Integer getUserId() {
        return userId;
    }

    public CommentPostVO setUserId(Integer userId) {
        this.userId = userId;
        return this;
    }

    public List<String> getPicList() {
        return picList;
    }

    public CommentPostVO setPicList(List<String> picList) {
        this.picList = picList;
        return this;
    }

    public Integer getRating() {
        return rating;
    }

    public CommentPostVO setRating(Integer rating) {
        this.rating = rating;
        return this;
    }

}
