package com.logic.code.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 快递公司VO
 * <AUTHOR>
 * @date 2025/1/27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExpressCompanyVO {
    
    /**
     * 快递公司名称（用于显示）
     */
    private String value;
    
    /**
     * 快递公司编码
     */
    private String code;
    
    /**
     * 快递公司ID
     */
    private Integer id;
    
    /**
     * 排序
     */
    private Integer sort;
}