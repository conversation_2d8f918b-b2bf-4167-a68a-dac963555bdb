package com.logic.code.model.dto;

import lombok.Data;

import java.util.List;

/**
 * 商品Excel导入结果DTO
 * <AUTHOR>
 * @date 2025/7/16
 */
@Data
public class GoodsImportResultDTO {
    
    /**
     * 总行数
     */
    private Integer totalRows;
    
    /**
     * 成功导入数量
     */
    private Integer successCount;
    
    /**
     * 失败数量
     */
    private Integer failureCount;
    
    /**
     * 跳过数量（空行等）
     */
    private Integer skipCount;
    
    /**
     * 是否全部成功
     */
    private Boolean allSuccess;
    
    /**
     * 导入耗时（毫秒）
     */
    private Long duration;
    
    /**
     * 成功导入的商品列表
     */
    private List<GoodsImportDTO> successList;
    
    /**
     * 失败的商品列表（包含错误信息）
     */
    private List<GoodsImportDTO> failureList;
    
    /**
     * 跳过的行号列表
     */
    private List<Integer> skipRows;
    
    /**
     * 总体错误信息
     */
    private String errorMessage;
    
    /**
     * 详细错误信息列表
     */
    private List<String> detailErrors;
    
    /**
     * 创建成功结果
     */
    public static GoodsImportResultDTO success(int totalRows, int successCount, List<GoodsImportDTO> successList) {
        GoodsImportResultDTO result = new GoodsImportResultDTO();
        result.setTotalRows(totalRows);
        result.setSuccessCount(successCount);
        result.setFailureCount(0);
        result.setSkipCount(totalRows - successCount);
        result.setAllSuccess(true);
        result.setSuccessList(successList);
        return result;
    }
    
    /**
     * 创建部分成功结果
     */
    public static GoodsImportResultDTO partialSuccess(int totalRows, int successCount, int failureCount, 
                                                     List<GoodsImportDTO> successList, List<GoodsImportDTO> failureList) {
        GoodsImportResultDTO result = new GoodsImportResultDTO();
        result.setTotalRows(totalRows);
        result.setSuccessCount(successCount);
        result.setFailureCount(failureCount);
        result.setSkipCount(totalRows - successCount - failureCount);
        result.setAllSuccess(false);
        result.setSuccessList(successList);
        result.setFailureList(failureList);
        return result;
    }
    
    /**
     * 创建失败结果
     */
    public static GoodsImportResultDTO failure(String errorMessage) {
        GoodsImportResultDTO result = new GoodsImportResultDTO();
        result.setTotalRows(0);
        result.setSuccessCount(0);
        result.setFailureCount(0);
        result.setSkipCount(0);
        result.setAllSuccess(false);
        result.setErrorMessage(errorMessage);
        return result;
    }
}
