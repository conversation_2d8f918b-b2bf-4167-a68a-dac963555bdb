package com.logic.code.model.dto;

/**
 * 管理员请求DTO类
 * 
 * <AUTHOR>
 * @date 2025/8/25
 */
public class AdminRequest {
    
    private Integer id;
    
    private String username;
    
    private String password;
    
    private String realName;
    
    private String avatar;
    
    private Integer adminRoleId;
    
    private Integer status; // 1:启用 0:禁用
    
    private String roles; // 角色名称
    
    // 分页参数
    private Integer page = 1;
    
    private Integer limit = 20;
    
    // 查询参数
    private String name; // 搜索关键字（姓名或账号）
    
    public Integer getId() {
        return id;
    }
    
    public AdminRequest setId(Integer id) {
        this.id = id;
        return this;
    }
    
    public String getUsername() {
        return username;
    }
    
    public AdminRequest setUsername(String username) {
        this.username = username;
        return this;
    }
    
    public String getPassword() {
        return password;
    }
    
    public AdminRequest setPassword(String password) {
        this.password = password;
        return this;
    }
    
    public String getRealName() {
        return realName;
    }
    
    public AdminRequest setRealName(String realName) {
        this.realName = realName;
        return this;
    }
    
    public String getAvatar() {
        return avatar;
    }
    
    public AdminRequest setAvatar(String avatar) {
        this.avatar = avatar;
        return this;
    }
    
    public Integer getAdminRoleId() {
        return adminRoleId;
    }
    
    public AdminRequest setAdminRoleId(Integer adminRoleId) {
        this.adminRoleId = adminRoleId;
        return this;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public AdminRequest setStatus(Integer status) {
        this.status = status;
        return this;
    }
    
    public String getRoles() {
        return roles;
    }
    
    public AdminRequest setRoles(String roles) {
        this.roles = roles;
        return this;
    }
    
    public Integer getPage() {
        return page;
    }
    
    public AdminRequest setPage(Integer page) {
        this.page = page;
        return this;
    }
    
    public Integer getLimit() {
        return limit;
    }
    
    public AdminRequest setLimit(Integer limit) {
        this.limit = limit;
        return this;
    }
    
    public String getName() {
        return name;
    }
    
    public AdminRequest setName(String name) {
        this.name = name;
        return this;
    }
}