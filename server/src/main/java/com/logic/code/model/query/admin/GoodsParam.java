package com.logic.code.model.query.admin;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/6/10 20:49
 * @desc
 */
@Data
public class GoodsParam {

    private Integer page;
    private Integer limit;
    private String name;
    private Integer categoryId;
    private Boolean isOnSale;

    /**
     * 商品状态类型
     * 1: 全部, 2: 出售中, 3: 仓库中, 4: 回收站
     */
    private Integer type;

    /**
     * 商品名称搜索（兼容前端字段名）
     */
    private String storeName;

    public String getStoreName() {
        return storeName != null ? storeName : name;
    }

}
