package com.logic.code.model.query;

import com.baomidou.mybatisplus.core.enums.SqlKeyword;
import logic.orm.annotation.WhereCondition;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/20 12:05
 * @desc 卡券查询参数
 */
@Data
public class CardParams {
    /**
     * 卡券编号
     */
    @WhereCondition(value = SqlKeyword.LIKE)
    private String no;
    
    /**
     * 卡券代码
     */
    @WhereCondition(value = SqlKeyword.LIKE)
    private String code;
    
    /**
     * 创建用户ID
     */
    private Integer createUser;
    
    /**
     * 卡券状态
     */
    private Integer status;
    
    /**
     * 不等于状态
     */
    @WhereCondition(value = SqlKeyword.NE, name = "status")
    private Integer noEqStatus;
    
    /**
     * 卡券类型
     */
    private Integer type;
    
    /**
     * 卡券名称
     */
    @WhereCondition(value = SqlKeyword.LIKE)
    private String name;
    
    /**
     * 手机号
     */
    @WhereCondition(value = SqlKeyword.LIKE)
    private String phone;
    
    /**
     * 开始日期-起始
     */
    @WhereCondition(value = SqlKeyword.GE, name = "startDate")
    private Date startDateBegin;
    
    /**
     * 开始日期-结束
     */
    @WhereCondition(value = SqlKeyword.LE, name = "startDate")
    private Date startDateEnd;
    
    /**
     * 结束日期-起始
     */
    @WhereCondition(value = SqlKeyword.GE, name = "endDate")
    private Date endDateBegin;
    
    /**
     * 结束日期-结束
     */
    @WhereCondition(value = SqlKeyword.LE, name = "endDate")
    private Date endDateEnd;
}
