package com.logic.code.model.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 商品Excel导入DTO
 * <AUTHOR>
 * @date 2025/7/16
 */
@Data
public class GoodsImportDTO {
    
    /**
     * 商品名称（必填）
     */
    private String name;
    
    /**
     * 商品分类ID（必填）
     */
    private Integer categoryId;
    
    /**
     * 商品分类名称（用于显示和验证）
     */
    private String categoryName;
    
    /**
     * 商品简介
     */
    private String brief;
    
    /**
     * 商品单位
     */
    private String unit;
    
    /**
     * 关键词
     */
    private String keywords;
    
    /**
     * 商品描述
     */
    private String description;
    
    /**
     * 是否上架（1-上架，0-下架）
     */
    private Integer isOnSale;
    
    /**
     * 是否热门（1-是，0-否）
     */
    private Integer isHot;
    
    /**
     * 是否新品（1-是，0-否）
     */
    private Integer isNew;
    
    /**
     * 排序值
     */
    private Integer sortOrder;
    
    /**
     * 虚拟销量
     */
    private Integer virtualSales;
    
    /**
     * 商品主图URL
     */
    private String imageUrl;
    
    /**
     * 商品轮播图URL（多个用逗号分隔）
     */
    private String sliderImages;
    
    /**
     * 单价
     */
    private BigDecimal unitPrice;
    
    /**
     * 详情标签（多个用逗号分隔）
     */
    private String detailTag;
    
    /**
     * 展示系统ID（多个用逗号分隔）
     */
    private String displaySystems;
    
    // === 单规格商品字段 ===
    
    /**
     * 规格类型（0-单规格，1-多规格）
     */
    private Integer specType;
    
    /**
     * 商品价格（单规格）
     */
    private BigDecimal price;
    
    /**
     * 成本价（单规格）
     */
    private BigDecimal costPrice;
    
    /**
     * 划线价（单规格）
     */
    private BigDecimal otPrice;
    
    /**
     * 库存数量（单规格）
     */
    private Integer stock;
    
    /**
     * 商品编码（单规格）
     */
    private String barCode;
    
    /**
     * 商品重量KG（单规格）
     */
    private BigDecimal weight;
    
    /**
     * 商品体积m³（单规格）
     */
    private BigDecimal volume;
    
    /**
     * 规格图片URL（单规格）
     */
    private String specImageUrl;
    
    /**
     * 一级分销佣金（单规格）
     */
    private BigDecimal brokerage;
    
    /**
     * 二级分销佣金（单规格）
     */
    private BigDecimal brokerageTwo;
    
    /**
     * 限购数量（单规格）
     */
    private Integer quota;
    
    /**
     * 是否显示限购（单规格）
     */
    private Integer quotaShow;
    
    // === 多规格商品字段 ===

    /**
     * 规格定义（JSON格式）
     * 格式：[{"value":"颜色","detail":[{"value":"红色","pic":""},{"value":"蓝色","pic":""}]},{"value":"尺寸","detail":[{"value":"S","pic":""},{"value":"M","pic":""}]}]
     */
    private String specItems;

    /**
     * SKU列表（JSON格式）
     * 格式：[{"detail":{"颜色":"红色","尺寸":"S"},"price":100,"cost":80,"stock":50,"bar_code":"SKU001",...}]
     */
    private String skuAttrs;

    // === 商品属性字段 ===

    /**
     * 商品属性（JSON格式或键值对格式）
     */
    private String attributes;

    // === 验证和处理相关字段 ===
    
    /**
     * 行号（用于错误提示）
     */
    private Integer rowNumber;
    
    /**
     * 验证错误信息
     */
    private String errorMessage;
    
    /**
     * 是否验证通过
     */
    private Boolean isValid;
    
    /**
     * 处理状态（0-待处理，1-成功，2-失败）
     */
    private Integer processStatus;
}
