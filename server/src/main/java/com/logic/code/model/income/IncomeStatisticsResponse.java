package com.logic.code.model.income;

import lombok.Data;

import java.math.BigDecimal;

/**
 * Income statistics response
 */
@Data
public class IncomeStatisticsResponse {
    
    private Integer userId;
    private String nickname;
    private String avatar;
    
    // Main statistics
    private BigDecimal totalIncome = BigDecimal.ZERO;
    private BigDecimal confirmedAmount = BigDecimal.ZERO;
    private BigDecimal pendingAmount = BigDecimal.ZERO;
    private BigDecimal settledAmount = BigDecimal.ZERO;
    private BigDecimal frozenAmount = BigDecimal.ZERO;
    
    // Statistics by income type
    private BigDecimal commissionAmount = BigDecimal.ZERO;
    private BigDecimal tieredPromotionAmount = BigDecimal.ZERO;
    private BigDecimal directorAmount = BigDecimal.ZERO;
    private BigDecimal teamAmount = BigDecimal.ZERO;
    private BigDecimal otherAmount = BigDecimal.ZERO;
    
    // Time-based statistics
    private BigDecimal monthlyIncome = BigDecimal.ZERO;
    private BigDecimal dailyIncome = BigDecimal.ZERO;
    
    // Additional info
    private Integer totalRecords = 0;
    private String lastUpdateTime;
}
