package com.logic.code.model.vo;

import com.logic.code.entity.goods.Goods;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 带活动信息的商品VO
 */
@Data
public class GoodsWithActivityVO {
    
    private Integer id;
    private String name;
    private String listPicUrl;
    private BigDecimal retailPrice;
    private BigDecimal unitPrice;
    private String goodsBrief;
    private BigDecimal counterPrice;
    private Integer sellVolume;
    private Boolean isHot;
    private Boolean isNew;
    private Integer categoryId;
    
    // 活动相关字段
    private String activityName;        // 活动名称
    private Boolean hasActivity;        // 是否有活动
    private String activityType;        // 活动类型
    
    public GoodsWithActivityVO() {
    }
    
    public GoodsWithActivityVO(Goods goods) {
        this.id = goods.getId();
        this.name = goods.getName();
        this.listPicUrl = goods.getListPicUrl();
        this.retailPrice = goods.getRetailPrice();
        this.unitPrice = goods.getUnitPrice();
        this.goodsBrief = goods.getGoodsBrief();
        this.counterPrice = goods.getCounterPrice();
        this.sellVolume = goods.getSellVolume();
        this.isHot = goods.getIsHot();
        this.isNew = goods.getIsNewly();
        this.categoryId = goods.getCategoryId();
        
        // 默认无活动
        this.hasActivity = false;
        this.activityName = null;
        this.activityType = null;
    }
    
    /**
     * 设置活动信息
     */
    public void setActivityInfo(String activityName, String activityType) {
        this.activityName = activityName;
        this.activityType = activityType;
        this.hasActivity = activityName != null && !activityName.trim().isEmpty();
    }
}