package com.logic.code.model.dto;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 微信发货信息录入DTO
 */
@Data
public class WxShippingInfoDTO {

    /**
     * 订单信息
     */
    @NotNull(message = "订单信息不能为空")
    private OrderKey orderKey;

    /**
     * 物流模式：1-实体物流配送，2-同城配送，3-虚拟商品，4-用户自提
     */
    @NotNull(message = "物流模式不能为空")
    private Integer logisticsType;

    /**
     * 发货模式：1-统一发货，2-分拆发货
     */
    @NotNull(message = "发货模式不能为空")
    private Integer deliveryMode;

    /**
     * 是否全部发货完成（分拆发货时必填）
     */
    private Boolean isAllDelivered;

    /**
     * 物流信息列表
     */
    @NotNull(message = "物流信息不能为空")
    private List<ShippingItem> shippingList;

    /**
     * 支付者信息
     */
    @NotNull(message = "支付者信息不能为空")
    private Payer payer;

    @Data
    public static class OrderKey {
        /**
         * 订单单号类型：1-商户侧单号，2-微信支付单号
         */
        @NotNull(message = "订单单号类型不能为空")
        private Integer orderNumberType;

        /**
         * 微信支付单号
         */
        private String transactionId;

        /**
         * 商户号
         */
        private String mchid;

        /**
         * 商户订单号
         */
        private String outTradeNo;
    }

    @Data
    public static class ShippingItem {
        /**
         * 物流单号
         */
        private String trackingNo;

        /**
         * 物流公司编码
         */
        private String expressCompany;

        /**
         * 商品信息描述
         */
        @NotNull(message = "商品信息不能为空")
        private String itemDesc;

        /**
         * 联系方式
         */
        private Contact contact;
    }

    @Data
    public static class Contact {
        /**
         * 寄件人联系方式
         */
        private String consignorContact;

        /**
         * 收件人联系方式
         */
        private String receiverContact;
    }

    @Data
    public static class Payer {
        /**
         * 用户openid
         */
        @NotNull(message = "用户openid不能为空")
        private String openid;
    }
}
