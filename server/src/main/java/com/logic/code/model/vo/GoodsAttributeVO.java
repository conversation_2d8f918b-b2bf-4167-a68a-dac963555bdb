package com.logic.code.model.vo;

import lombok.Data;

/**
 * 商品属性VO类
 */
@Data
public class GoodsAttributeVO {
    /**
     * 属性ID（编码）
     */
    private Integer attributeId;

    /**
     * 属性名称
     */
    private String attributeName;

    /**
     * 属性值
     */
    private String attributeValue;

    public GoodsAttributeVO() {}

    public GoodsAttributeVO(String attributeName, String attributeValue) {
        this.attributeName = attributeName;
        this.attributeValue = attributeValue;
    }

    public GoodsAttributeVO(Integer attributeId, String attributeName, String attributeValue) {
        this.attributeId = attributeId;
        this.attributeName = attributeName;
        this.attributeValue = attributeValue;
    }
}
