package com.logic.code.model.dto;

/**
 * 管理员响应DTO类
 * 
 * <AUTHOR>
 * @date 2025/8/25
 */
public class AdminResponse {
    
    private Integer id;
    
    private String account; // 对应username字段
    
    private String realName;
    
    private String avatar;
    
    private Integer adminRoleId;
    
    private Integer status; // 1:启用 0:禁用
    
    private String roles; // 角色名称
    
    private String lastIp; // 最后登录IP
    
    private String lastTime; // 最后登录时间（格式化后的字符串）
    
    private String _last_time; // 兼容前端的时间字段名
    
    public Integer getId() {
        return id;
    }
    
    public AdminResponse setId(Integer id) {
        this.id = id;
        return this;
    }
    
    public String getAccount() {
        return account;
    }
    
    public AdminResponse setAccount(String account) {
        this.account = account;
        return this;
    }
    
    public String getRealName() {
        return realName;
    }
    
    public AdminResponse setRealName(String realName) {
        this.realName = realName;
        return this;
    }
    
    public String getAvatar() {
        return avatar;
    }
    
    public AdminResponse setAvatar(String avatar) {
        this.avatar = avatar;
        return this;
    }
    
    public Integer getAdminRoleId() {
        return adminRoleId;
    }
    
    public AdminResponse setAdminRoleId(Integer adminRoleId) {
        this.adminRoleId = adminRoleId;
        return this;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public AdminResponse setStatus(Integer status) {
        this.status = status;
        return this;
    }
    
    public String getRoles() {
        return roles;
    }
    
    public AdminResponse setRoles(String roles) {
        this.roles = roles;
        return this;
    }
    
    public String getLastIp() {
        return lastIp;
    }
    
    public AdminResponse setLastIp(String lastIp) {
        this.lastIp = lastIp;
        return this;
    }
    
    public String getLastTime() {
        return lastTime;
    }
    
    public AdminResponse setLastTime(String lastTime) {
        this.lastTime = lastTime;
        return this;
    }
    
    public String get_last_time() {
        return _last_time;
    }
    
    public AdminResponse set_last_time(String _last_time) {
        this._last_time = _last_time;
        return this;
    }
}