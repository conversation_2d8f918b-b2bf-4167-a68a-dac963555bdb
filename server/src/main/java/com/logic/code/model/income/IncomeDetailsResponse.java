package com.logic.code.model.income;

import com.logic.code.common.CommonPage;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * Income details response
 */
@Data
public class IncomeDetailsResponse {
    
    private List<IncomeDetailItem> list;
    private CommonPage pageInfo;
    private IncomeDetailsSummary summary;
    
    @Data
    public static class IncomeDetailItem {
        private Long id;
        private Integer userId;
        private Integer incomeType;
        private String incomeTypeName;
        private BigDecimal amount;
        private String orderNo;
        private String productName;
        private String productImage;
        private Integer sourceUserId;
        private String sourceUserNickname;
        private Integer status;
        private String statusName;
        private String settleTime;
        private String createTime;
        private String remark;
        
        // Additional fields for tiered promotion
        private String tierLevel;
        private Integer promotionOrderCount;
    }
    
    @Data
    public static class IncomeDetailsSummary {
        private BigDecimal totalAmount = BigDecimal.ZERO;
        private BigDecimal confirmedAmount = BigDecimal.ZERO;
        private BigDecimal pendingAmount = BigDecimal.ZERO;
        private BigDecimal settledAmount = BigDecimal.ZERO;
        private BigDecimal frozenAmount = BigDecimal.ZERO;
        private Integer totalCount = 0;
    }
}
