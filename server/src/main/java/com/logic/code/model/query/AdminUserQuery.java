package com.logic.code.model.query;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * 管理员用户查询参数
 * <AUTHOR>
 * @date 2025/1/27
 */
@Data
public class AdminUserQuery {
    
    private Integer pageNum = 1;
    private Integer pageSize = 10;
    private String type; // 用户类型筛选：new-新用户, active-活跃用户, vip-VIP用户, promoter-推广用户
    private String keyword; // 搜索关键词（昵称、手机号）
    private Integer userLevelId; // 用户等级筛选

}