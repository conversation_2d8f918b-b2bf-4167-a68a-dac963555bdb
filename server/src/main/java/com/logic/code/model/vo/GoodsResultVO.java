package com.logic.code.model.vo;


import com.logic.code.entity.goods.Category;
import com.logic.code.entity.goods.Goods;
import lombok.Data;

import java.util.List;

@Data

public class GoodsResultVO {

    private List<GoodsWithActivityVO> goodsList;

    List<CategoryFilterVO> filterCategory;

    List<Category> categoryList;

    public static final GoodsResultVO EMPTY_GOODS_RESULT = new GoodsResultVO();

    public GoodsResultVO() {
    }

    public GoodsResultVO(List<GoodsWithActivityVO> goodsList, List<CategoryFilterVO> filterCategory) {
        this.goodsList = goodsList;
        this.filterCategory = filterCategory;
    }

    public List<GoodsWithActivityVO> getGoodsList() {
        return goodsList;
    }

    public GoodsResultVO setGoodsList(List<GoodsWithActivityVO> goodsList) {
        this.goodsList = goodsList;
        return this;
    }

    public List<CategoryFilterVO> getFilterCategory() {
        return filterCategory;
    }

    public GoodsResultVO setFilterCategory(List<CategoryFilterVO> filterCategory) {
        this.filterCategory = filterCategory;
        return this;
    }
}
