package com.logic.code.model.vo;

/**
 * <AUTHOR>
 * @date 2025/7/5
 * @desc 属性与属性分类联合查询VO
 */
public class AttributeWithCategoryVO {
    
    private Integer id;
    private Integer attributeCategoryId;
    private String name;
    private Boolean inputType;
    private Byte sortOrder;
    private String values;
    
    // 属性分类信息
    private String categoryName;
    private Boolean categoryEnabled;

    public Integer getId() {
        return id;
    }

    public AttributeWithCategoryVO setId(Integer id) {
        this.id = id;
        return this;
    }

    public Integer getAttributeCategoryId() {
        return attributeCategoryId;
    }

    public AttributeWithCategoryVO setAttributeCategoryId(Integer attributeCategoryId) {
        this.attributeCategoryId = attributeCategoryId;
        return this;
    }

    public String getName() {
        return name;
    }

    public AttributeWithCategoryVO setName(String name) {
        this.name = name;
        return this;
    }

    public Boolean getInputType() {
        return inputType;
    }

    public AttributeWithCategoryVO setInputType(Boolean inputType) {
        this.inputType = inputType;
        return this;
    }

    public Byte getSortOrder() {
        return sortOrder;
    }

    public AttributeWithCategoryVO setSortOrder(Byte sortOrder) {
        this.sortOrder = sortOrder;
        return this;
    }

    public String getValues() {
        return values;
    }

    public AttributeWithCategoryVO setValues(String values) {
        this.values = values;
        return this;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public AttributeWithCategoryVO setCategoryName(String categoryName) {
        this.categoryName = categoryName;
        return this;
    }

    public Boolean getCategoryEnabled() {
        return categoryEnabled;
    }

    public AttributeWithCategoryVO setCategoryEnabled(Boolean categoryEnabled) {
        this.categoryEnabled = categoryEnabled;
        return this;
    }
}
