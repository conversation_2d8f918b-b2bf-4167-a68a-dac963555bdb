package com.logic.code.model.query;

import lombok.Data;

import java.util.List;

/**
 * 管理员订单查询参数
 * <AUTHOR>
 * @date 2025/1/27
 */
@Data
public class AdminOrderQuery {

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 页大小
     */
    private Integer pageSize = 10;

    /**
     * 订单状态
     */
    private String orderStatus;

    /**
     * 搜索关键词（订单号、用户昵称、商品名称）
     */
    private String keyword;

    /**
     * 排序字段（createTime-创建时间）
     */
    private String sortField = "createTime";

    /**
     * 排序方式（desc-倒序/最新优先, asc-正序/最早优先）
     */
    private String sortOrder = "desc";

    /**
     * 分页结果
     */
    @Data
    public static class PageResult {
        private List<?> list;
        private Integer pageNum;
        private Integer pageSize;
        private Integer total;
        private Integer pages;
    }
}