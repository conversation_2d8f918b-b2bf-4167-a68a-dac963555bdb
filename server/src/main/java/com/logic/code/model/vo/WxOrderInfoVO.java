package com.logic.code.model.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 微信订单信息响应VO
 */
@Data
public class WxOrderInfoVO {

    /**
     * 微信支付单号
     */
    private String transactionId;

    /**
     * 商户号
     */
    private String merchantId;

    /**
     * 二级商户号
     */
    private String subMerchantId;

    /**
     * 商户订单号
     */
    private String merchantTradeNo;

    /**
     * 商品描述
     */
    private String description;

    /**
     * 支付金额（分）
     */
    private Integer paidAmount;

    /**
     * 支付者openid
     */
    private String openid;

    /**
     * 交易创建时间
     */
    private Long tradeCreateTime;

    /**
     * 支付时间
     */
    private Long payTime;

    /**
     * 订单状态：1-待发货，2-已发货，3-确认收货，4-交易完成，5-已退款，6-资金待结算
     */
    private Integer orderState;

    /**
     * 是否处在交易纠纷中
     */
    private Boolean inComplaint;

    /**
     * 发货信息
     */
    private ShippingInfo shipping;

    @Data
    public static class ShippingInfo {
        /**
         * 发货模式：1-统一发货，2-分拆发货
         */
        private Integer deliveryMode;

        /**
         * 物流模式：1-实体物流配送，2-同城配送，3-虚拟商品，4-用户自提
         */
        private Integer logisticsType;

        /**
         * 是否已完成全部发货
         */
        private Boolean finishShipping;

        /**
         * 商品描述
         */
        private String goodsDesc;

        /**
         * 已完成全部发货的次数
         */
        private Integer finishShippingCount;

        /**
         * 物流信息列表
         */
        private List<ShippingItem> shippingList;
    }

    @Data
    public static class ShippingItem {
        /**
         * 物流单号
         */
        private String trackingNo;

        /**
         * 物流公司编码
         */
        private String expressCompany;

        /**
         * 商品描述
         */
        private String goodsDesc;

        /**
         * 上传时间
         */
        private Long uploadTime;

        /**
         * 联系方式
         */
        private Contact contact;
    }

    @Data
    public static class Contact {
        /**
         * 寄件人联系方式
         */
        private String consignorContact;

        /**
         * 收件人联系方式
         */
        private String receiverContact;
    }
}
