package com.logic.code.model.vo;

import com.logic.code.entity.order.OrderGoods;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 积分抵扣订单列表VO
 * 用于前端展示使用了积分抵扣的订单信息
 */
@Data
public class PointsOrderListVO {
    
    /**
     * 订单ID
     */
    private Integer id;
    
    /**
     * 订单号
     */
    private String orderSn;
    
    /**
     * 用户ID
     */
    private Integer userId;
    
    /**
     * 用户昵称
     */
    private String userNickname;
    
    /**
     * 用户手机号
     */
    private String userMobile;
    
    /**
     * 用户头像
     */
    private String userAvatar;
    
    /**
     * 订单状态值
     */
    private Integer orderStatus;
    
    /**
     * 订单状态文本
     */
    private String orderStatusText;
    
    /**
     * 支付状态值
     */
    private Integer payStatus;
    
    /**
     * 支付状态文本
     */
    private String payStatusText;
    
    /**
     * 订单总金额
     */
    private BigDecimal orderPrice;
    
    /**
     * 实际支付金额
     */
    private BigDecimal actualPrice;
    
    /**
     * 商品总金额
     */
    private BigDecimal goodsPrice;
    
    /**
     * 运费
     */
    private BigDecimal freightPrice;
    
    /**
     * 优惠券抵扣金额
     */
    private BigDecimal couponPrice;
    
    /**
     * 使用的积分数量
     */
    private Integer integral;
    
    /**
     * 积分抵扣金额
     */
    private BigDecimal integralMoney;
    
    /**
     * 余额抵扣金额
     */
    private BigDecimal balancePrice;
    
    /**
     * 订单创建时间
     */
    private Date createTime;
    
    /**
     * 支付时间
     */
    private Date payTime;
    
    /**
     * 订单商品列表
     */
    private List<OrderGoods> goodsList;
    
    /**
     * 格式化的创建时间
     */
    public String getCreateTimeFormat() {
        if (createTime != null) {
            return new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(createTime);
        }
        return "";
    }
    
    /**
     * 格式化的支付时间
     */
    public String getPayTimeFormat() {
        if (payTime != null) {
            return new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(payTime);
        }
        return "";
    }
    
    /**
     * 计算节省金额（积分+优惠券+余额）
     */
    public BigDecimal getSavedAmount() {
        BigDecimal saved = BigDecimal.ZERO;
        if (integralMoney != null) {
            saved = saved.add(integralMoney);
        }
        if (couponPrice != null) {
            saved = saved.add(couponPrice);
        }
        if (balancePrice != null) {
            saved = saved.add(balancePrice);
        }
        return saved;
    }
    
    /**
     * 获取商品名称列表（用于显示）
     */
    public String getGoodsNames() {
        if (goodsList != null && !goodsList.isEmpty()) {
            return goodsList.stream()
                    .map(OrderGoods::getGoodsName)
                    .reduce((name1, name2) -> name1 + "、" + name2)
                    .orElse("");
        }
        return "";
    }
    
}