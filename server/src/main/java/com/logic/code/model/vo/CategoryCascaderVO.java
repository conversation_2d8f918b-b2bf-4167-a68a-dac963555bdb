package com.logic.code.model.vo;

import com.logic.code.entity.goods.Category;
import lombok.Data;

import java.util.List;

/**
 * VO class for Category data compatible with el-cascader component
 */
@Data
public class CategoryCascaderVO {
    
    private Integer value; // Maps to category id
    private String label;  // Maps to category name
    private List<CategoryCascaderVO> children;
    
    public CategoryCascaderVO() {
    }
    
    public CategoryCascaderVO(Category category) {
        this.value = category.getId();
        this.label = category.getName();
    }
    
    public Integer getValue() {
        return value;
    }
    
    public CategoryCascaderVO setValue(Integer value) {
        this.value = value;
        return this;
    }
    
    public String getLabel() {
        return label;
    }
    
    public CategoryCascaderVO setLabel(String label) {
        this.label = label;
        return this;
    }
    
    public List<CategoryCascaderVO> getChildren() {
        return children;
    }
    
    public CategoryCascaderVO setChildren(List<CategoryCascaderVO> children) {
        this.children = children;
        return this;
    }
} 