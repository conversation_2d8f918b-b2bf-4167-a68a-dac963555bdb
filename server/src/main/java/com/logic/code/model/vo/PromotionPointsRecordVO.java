package com.logic.code.model.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 推广积分记录VO
 */
@Data
@Accessors(chain = true)
public class PromotionPointsRecordVO {
    
    /**
     * 记录ID
     */
    private Integer id;
    
    /**
     * 积分数量
     */
    private Integer points;
    
    /**
     * 记录类型
     */
    private String type;
    
    /**
     * 记录描述
     */
    private String description;
    
    /**
     * 被推广用户昵称
     */
    private String promotedUserNickname;
    
    /**
     * 被推广用户头像
     */
    private String promotedUserAvatar;
    
    /**
     * 推广等级
     */
    private Integer promotionLevel;
    
    /**
     * 推广等级名称
     */
    private String promotionLevelName;
    
    /**
     * 创建时间
     */
    private Date createTime;
}