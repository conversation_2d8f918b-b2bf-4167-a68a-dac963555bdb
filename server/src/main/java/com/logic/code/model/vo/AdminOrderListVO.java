package com.logic.code.model.vo;

import com.logic.code.entity.order.OrderGoods;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 管理员订单列表VO
 * <AUTHOR>
 * @date 2025/1/27
 */
@Data
public class AdminOrderListVO {
    
    /**
     * 订单ID
     */
    private Integer id;
    
    /**
     * 订单号
     */
    private String orderSn;
    
    /**
     * 订单状态码
     */
    private Integer orderStatus;
    
    /**
     * 订单状态文本
     */
    private String orderStatusText;
    
    /**
     * 订单金额
     */
    private BigDecimal orderPrice;
    
    /**
     * 创建时间
     */
    private String createTime;
    
    /**
     * 用户昵称
     */
    private String userName;
    
    /**
     * 用户头像
     */
    private String userAvatar;
    
    /**
     * 用户手机号
     */
    private String userMobile;
    
    /**
     * 商品列表
     */
    private List<OrderGoods> goodsList;
}