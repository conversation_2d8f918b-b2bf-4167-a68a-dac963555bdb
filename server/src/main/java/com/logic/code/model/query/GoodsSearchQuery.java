package com.logic.code.model.query;

import java.util.Objects;

public class GoodsSearchQuery {

    private Integer categoryId;

    private Integer brandId;

    private String keyword;

    private Boolean newly;

    private Boolean isNewly;  // 新增字段，对应前端的isNewly参数

    private Boolean hot;

    private Boolean tieredPromotion;  // 推三返本筛选参数

    private String sort;

    private String order;

    /**
     * 页码，从1开始
     */
    private int pageNum;
    /**
     * 页面大小
     */
    private int pageSize;

    public Integer getCategoryId() {
        return categoryId;
    }

    public GoodsSearchQuery setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
        return this;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public GoodsSearchQuery setBrandId(Integer brandId) {
        this.brandId = brandId;
        return this;
    }

    public String getKeyword() {
        return keyword;
    }

    public GoodsSearchQuery setKeyword(String keyword) {
        this.keyword = keyword;
        return this;
    }

    public Boolean getNewly() {
        return newly;
    }

    public GoodsSearchQuery setNewly(<PERSON>olean newly) {
        this.newly = newly;
        return this;
    }

    public Boolean getIsNewly() {
        return isNewly;
    }

    public GoodsSearchQuery setIsNewly(Boolean isNewly) {
        this.isNewly = isNewly;
        return this;
    }

    public Boolean getHot() {
        return hot;
    }

    public GoodsSearchQuery setHot(Boolean hot) {
        this.hot = hot;
        return this;
    }

    public Boolean getTieredPromotion() {
        return tieredPromotion;
    }

    public GoodsSearchQuery setTieredPromotion(Boolean tieredPromotion) {
        this.tieredPromotion = tieredPromotion;
        return this;
    }

    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    /**
     * 生成缓存友好的哈希码
     */
    @Override
    public int hashCode() {
        return Objects.hash(categoryId, brandId, keyword, newly, isNewly, hot, tieredPromotion, pageNum, pageSize);
    }

    /**
     * 缓存友好的equals方法
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;

        GoodsSearchQuery that = (GoodsSearchQuery) obj;
        return pageNum == that.pageNum &&
               pageSize == that.pageSize &&
               Objects.equals(categoryId, that.categoryId) &&
               Objects.equals(brandId, that.brandId) &&
               Objects.equals(keyword, that.keyword) &&
               Objects.equals(newly, that.newly) &&
               Objects.equals(isNewly, that.isNewly) &&
               Objects.equals(hot, that.hot) &&
               Objects.equals(tieredPromotion, that.tieredPromotion);
    }

    /**
     * 缓存友好的toString方法
     */
    @Override
    public String toString() {
        return "GoodsSearchQuery{" +
               "categoryId=" + categoryId +
               ", brandId=" + brandId +
               ", keyword='" + keyword + '\'' +
               ", newly=" + newly +
               ", isNewly=" + isNewly +
               ", hot=" + hot +
               ", tieredPromotion=" + tieredPromotion +
               ", pageNum=" + pageNum +
               ", pageSize=" + pageSize +
               '}';
    }

    /**
     * 检查查询是否适合缓存
     */
    public boolean isCacheable() {
        // 只缓存前5页的数据
        if (pageNum > 5) {
            return false;
        }

        // 关键词太长不缓存
        if (keyword != null && keyword.length() > 20) {
            return false;
        }

        // 页面大小过大不缓存
        if (pageSize > 100) {
            return false;
        }

        return true;
    }
}
