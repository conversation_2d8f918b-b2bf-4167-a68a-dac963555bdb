package com.logic.code.model.vo;


import com.logic.code.entity.Address;
import com.logic.code.entity.UserCoupon;
import com.logic.code.entity.order.Cart;

import java.math.BigDecimal;
import java.util.List;

public class CartCheckoutVO {

    private BigDecimal actualPrice;

    private CheckedAddressVO checkedAddress;

    private UserCoupon checkedCoupon;

    private List<UserCoupon> couponList;

    private List<Cart> checkedGoodsList;

    private BigDecimal couponPrice;

    private BigDecimal freightPrice;

    private BigDecimal goodsTotalPrice;

    private BigDecimal orderTotalPrice;

    private BigDecimal userBalance; // 用户余额

    private BigDecimal balancePrice; // 使用的余额金额

    private List<UserCoupon> availableCoupons; // 可用的优惠券列表

    private Integer userPoints; // 用户积分

    private Integer usePoints; // 使用的积分数量

    private BigDecimal pointsPrice; // 积分抵扣金额

    private Integer maxUsablePoints; // 最大可用积分数量

    private com.logic.code.entity.PointsConfig pointsConfig; // 积分配置

    public CartCheckoutVO() {
    }

    public static class CheckedAddressVO {

        private Integer id;

        private String name;

        private Integer userId;

        private Short countryId;

        private Short provinceId;

        private Short cityId;

        private Short districtId;

        private String address;

        private String mobile;

        private Boolean isDefault;

        private String provinceName;

        private String cityName;

        private String districtName;

        private String fullRegion;

        public CheckedAddressVO() {
        }

        public CheckedAddressVO(Address address) {
            this.id = address.getId();
            this.name = address.getName();
            this.userId = address.getUserId();
            this.countryId = address.getCountryId();
            this.provinceId = address.getProvinceId();
            this.cityId = address.getCityId();
            this.districtId = address.getDistrictId();
            this.address = address.getAddress();
            this.mobile = address.getMobile();
            this.isDefault = address.getIsDefault();
            this.provinceName = null;
            this.cityName = null;
            this.districtName = null;
            this.fullRegion = null;
        }

        public Integer getId() {
            return id;
        }

        public CheckedAddressVO setId(Integer id) {
            this.id = id;
            return this;
        }

        public String getName() {
            return name;
        }

        public CheckedAddressVO setName(String name) {
            this.name = name;
            return this;
        }

        public Integer getUserId() {
            return userId;
        }

        public CheckedAddressVO setUserId(Integer userId) {
            this.userId = userId;
            return this;
        }

        public Short getCountryId() {
            return countryId;
        }

        public CheckedAddressVO setCountryId(Short countryId) {
            this.countryId = countryId;
            return this;
        }

        public Short getProvinceId() {
            return provinceId;
        }

        public CheckedAddressVO setProvinceId(Short provinceId) {
            this.provinceId = provinceId;
            return this;
        }

        public Short getCityId() {
            return cityId;
        }

        public CheckedAddressVO setCityId(Short cityId) {
            this.cityId = cityId;
            return this;
        }

        public Short getDistrictId() {
            return districtId;
        }

        public CheckedAddressVO setDistrictId(Short districtId) {
            this.districtId = districtId;
            return this;
        }

        public String getAddress() {
            return address;
        }

        public CheckedAddressVO setAddress(String address) {
            this.address = address;
            return this;
        }

        public String getMobile() {
            return mobile;
        }

        public CheckedAddressVO setMobile(String mobile) {
            this.mobile = mobile;
            return this;
        }

        public Boolean getIsDefault() {
            return isDefault;
        }

        public CheckedAddressVO setIsDefault(Boolean isDefault) {
            this.isDefault = isDefault;
            return this;
        }

        public String getProvinceName() {
            return provinceName;
        }

        public CheckedAddressVO setProvinceName(String provinceName) {
            this.provinceName = provinceName;
            return this;
        }

        public String getCityName() {
            return cityName;
        }

        public CheckedAddressVO setCityName(String cityName) {
            this.cityName = cityName;
            return this;
        }

        public String getDistrictName() {
            return districtName;
        }

        public CheckedAddressVO setDistrictName(String districtName) {
            this.districtName = districtName;
            return this;
        }

        public String getFullRegion() {
            return fullRegion;
        }

        public CheckedAddressVO setFullRegion(String fullRegion) {
            this.fullRegion = fullRegion;
            return this;
        }
    }

    public BigDecimal getActualPrice() {
        return actualPrice;
    }

    public CartCheckoutVO setActualPrice(BigDecimal actualPrice) {
        this.actualPrice = actualPrice;
        return this;
    }

    public CheckedAddressVO getCheckedAddress() {
        return checkedAddress;
    }

    public CartCheckoutVO setCheckedAddress(CheckedAddressVO checkedAddress) {
        this.checkedAddress = checkedAddress;
        return this;
    }

    public UserCoupon getCheckedCoupon() {
        return checkedCoupon;
    }

    public CartCheckoutVO setCheckedCoupon(UserCoupon checkedCoupon) {
        this.checkedCoupon = checkedCoupon;
        return this;
    }

    public List<UserCoupon> getCouponList() {
        return couponList;
    }

    public CartCheckoutVO setCouponList(List<UserCoupon> couponList) {
        this.couponList = couponList;
        return this;
    }

    public List<Cart> getCheckedGoodsList() {
        return checkedGoodsList;
    }

    public CartCheckoutVO setCheckedGoodsList(List<Cart> checkedGoodsList) {
        this.checkedGoodsList = checkedGoodsList;
        return this;
    }

    public BigDecimal getCouponPrice() {
        return couponPrice;
    }

    public CartCheckoutVO setCouponPrice(BigDecimal couponPrice) {
        this.couponPrice = couponPrice;
        return this;
    }

    public BigDecimal getFreightPrice() {
        return freightPrice;
    }

    public CartCheckoutVO setFreightPrice(BigDecimal freightPrice) {
        this.freightPrice = freightPrice;
        return this;
    }

    public BigDecimal getGoodsTotalPrice() {
        return goodsTotalPrice;
    }

    public CartCheckoutVO setGoodsTotalPrice(BigDecimal goodsTotalPrice) {
        this.goodsTotalPrice = goodsTotalPrice;
        return this;
    }

    public BigDecimal getOrderTotalPrice() {
        return orderTotalPrice;
    }

    public CartCheckoutVO setOrderTotalPrice(BigDecimal orderTotalPrice) {
        this.orderTotalPrice = orderTotalPrice;
        return this;
    }

    public BigDecimal getUserBalance() {
        return userBalance;
    }

    public CartCheckoutVO setUserBalance(BigDecimal userBalance) {
        this.userBalance = userBalance;
        return this;
    }

    public BigDecimal getBalancePrice() {
        return balancePrice;
    }

    public CartCheckoutVO setBalancePrice(BigDecimal balancePrice) {
        this.balancePrice = balancePrice;
        return this;
    }

    public List<UserCoupon> getAvailableCoupons() {
        return availableCoupons;
    }

    public CartCheckoutVO setAvailableCoupons(List<UserCoupon> availableCoupons) {
        this.availableCoupons = availableCoupons;
        return this;
    }

    public Integer getUserPoints() {
        return userPoints;
    }

    public CartCheckoutVO setUserPoints(Integer userPoints) {
        this.userPoints = userPoints;
        return this;
    }

    public Integer getUsePoints() {
        return usePoints;
    }

    public CartCheckoutVO setUsePoints(Integer usePoints) {
        this.usePoints = usePoints;
        return this;
    }

    public BigDecimal getPointsPrice() {
        return pointsPrice;
    }

    public CartCheckoutVO setPointsPrice(BigDecimal pointsPrice) {
        this.pointsPrice = pointsPrice;
        return this;
    }

    public Integer getMaxUsablePoints() {
        return maxUsablePoints;
    }

    public CartCheckoutVO setMaxUsablePoints(Integer maxUsablePoints) {
        this.maxUsablePoints = maxUsablePoints;
        return this;
    }

    public com.logic.code.entity.PointsConfig getPointsConfig() {
        return pointsConfig;
    }

    public CartCheckoutVO setPointsConfig(com.logic.code.entity.PointsConfig pointsConfig) {
        this.pointsConfig = pointsConfig;
        return this;
    }
}
