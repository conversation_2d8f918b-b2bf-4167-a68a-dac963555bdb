package com.logic.code.model.vo;

import java.math.BigDecimal;

public class CommentCountVO {

    private long allCount;

    private long hasPicCount;

    private long goodCount;

    private long normalCount;

    private long badCount;

    private BigDecimal averageRating;

    private BigDecimal satisfactionRate;

    public CommentCountVO() {
    }

    public CommentCountVO(long allCount, long hasPicCount) {
        this.allCount = allCount;
        this.hasPicCount = hasPicCount;
    }

    public CommentCountVO(long allCount, long hasPicCount, long goodCount, long normalCount, long badCount,
                         BigDecimal averageRating, BigDecimal satisfactionRate) {
        this.allCount = allCount;
        this.hasPicCount = hasPicCount;
        this.goodCount = goodCount;
        this.normalCount = normalCount;
        this.badCount = badCount;
        this.averageRating = averageRating;
        this.satisfactionRate = satisfactionRate;
    }

    public long getAllCount() {
        return allCount;
    }

    public CommentCountVO setAllCount(long allCount) {
        this.allCount = allCount;
        return this;
    }

    public long getHasPicCount() {
        return hasPicCount;
    }

    public CommentCountVO setHasPicCount(long hasPicCount) {
        this.hasPicCount = hasPicCount;
        return this;
    }

    public long getGoodCount() {
        return goodCount;
    }

    public CommentCountVO setGoodCount(long goodCount) {
        this.goodCount = goodCount;
        return this;
    }

    public long getNormalCount() {
        return normalCount;
    }

    public CommentCountVO setNormalCount(long normalCount) {
        this.normalCount = normalCount;
        return this;
    }

    public long getBadCount() {
        return badCount;
    }

    public CommentCountVO setBadCount(long badCount) {
        this.badCount = badCount;
        return this;
    }

    public BigDecimal getAverageRating() {
        return averageRating;
    }

    public CommentCountVO setAverageRating(BigDecimal averageRating) {
        this.averageRating = averageRating;
        return this;
    }

    public BigDecimal getSatisfactionRate() {
        return satisfactionRate;
    }

    public CommentCountVO setSatisfactionRate(BigDecimal satisfactionRate) {
        this.satisfactionRate = satisfactionRate;
        return this;
    }
}
