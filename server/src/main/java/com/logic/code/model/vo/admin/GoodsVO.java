package com.logic.code.model.vo.admin;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/10 20:35
 * @desc
 */
@Data
public class GoodsVO {

    private Integer id;

    private String storeName;

    private List<Integer> categoryIds;

    private List<Integer> labelIds;

    private String keyword;

    private String unitName;

    private String storeInfo;

    private String image;

    private List<String> sliderImages;

    private String description;

    private Integer virtualSales;

    private Integer giveIntegral;

    private Integer sort;

    private Integer isShow;

    private Integer isHot;

    private Integer isBenefit;

    private Integer isBest;

    private Integer isNew;

    private Integer isGood;

    private Integer isPostage;

    private List<Integer> isSub;

    private Integer specType;

    private Integer isVirtual;

    private Integer virtualType;

    private String videoLink;

    private Integer postage;

    private Integer tempId;

    private List<Map<String, Object>> attrs;

    private List<Map<String, Object>> items;

    private List<String> logistics;

    private Integer freight;

    private List<Integer> couponIds;

    private List<Integer> labelList;

    private List<Map<String, String>> paramsList;

    private List<Integer> protectionList;

    private List<Map<String, Object>> customForm;

    private List<Map<String, Object>> recommendList;

    private Integer isCopy;

    /**
     * 商品展示系统ID列表
     */
    private List<Integer> displaySystems;

    /**
     * 商品单价
     */
    private BigDecimal unitPrice;

    /**
     * 商品详情标签，多个标签用逗号分割
     */
    private String detailTag;
}
