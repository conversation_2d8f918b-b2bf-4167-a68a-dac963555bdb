package com.logic.code.controller.admin;

import com.logic.code.common.response.Result;
import com.logic.code.service.PointsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 积分管理控制器
 * 用于管理员操作积分相关功能
 */
@RestController
@RequestMapping("/admin/points")
@Slf4j
public class PointsManagementController {

    @Autowired
    private PointsService pointsService;

    /**
     * 修复历史积分退回问题
     * 管理员可以调用此接口修复历史数据中积分未正确退回的问题
     */
    @PostMapping("/fix-historical-refund-issues")
    public Result<String> fixHistoricalRefundIssues() {
        try {
            log.info("管理员触发修复历史积分退回问题");
            pointsService.fixHistoricalPointsRefundIssues();
            return Result.success("历史积分退回问题修复完成");
        } catch (Exception e) {
            log.error("修复历史积分退回问题失败", e);
            return Result.failure("修复失败：" + e.getMessage());
        }
    }

    /**
     * 验证用户积分数据一致性
     * 检查用户表中的积分是否与积分记录表计算出的总积分一致
     */
    @GetMapping("/validate-consistency")
    public Result<String> validatePointsConsistency() {
        try {
            log.info("管理员触发验证用户积分数据一致性");
            pointsService.validateUserPointsConsistency();
            return Result.success("积分数据一致性验证完成，请查看日志");
        } catch (Exception e) {
            log.error("验证积分数据一致性失败", e);
            return Result.failure("验证失败：" + e.getMessage());
        }
    }

    /**
     * 手动退回指定订单的积分
     * 用于处理特殊情况下的积分退回
     */
    @PostMapping("/manual-refund/{orderId}")
    public Result<String> manualRefundPoints(@PathVariable Integer orderId) {
        try {
            log.info("管理员手动退回订单{}的积分", orderId);
            
            // 这里需要先获取订单信息
            // Order order = orderService.queryById(orderId);
            // if (order == null) {
            //     return Result.failure("订单不存在");
            // }
            
            // boolean result = pointsService.refundPointsFromOrder(order.getUserId(), orderId);
            // if (result) {
            //     return Result.success("积分退回成功");
            // } else {
            //     return Result.failure("积分退回失败");
            // }
            
            return Result.failure("功能暂未实现，请使用其他方式");
        } catch (Exception e) {
            log.error("手动退回积分失败", e);
            return Result.failure("退回失败：" + e.getMessage());
        }
    }

    /**
     * 重新计算指定用户的积分总数
     */
    @PostMapping("/recalculate-user-points/{userId}")
    public Result<String> recalculateUserPoints(@PathVariable Integer userId) {
        try {
            log.info("管理员重新计算用户{}的积分总数", userId);
            pointsService.updateUserPoints(userId);
            return Result.success("用户积分重新计算完成");
        } catch (Exception e) {
            log.error("重新计算用户积分失败", e);
            return Result.failure("计算失败：" + e.getMessage());
        }
    }
}
