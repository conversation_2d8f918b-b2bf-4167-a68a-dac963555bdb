package com.logic.code.controller.admin;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logic.code.entity.User;
import com.logic.code.entity.order.Order;
import com.logic.code.entity.order.OrderGoods;
import com.logic.code.model.vo.PointsOrderListVO;
import com.logic.code.mapper.OrderMapper;
import com.logic.code.service.OrderGoodsService;
import com.logic.code.service.OrderService;
import com.logic.code.service.UserService;
import com.logic.code.common.response.Result;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 营销积分订单控制器
 * 用于管理和查看使用了积分抵扣的订单
 * 路径: /marketing/integral/order
 */
@RestController
@RequestMapping("/adminapi/marketing/integral/order")
@Slf4j
public class MarketingIntegralOrderController {

    @Resource
    private OrderService orderService;

    @Resource
    private OrderMapper orderMapper;

    @Resource
    private OrderGoodsService orderGoodsService;

    @Resource
    private UserService userService;

    /**
     * 获取使用了积分抵扣的订单列表
     * GET /marketing/integral/order/list
     *
     * @param page        页码
     * @param limit       每页大小
     * @param orderSn     订单号（可选）
     * @param userKeyword 用户关键词（昵称或手机号，可选）
     * @param startTime   开始时间（可选）
     * @param endTime     结束时间（可选）
     * @return 积分抵扣订单列表
     */
    @GetMapping("/list")
    public Result<Map<String, Object>> getPointsOrderList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "15") Integer limit,
            @RequestParam(required = false) String orderSn,
            @RequestParam(required = false) String userKeyword,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime) {

        try {
            log.info("获取积分抵扣订单列表，页码：{}，每页大小：{}", page, limit);

            // 构建查询条件
            QueryWrapper<Order> queryWrapper = new QueryWrapper<>();

            // 只查询使用了积分的订单（integral > 0 且 integralMoney > 0）
            queryWrapper.gt("integral", 0)
                       .gt("integral_money", 0);

            // 订单号条件
            if (orderSn != null && !orderSn.trim().isEmpty()) {
                queryWrapper.like("order_sn", orderSn.trim());
            }

            // 时间范围条件
            if (startTime != null && !startTime.trim().isEmpty()) {
                queryWrapper.ge("create_time", startTime + " 00:00:00");
            }
            if (endTime != null && !endTime.trim().isEmpty()) {
                queryWrapper.le("create_time", endTime + " 23:59:59");
            }

            // 按创建时间倒序排列
            queryWrapper.orderByDesc("create_time");

            // 分页查询
            Page<Order> orderPage = new Page<>(page, limit);
            Page<Order> resultPage = orderMapper.selectPage(orderPage, queryWrapper);

            // 构建返回数据
            List<PointsOrderListVO> pointsOrderList = new ArrayList<>();
            for (Order order : resultPage.getRecords()) {
                try {
                    // 获取用户信息
                    User user = userService.queryById(order.getUserId());
                    
                    // 用户关键词过滤（如果提供了用户关键词）
                    if (userKeyword != null && !userKeyword.trim().isEmpty()) {
                        String keyword = userKeyword.trim().toLowerCase();
                        boolean matchUser = false;
                        
                        if (user != null) {
                            // 检查昵称
                            if (user.getNickname() != null && 
                                user.getNickname().toLowerCase().contains(keyword)) {
                                matchUser = true;
                            }
                            // 检查手机号
                            if (user.getMobile() != null && 
                                user.getMobile().contains(keyword)) {
                                matchUser = true;
                            }
                        }
                        
                        if (!matchUser) {
                            continue; // 跳过不匹配的订单
                        }
                    }

                    // 获取订单商品信息
                    QueryWrapper<OrderGoods> goodsWrapper = new QueryWrapper<>();
                    goodsWrapper.eq("order_id", order.getId());
                    List<OrderGoods> orderGoodsList = orderGoodsService.queryList(goodsWrapper);

                    // 构建VO对象
                    PointsOrderListVO pointsOrderVO = new PointsOrderListVO();
                    pointsOrderVO.setId(order.getId());
                    pointsOrderVO.setOrderSn(order.getOrderSn());
                    pointsOrderVO.setUserId(order.getUserId());
                    pointsOrderVO.setUserNickname(user != null ? user.getNickname() : "未知用户");
                    pointsOrderVO.setUserMobile(user != null ? user.getMobile() : "");
                    pointsOrderVO.setUserAvatar(user != null ? user.getAvatar() : "");
                    pointsOrderVO.setOrderStatus(order.getOrderStatus().getValue());
                    pointsOrderVO.setOrderStatusText(order.getOrderStatus().getName());
                    pointsOrderVO.setPayStatus(order.getPayStatus().getValue());
                    pointsOrderVO.setPayStatusText(order.getPayStatus().getName());
                    pointsOrderVO.setOrderPrice(order.getOrderPrice());
                    pointsOrderVO.setActualPrice(order.getActualPrice());
                    pointsOrderVO.setGoodsPrice(order.getGoodsPrice());
                    pointsOrderVO.setFreightPrice(order.getFreightPrice() != null ? order.getFreightPrice() : BigDecimal.ZERO);
                    pointsOrderVO.setCouponPrice(order.getCouponPrice() != null ? order.getCouponPrice() : BigDecimal.ZERO);
                    pointsOrderVO.setIntegral(order.getIntegral());
                    pointsOrderVO.setIntegralMoney(order.getIntegralMoney());
                    pointsOrderVO.setBalancePrice(order.getBalancePrice() != null ? order.getBalancePrice() : BigDecimal.ZERO);
                    pointsOrderVO.setCreateTime(order.getCreateTime());
                    pointsOrderVO.setPayTime(order.getPayTime());
                    pointsOrderVO.setGoodsList(orderGoodsList);

                    pointsOrderList.add(pointsOrderVO);
                } catch (Exception e) {
                    log.error("处理订单{}时发生错误：{}", order.getId(), e.getMessage());
                    // 继续处理其他订单，不抛出异常
                }
            }

            // 构建分页结果
            Map<String, Object> result = new HashMap<>();
            result.put("list", pointsOrderList);
            result.put("count", (int) resultPage.getTotal());
            result.put("limit", limit);
            result.put("page", page);

            log.info("获取积分抵扣订单列表成功，共{}条记录", pointsOrderList.size());
            return Result.success(result);

        } catch (Exception e) {
            log.error("获取积分抵扣订单列表失败：{}", e.getMessage(), e);
            return Result.failure("获取积分抵扣订单列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取积分抵扣订单统计信息
     * GET /marketing/integral/order/statistics
     *
     * @param startTime 开始时间（可选）
     * @param endTime   结束时间（可选）
     * @return 统计信息
     */
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getPointsOrderStatistics(
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime) {

        try {
            log.info("获取积分抵扣订单统计信息");

            // 构建查询条件
            QueryWrapper<Order> queryWrapper = new QueryWrapper<>();
            queryWrapper.gt("integral", 0)
                       .gt("integral_money", 0);

            // 时间范围条件
            if (startTime != null && !startTime.trim().isEmpty()) {
                queryWrapper.ge("create_time", startTime + " 00:00:00");
            }
            if (endTime != null && !endTime.trim().isEmpty()) {
                queryWrapper.le("create_time", endTime + " 23:59:59");
            }

            // 查询所有符合条件的订单
            List<Order> orders = orderMapper.selectList(queryWrapper);

            // 统计计算
            int totalOrders = orders.size();
            Integer totalPointsUsed = orders.stream()
                    .mapToInt(order -> order.getIntegral() != null ? order.getIntegral() : 0)
                    .sum();
            BigDecimal totalPointsMoney = orders.stream()
                    .map(order -> order.getIntegralMoney() != null ? order.getIntegralMoney() : BigDecimal.ZERO)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal totalOrderAmount = orders.stream()
                    .map(order -> order.getOrderPrice() != null ? order.getOrderPrice() : BigDecimal.ZERO)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 计算平均值
            BigDecimal avgPointsPerOrder = totalOrders > 0 ? 
                    new BigDecimal(totalPointsUsed).divide(new BigDecimal(totalOrders), 2, BigDecimal.ROUND_HALF_UP) : 
                    BigDecimal.ZERO;
            BigDecimal avgPointsMoneyPerOrder = totalOrders > 0 ? 
                    totalPointsMoney.divide(new BigDecimal(totalOrders), 2, BigDecimal.ROUND_HALF_UP) : 
                    BigDecimal.ZERO;

            // 构建统计结果
            return Result.success(Map.of(
                    "totalOrders", totalOrders,
                    "totalPointsUsed", totalPointsUsed,
                    "totalPointsMoney", totalPointsMoney,
                    "totalOrderAmount", totalOrderAmount,
                    "avgPointsPerOrder", avgPointsPerOrder,
                    "avgPointsMoneyPerOrder", avgPointsMoneyPerOrder
            ));

        } catch (Exception e) {
            log.error("获取积分抵扣订单统计信息失败：{}", e.getMessage(), e);
            return Result.failure("获取统计信息失败：" + e.getMessage());
        }
    }
}