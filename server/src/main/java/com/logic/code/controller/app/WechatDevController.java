package com.logic.code.controller.app;

import com.logic.code.common.constants.WechatConstants;
import com.logic.code.common.exception.WeshopException;
import com.logic.code.common.response.CommonResultStatus;
import com.logic.code.common.response.Result;
import com.logic.code.common.utils.JsonUtils;
import com.logic.code.common.utils.JwtHelper;
import com.logic.code.entity.User;
import com.logic.code.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
//@Api(tags = "开发人员测试接口，包括获取Token等，只在dev环境可见")
@RestController
@RequestMapping("/wechat/dev")
@Profile("dev")
public class WechatDevController {

  @Autowired
  private UserService userService;

  @PostMapping("{userId}/token")
  //@ApiOperation("【慎用】根据id获取Token用于测试")
  public Result<String> getTokenByUserId(@PathVariable("userId") String userId) {
    User user = userService.queryById(userId);
    if (user == null) {
        throw new WeshopException(CommonResultStatus.RECORD_NOT_EXIST);
    }
    //生成token
    String token = JwtHelper.createJWT("wechat", JsonUtils.toJson(user), WechatConstants.JWT_TTL);
      return Result.success(token);
  }

}
