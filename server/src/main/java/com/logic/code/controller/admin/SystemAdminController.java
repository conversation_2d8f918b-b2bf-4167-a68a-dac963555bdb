package com.logic.code.controller.admin;

import com.logic.code.common.response.Result;
import com.logic.code.model.dto.AdminFormResponse;
import com.logic.code.model.dto.AdminRequest;
import com.logic.code.service.AdminService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 系统管理员管理控制器
 * 
 * <AUTHOR>
 * @date 2025/8/25
 */
@Slf4j
@RestController
@RequestMapping("/adminapi/setting")
public class SystemAdminController {

    @Resource
    private AdminService adminService;

    /**
     * 管理员列表查询接口
     * 
     * @param request 查询请求参数
     * @return 管理员列表
     */
    @GetMapping("/admin")
    public Result<Map<String, Object>> getAdminList(AdminRequest request) {
        try {
            Map<String, Object> result = adminService.getAdminList(request);
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询管理员列表失败", e);
            return Result.failure("查询管理员列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取新增管理员表单数据接口
     * 
     * @return 表单数据
     */
    @GetMapping("/admin/create")
    public Result<AdminFormResponse> getCreateFormData() {
        try {
            AdminFormResponse response = adminService.getCreateFormData();
            return Result.success(response);
        } catch (Exception e) {
            log.error("获取新增管理员表单数据失败", e);
            return Result.failure("获取表单数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取编辑管理员表单数据接口
     * 
     * @param id 管理员ID
     * @return 表单数据
     */
    @GetMapping("/admin/{id}/edit")
    public Result<AdminFormResponse> getEditFormData(@PathVariable Integer id) {
        try {
            AdminFormResponse response = adminService.getEditFormData(id);
            return Result.success(response);
        } catch (Exception e) {
            log.error("获取编辑管理员表单数据失败，ID: {}", id, e);
            return Result.failure("获取管理员信息失败：" + e.getMessage());
        }
    }

    /**
     * 创建管理员接口
     * 
     * @param request 管理员请求数据
     * @return 创建结果
     */
    @PostMapping("/admin")
    public Result<Map<String, Object>> createAdmin(@RequestBody AdminRequest request) {
        try {
            Map<String, Object> result = adminService.createAdmin(request);
            return Result.success(result);
        } catch (Exception e) {
            log.error("创建管理员失败", e);
            return Result.failure("创建管理员失败：" + e.getMessage());
        }
    }

    /**
     * 更新管理员接口
     * 
     * @param id 管理员ID
     * @param request 管理员请求数据
     * @return 更新结果
     */
    @PutMapping("/admin/{id}")
    public Result<Map<String, Object>> updateAdmin(@PathVariable Integer id, @RequestBody AdminRequest request) {
        try {
            Map<String, Object> result = adminService.updateAdmin(id, request);
            return Result.success(result);
        } catch (Exception e) {
            log.error("更新管理员失败，ID: {}", id, e);
            return Result.failure("更新管理员失败：" + e.getMessage());
        }
    }

    /**
     * 删除管理员接口
     * 
     * @param id 管理员ID
     * @return 删除结果
     */
    @DeleteMapping("/admin/{id}")
    public Result<Map<String, Object>> deleteAdmin(@PathVariable Integer id) {
        try {
            Map<String, Object> result = adminService.deleteAdmin(id);
            return Result.success(result);
        } catch (Exception e) {
            log.error("删除管理员失败，ID: {}", id, e);
            return Result.failure("删除管理员失败：" + e.getMessage());
        }
    }

    /**
     * 修改管理员状态接口
     * 
     * @param id 管理员ID
     * @param status 状态值
     * @return 修改结果
     */
    @PutMapping("/set_status/{id}/{status}")
    public Result<String> updateAdminStatus(@PathVariable Integer id, @PathVariable Integer status) {
        try {
            Map<String, Object> result = adminService.updateAdminStatus(id, status);
            return Result.success((String) result.get("message"));
        } catch (Exception e) {
            log.error("修改管理员状态失败，ID: {}, status: {}", id, status, e);
            return Result.failure("修改管理员状态失败：" + e.getMessage());
        }
    }
}