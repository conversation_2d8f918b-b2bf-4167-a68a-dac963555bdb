package com.logic.code.controller.admin;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.HashMap;
import java.util.Map;
/**
 * <AUTHOR>
 * @date 2025/7/8 20:19
 * @desc
 */
@RestController
@RequestMapping("/adminapi")
public class ViewController {
    @RequestMapping("/get_workerman_url")
    public Map<String, Object> getWorkermanUrl() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", 200);
        result.put("msg", "success");

        Map<String, String> data = new HashMap<>();
        data.put("admin", "wss://v5.crmeb.net/notice");
        data.put("chat", "wss://v5.crmeb.net/msg");

        result.put("data", data);
        return result;
    }
}
