package com.logic.code.controller.app;

import com.logic.code.common.response.Result;
import com.logic.code.service.WxQrCodeService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 微信小程序二维码控制器
 */
@RestController
@RequestMapping("/api/wx/qrcode")
@RequiredArgsConstructor
public class WxQrCodeController {

    private final WxQrCodeService wxQrCodeService;

    @Value("${wx.miniapp.configs[0].appid}")
    private String appid;

    /**
     * 获取不限制的小程序码
     *
     * @param scene 参数，最大32个可见字符
     * @param page 页面路径，为空则是首页
     * @param width 宽度，单位px，默认430，最小280px，最大1280px
     * @param autoColor 是否自动配置线条颜色，默认false
     * @param r 红色（0-255），autoColor为false时有效
     * @param g 绿色（0-255），autoColor为false时有效
     * @param b 蓝色（0-255），autoColor为false时有效
     * @param isHyaline 是否需要透明底色，默认false
     * @param envVersion 版本类型，release、develop、trial，默认release
     * @return 二维码图片
     */
    @GetMapping("/unlimited")
    public ResponseEntity<byte[]> getUnlimitedQrCode(
            @RequestParam String scene,
            @RequestParam(required = false) String page,
            @RequestParam(required = false, defaultValue = "430") int width,
            @RequestParam(required = false, defaultValue = "false") boolean autoColor,
            @RequestParam(required = false, defaultValue = "0") String r,
            @RequestParam(required = false, defaultValue = "0") String g,
            @RequestParam(required = false, defaultValue = "0") String b,
            @RequestParam(required = false, defaultValue = "false") boolean isHyaline,
            @RequestParam(required = false, defaultValue = "release") String envVersion) {

        // 创建线条颜色对象
        cn.binarywang.wx.miniapp.bean.WxMaCodeLineColor lineColor = null;
        if (!autoColor) {
            lineColor = wxQrCodeService.createLineColor(r, g, b);
        }

        // 生成小程序码
        byte[] qrCodeBytes = wxQrCodeService.createUnlimitedQrCode(scene, page, width, autoColor, lineColor, isHyaline, envVersion);

        // 设置响应头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.IMAGE_PNG);

        return new ResponseEntity<>(qrCodeBytes, headers, HttpStatus.OK);
    }

    /**
     * 获取简单的小程序码（使用默认参数）
     *
     * @param scene 参数，最大32个可见字符
     * @param page 页面路径，为空则是首页
     * @return 二维码图片
     */
    @GetMapping("/simple")
    public ResponseEntity<byte[]> getSimpleQrCode(
            @RequestParam String scene,
            @RequestParam(required = false) String page) {

        byte[] qrCodeBytes = wxQrCodeService.createUnlimitedQrCode(scene, page);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.IMAGE_PNG);

        return new ResponseEntity<>(qrCodeBytes, headers, HttpStatus.OK);
    }

    /**
     * 获取小程序二维码 (与微信官方API一致的接口实现)
     * 适用于需要的码数量较少的业务场景。通过该接口生成的小程序码，永久有效，有数量限制(100,000)
     *
     * @param params 请求参数，包含path和width
     * @return 二维码图片或错误信息
     */
    @PostMapping("/createQRCode")
    public ResponseEntity<?> createQRCode(@RequestBody Map<String, Object> params) {
        try {
            // 获取参数
            String path = (String) params.get("path");

            // 路径验证
            if (path == null || path.isEmpty() || path.length() > 128) {
                Map<String, Object> error = new HashMap<>();
                error.put("errcode", 40159);
                error.put("errmsg", "invalid length for path, or the data is not json string");
                return new ResponseEntity<>(error, HttpStatus.BAD_REQUEST);
            }

            // 检查是否包含系统保留参数
            if (path.contains("scancode_time")) {
                Map<String, Object> error = new HashMap<>();
                error.put("errcode", 85096);
                error.put("errmsg", "page or path not allow include scancode_time field");
                return new ResponseEntity<>(error, HttpStatus.BAD_REQUEST);
            }

            // 获取宽度参数，默认为430
            Integer width = 430;
            if (params.containsKey("width")) {
                width = Integer.parseInt(params.get("width").toString());

                // 宽度验证
                if (width < 280) {
                    width = 280; // 最小宽度
                } else if (width > 1280) {
                    width = 1280; // 最大宽度
                }
            }

            // 调用服务生成二维码
            byte[] qrCodeBytes = wxQrCodeService.createQRCode(path, width);

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.IMAGE_PNG);

            return new ResponseEntity<>(qrCodeBytes, headers, HttpStatus.OK);

        } catch (Exception e) {
            // 超出配额或其他错误
            Map<String, Object> error = new HashMap<>();
            if (e.getMessage().contains("count out of limit")) {
                error.put("errcode", 45029);
                error.put("errmsg", "qrcode count out of limit");
            } else {
                error.put("errcode", -1);
                error.put("errmsg", "system error");
            }
            return new ResponseEntity<>(error, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 测试小程序码生成 - 方形二维码
     * @return 方形二维码图片
     */
    @GetMapping("/test")
    public ResponseEntity<byte[]> test() {
        String scene = "WJSM00154258889001X";
        String page = "pages/index/index";

        // 创建方形二维码，设置合适的宽度
        int width = 430; // 标准正方形尺寸
        boolean autoColor = false;
        boolean isHyaline = false; // 不透明背景，确保方形效果更明显
        String envVersion = "release";

        // 创建黑色线条，增强对比度
        cn.binarywang.wx.miniapp.bean.WxMaCodeLineColor lineColor =
            wxQrCodeService.createLineColor("0", "0", "0");

        // 生成方形小程序码
        byte[] qrCodeBytes = wxQrCodeService.createUnlimitedQrCode(
            scene, page, width, autoColor, lineColor, isHyaline, envVersion);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.IMAGE_PNG);

        return new ResponseEntity<>(qrCodeBytes, headers, HttpStatus.OK);
    }

    @GetMapping("/demo")
    public ResponseEntity<byte[]> demo() {

        // 创建方形二维码，设置合适的宽度
        int width = 430; // 标准正方形尺寸
        // 生成方形小程序码
        byte[] qrCodeBytes = wxQrCodeService.createQRCode("pages/index/index", width);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.IMAGE_PNG);

        return new ResponseEntity<>(qrCodeBytes, headers, HttpStatus.OK);
    }

    /**
     * 获取配置信息
     * @return 配置信息
     */
    @GetMapping("/config")
    public Result<Map<String, String>> getConfig() {
        Map<String, String> config = new HashMap<>();
        config.put("appid", appid);
        return Result.success(config);
    }
}
