package com.logic.code.controller.admin;

import com.logic.code.common.response.Result;
import com.logic.code.service.PointsService;
import com.logic.code.service.PointsConsistencyScheduleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 积分系统测试控制器
 * 用于测试积分数据一致性检查和修复功能
 * 
 * <AUTHOR>
 * @date 2025/1/3
 */
@RestController
@RequestMapping("/adminapi/points/test")
@Slf4j
public class PointsTestController {
    
    @Autowired
    private PointsService pointsService;
    
    @Autowired
    private PointsConsistencyScheduleService consistencyScheduleService;
    
    /**
     * 测试积分一致性检查
     * GET /adminapi/points/test/check-consistency
     */
    @GetMapping("/check-consistency")
    public Result<Map<String, Object>> testCheckConsistency() {
        try {
            log.info("测试积分一致性检查");
            
            Map<String, Object> result = pointsService.validateUserPointsConsistency();
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("测试积分一致性检查失败", e);
            return Result.failure("测试失败：" + e.getMessage());
        }
    }
    
    /**
     * 测试积分数据修复（仅检查，不实际修复）
     * GET /adminapi/points/test/check-fix
     */
    @GetMapping("/check-fix")
    public Result<Map<String, Object>> testCheckFix() {
        try {
            log.info("测试积分数据修复检查（不实际修复）");
            
            Map<String, Object> result = pointsService.fixUserPointsInconsistency(false);
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("测试积分数据修复检查失败", e);
            return Result.failure("测试失败：" + e.getMessage());
        }
    }
    
    /**
     * 测试积分数据修复（实际修复）
     * POST /adminapi/points/test/do-fix
     */
    @PostMapping("/do-fix")
    public Result<Map<String, Object>> testDoFix() {
        try {
            log.info("测试积分数据修复（实际修复）");
            
            Map<String, Object> result = pointsService.fixUserPointsInconsistency(true);
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("测试积分数据修复失败", e);
            return Result.failure("测试失败：" + e.getMessage());
        }
    }
    
    /**
     * 测试定时任务手动触发
     * POST /adminapi/points/test/manual-schedule
     */
    @PostMapping("/manual-schedule")
    public Result<Map<String, Object>> testManualSchedule(@RequestParam(defaultValue = "false") boolean autoFix) {
        try {
            log.info("测试定时任务手动触发，自动修复：{}", autoFix);
            
            Map<String, Object> result = consistencyScheduleService.manualPointsConsistencyCheck(autoFix);
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("测试定时任务手动触发失败", e);
            return Result.failure("测试失败：" + e.getMessage());
        }
    }
    
    /**
     * 测试单个用户积分修复
     * POST /adminapi/points/test/fix-user/{userId}
     */
    @PostMapping("/fix-user/{userId}")
    public Result<String> testFixUser(@PathVariable Integer userId) {
        try {
            log.info("测试修复用户{}的积分", userId);
            
            pointsService.updateUserPoints(userId);
            
            return Result.success("用户积分修复测试成功");
        } catch (Exception e) {
            log.error("测试修复用户{}积分失败", userId, e);
            return Result.failure("测试失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取积分系统配置信息
     * GET /adminapi/points/test/config
     */
    @GetMapping("/config")
    public Result<Map<String, Object>> getConfig() {
        try {
            Map<String, Object> config = consistencyScheduleService.getConsistencyCheckConfig();
            return Result.success(config);
        } catch (Exception e) {
            log.error("获取积分系统配置失败", e);
            return Result.failure("获取配置失败：" + e.getMessage());
        }
    }
    
    /**
     * 模拟创建积分不一致数据（仅用于测试）
     * POST /adminapi/points/test/create-inconsistency/{userId}
     */
    @PostMapping("/create-inconsistency/{userId}")
    public Result<String> createInconsistency(@PathVariable Integer userId, @RequestParam Integer points) {
        try {
            log.warn("⚠️ 测试：为用户{}创建积分不一致数据，积分差值：{}", userId, points);
            
            // 这里可以添加创建不一致数据的逻辑，仅用于测试
            // 注意：这个功能仅用于测试环境，生产环境应该禁用
            
            return Result.success("测试数据创建成功（注意：仅用于测试）");
        } catch (Exception e) {
            log.error("创建测试数据失败", e);
            return Result.failure("创建测试数据失败：" + e.getMessage());
        }
    }
}
