package com.logic.code.controller.app;

import com.logic.code.common.response.Result;
import com.logic.code.service.CouponConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 优惠券配置控制器
 * <AUTHOR>
 * @date 2025/7/25
 */
@RestController
@RequestMapping("/wechat/coupon-config")
@Slf4j
public class CouponConfigController {

    @Autowired
    private CouponConfigService couponConfigService;

    /**
     * 获取优惠券使用规则
     */
    @GetMapping("/rules")
    public Result<Map<String, Object>> getCouponRules() {
        try {
            Map<String, Object> rules = couponConfigService.getCouponRules();
            return Result.success(rules);
        } catch (Exception e) {
            log.error("获取优惠券规则失败", e);
            return Result.failure("获取优惠券规则失败：" + e.getMessage());
        }
    }

    /**
     * 更新优惠券使用规则（管理员功能）
     */
    @PostMapping("/update-rule")
    public Result<Boolean> updateCouponRule(@RequestBody Map<String, Object> params) {
        try {
            String couponType = params.get("couponType").toString();
            BigDecimal minAmount = new BigDecimal(params.get("minAmount").toString());
            
            couponConfigService.updateCouponRule(couponType, minAmount);
            return Result.success(true);
        } catch (Exception e) {
            log.error("更新优惠券规则失败", e);
            return Result.failure("更新优惠券规则失败：" + e.getMessage());
        }
    }
}