package com.logic.code.controller.admin;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logic.code.common.response.Result;
import com.logic.code.entity.PointsRecord;
import com.logic.code.entity.User;
import com.logic.code.mapper.PointsRecordMapper;
import com.logic.code.mapper.UserMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 积分记录管理控制器
 * 用于管理员查看和管理积分记录
 */
@RestController
@RequestMapping("/adminapi/marketing")
@Slf4j
public class PointRecordController {

    @Resource
    private PointsRecordMapper pointsRecordMapper;
    
    @Resource
    private UserMapper userMapper;

    /**
     * 获取积分记录列表
     * GET /admin/marketing/point_record
     */
    @GetMapping("/point_record")
    public Result<Map<String, Object>> getPointRecordList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer limit,
            @RequestParam(required = false) String time,
            @RequestParam(required = false) String trading_type,
            @RequestParam(required = false) String keywords) {
        
        try {
            log.info("获取积分记录列表 - page: {}, limit: {}, time: {}, trading_type: {}, keywords: {}", 
                    page, limit, time, trading_type, keywords);

            // 创建分页对象
            Page<PointsRecord> pageObj = new Page<>(page, limit);
            
            // 构建查询条件
            QueryWrapper<PointsRecord> wrapper = new QueryWrapper<>();
            
            // 时间范围筛选
            if (StringUtils.hasText(time)) {
                String[] timeRange = time.split("-");
                if (timeRange.length == 2) {
                    try {
                        String startDate = timeRange[0].trim() + " 00:00:00";
                        String endDate = timeRange[1].trim() + " 23:59:59";
                        wrapper.ge("create_time", startDate)
                               .le("create_time", endDate);
                    } catch (Exception e) {
                        log.warn("时间格式解析失败: {}", time, e);
                    }
                }
            }
            
            // 交易类型筛选
            if (StringUtils.hasText(trading_type)) {
                wrapper.eq("type", trading_type);
            }
            
            // 关键词搜索（按用户昵称或备注搜索）
            if (StringUtils.hasText(keywords)) {
                // 先查找匹配昵称的用户ID
                QueryWrapper<User> userWrapper = new QueryWrapper<>();
                userWrapper.like("nickname", keywords);
                List<User> users = userMapper.selectList(userWrapper);
                List<Integer> userIds = users.stream()
                        .map(User::getId)
                        .collect(Collectors.toList());
                
                if (!userIds.isEmpty()) {
                    wrapper.and(w -> w.in("user_id", userIds).or().like("remark", keywords));
                } else {
                    wrapper.like("remark", keywords);
                }
            }
            
            // 按创建时间倒序排列
            wrapper.orderByDesc("create_time");
            
            // 执行分页查询
            IPage<PointsRecord> result = pointsRecordMapper.selectPage(pageObj, wrapper);
            
            // 获取用户信息并组装响应数据
            List<Map<String, Object>> recordList = result.getRecords().stream().map(record -> {
                Map<String, Object> item = new HashMap<>();
                item.put("id", record.getId());
                
                // 处理关联订单信息
                String relation = "-";
                if (record.getSourceId() != null) {
                    if ("order".equals(record.getSource())) {
                        relation = "订单:" + record.getSourceId();
                    } else if ("promotion".equals(record.getSource())) {
                        relation = "推广用户:" + record.getSourceId();
                    } else {
                        relation = record.getSource() + ":" + record.getSourceId();
                    }
                }
                item.put("relation", relation);
                item.put("add_time", record.getCreateTime());
                item.put("number", Math.abs(record.getPoints()));
                item.put("pm", record.getPoints() > 0); // true为正数，false为负数
                
                // 获取用户昵称
                User user = userMapper.selectById(record.getUserId());
                item.put("nickname", user != null ? user.getNickname() : "未知用户");
                
                // 交易类型名称
                item.put("type_name", getTypeName(record.getType(), record.getSource()));
                
                // 备注
                item.put("mark", StringUtils.hasText(record.getRemark()) ? record.getRemark() : 
                        StringUtils.hasText(record.getDescription()) ? record.getDescription() : "");
                
                return item;
            }).collect(Collectors.toList());
            
            // 构建交易类型选项
            Map<String, String> statusOptions = new HashMap<>();
            statusOptions.put("", "全部");
            statusOptions.put("earn", "获得积分");
            statusOptions.put("use", "消费积分");
            statusOptions.put("refund", "退回积分");
            
            // 组装响应结果
            Map<String, Object> response = new HashMap<>();
            response.put("list", recordList);
            response.put("count", result.getTotal());
            response.put("status", statusOptions);
            
            return Result.success(response);
            
        } catch (Exception e) {
            log.error("获取积分记录列表失败", e);
            return Result.failure("获取积分记录列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取推广用户信息
     * GET /admin/marketing/point_record/promotion_user/{userId}
     */
    @GetMapping("/point_record/promotion_user/{userId}")
    public Result<Map<String, Object>> getPromotionUserInfo(@PathVariable Integer userId) {
        try {
            log.info("获取推广用户信息 - userId: {}", userId);

            // 查询用户是否存在
            User user = userMapper.selectById(userId);
            if (user == null) {
                return Result.failure("用户不存在");
            }
            
            // 组装用户信息
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("id", user.getId());
            userInfo.put("nickname", user.getNickname());
            userInfo.put("avatar", user.getAvatar());
            userInfo.put("mobile", user.getMobile());
            userInfo.put("registerTime", user.getRegisterTime());
            userInfo.put("lastLoginTime", user.getLastLoginTime());
            userInfo.put("points", user.getPoints() != null ? user.getPoints() : 0);
            userInfo.put("promotionTime", user.getPromotionTime());
            userInfo.put("promoterId", user.getPromoterId());
            
            // 获取推广者信息
            if (user.getPromoterId() != null) {
                User promoter = userMapper.selectById(user.getPromoterId());
                if (promoter != null) {
                    userInfo.put("promoterNickname", promoter.getNickname());
                }
            }
            
            return Result.success(userInfo);
            
        } catch (Exception e) {
            log.error("获取推广用户信息失败", e);
            return Result.failure("获取用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 修改积分记录备注
     * POST /admin/marketing/point_record/remark/{id}
     */
    @PostMapping("/point_record/remark/{id}")
    public Result<String> updatePointRecordRemark(
            @PathVariable Integer id,
            @RequestBody Map<String, String> requestBody) {
        
        try {
            String mark = requestBody.get("mark");
            if (mark == null) {
                mark = "";
            }
            
            log.info("修改积分记录备注 - id: {}, mark: {}", id, mark);

            // 查询记录是否存在
            PointsRecord record = pointsRecordMapper.selectById(id);
            if (record == null) {
                return Result.failure("积分记录不存在");
            }
            
            // 更新备注
            record.setRemark(mark.trim());
            int updateResult = pointsRecordMapper.updateById(record);
            
            if (updateResult > 0) {
                return Result.success("备注修改成功");
            } else {
                return Result.failure("备注修改失败");
            }
            
        } catch (Exception e) {
            log.error("修改积分记录备注失败", e);
            return Result.failure("修改备注失败: " + e.getMessage());
        }
    }

    /**
     * 获取交易类型名称
     */
    private String getTypeName(String type, String source) {
        if (type == null) {
            return "未知类型";
        }
        
        switch (type) {
            case "earn":
                if ("order".equals(source)) {
                    return "消费获得";
                } else if ("comment".equals(source)) {
                    return "评价奖励";
                } else if ("manual".equals(source)) {
                    return "手动调整";
                } else {
                    return "获得积分";
                }
            case "use":
                if ("order".equals(source)) {
                    return "下单抵扣";
                } else {
                    return "消费积分";
                }
            case "refund":
                return "退回积分";
            default:
                return "其他";
        }
    }
}