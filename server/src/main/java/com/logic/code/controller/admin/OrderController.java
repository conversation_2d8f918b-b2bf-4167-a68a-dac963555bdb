package com.logic.code.controller.admin;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logic.code.common.enmus.WeshopWechatResultStatus;
import com.logic.code.common.exception.WeshopWechatException;
import com.logic.code.common.response.Result;
import com.logic.code.common.utils.JwtHelper;
import com.logic.code.entity.User;
import com.logic.code.entity.order.OrderExpress;
import com.logic.code.model.query.OrderQuery;
import com.logic.code.model.vo.ExpressCompanyVO;
import com.logic.code.model.vo.OrderDetailVO;
import com.logic.code.service.DeliveryOrderExportService;
import com.logic.code.service.ExpressCompanyService;
import com.logic.code.service.OrderExpressService;
import com.logic.code.service.OrderService;
import com.logic.code.service.UserService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/9 11:47
 * @desc 订单管理控制器
 */

@RestController
@RequestMapping("/adminapi/order")
@Slf4j
public class OrderController {

    @Resource
    OrderService orderService;

    @Resource
    UserService userService;

    @Resource
    OrderExpressService orderExpressService;

    @Resource
    ExpressCompanyService expressCompanyService;

    @Resource
    DeliveryOrderExportService deliveryOrderExportService;

    /**
     * 获取订单列表
     * @param orderQuery 查询条件
     * @return 订单列表
     */
    @RequestMapping("/list")
    public Result<Page> queryOrderList(OrderQuery orderQuery) {
        Page listVOS = orderService.getOrderList(orderQuery);
        return Result.success(listVOS);
    }

    /**
     * 获取订单详情
     * @param id 订单ID
     * @return 订单详情
     */
    @RequestMapping("/info/{id}")
    public Result<OrderDetailVO> getOrderDetail(@PathVariable("id") Integer id) {
        OrderDetailVO orderDetail = orderService.queryOrderDetail(id);
        return Result.success(orderDetail);
    }

    /**
     * 获取物流信息
     * @param id 订单ID
     * @return 物流信息
     */
    @RequestMapping("/express/{id}")
    public Result<String> getExpressInfo(@PathVariable("id") Integer id) {
        // 记录日志
        id = 148;
        System.out.println("Fetching logistics data for order ID: " + id);

        // 查询订单物流信息
        OrderExpress orderExpress = orderExpressService.queryByOrderId(id);

        if (orderExpress != null) {
            // 从OrderExpress中获取traces物流轨迹数据
            String tracesJson = orderExpress.getTraces();
            return Result.success(tracesJson);
        }


        return Result.failure("未获取到物流数据");
    }

    /**
     * 获取快递公司列表
     */
    @GetMapping("/kuaidi_coms")
    public Result<List<ExpressCompanyVO>> getExpressCompanyList() {
        try {
            // 从数据库获取启用的快递公司列表
            List<ExpressCompanyVO> expressList = expressCompanyService.getEnabledExpressCompanies();
            
            // 如果数据库中没有数据，返回默认列表
            if (expressList.isEmpty()) {
                expressList = getDefaultExpressCompanies();
                log.warn("数据库中没有快递公司数据，使用默认列表");
            }
            
            log.info("获取快递公司列表成功，共{}家", expressList.size());
            return Result.success(expressList);
        } catch (Exception e) {
            log.error("获取快递公司列表失败: {}", e.getMessage(), e);
            // 发生异常时返回默认列表，确保功能可用
            List<ExpressCompanyVO> defaultList = getDefaultExpressCompanies();
            log.warn("使用默认快递公司列表，共{}家", defaultList.size());
            return Result.success(defaultList);
        }
    }

    /**
     * 获取默认快递公司列表（备用方案）
     * @return 快递公司列表
     */
    private List<ExpressCompanyVO> getDefaultExpressCompanies() {
        List<ExpressCompanyVO> expressList = new ArrayList<>();
        
        // 添加常用快递公司
        expressList.add(ExpressCompanyVO.builder().value("顺丰速运").code("SF").id(1).sort(1).build());
        expressList.add(ExpressCompanyVO.builder().value("中通快递").code("ZTO").id(2).sort(2).build());
        expressList.add(ExpressCompanyVO.builder().value("圆通速递").code("YTO").id(3).sort(3).build());
        expressList.add(ExpressCompanyVO.builder().value("申通快递").code("STO").id(4).sort(4).build());
        expressList.add(ExpressCompanyVO.builder().value("韵达速递").code("YD").id(5).sort(5).build());
        expressList.add(ExpressCompanyVO.builder().value("百世快递").code("HTKY").id(6).sort(6).build());
        expressList.add(ExpressCompanyVO.builder().value("德邦快递").code("DBL").id(7).sort(7).build());
        expressList.add(ExpressCompanyVO.builder().value("京东快递").code("JD").id(8).sort(8).build());
        expressList.add(ExpressCompanyVO.builder().value("邮政快递包裹").code("YZPY").id(9).sort(9).build());
        expressList.add(ExpressCompanyVO.builder().value("EMS").code("EMS").id(10).sort(10).build());
        expressList.add(ExpressCompanyVO.builder().value("天天快递").code("HHTT").id(11).sort(11).build());
        expressList.add(ExpressCompanyVO.builder().value("宅急送").code("ZJS").id(12).sort(12).build());
        expressList.add(ExpressCompanyVO.builder().value("国通快递").code("GTO").id(13).sort(13).build());
        expressList.add(ExpressCompanyVO.builder().value("全峰快递").code("QFKD").id(14).sort(14).build());
        expressList.add(ExpressCompanyVO.builder().value("优速快递").code("UC").id(15).sort(15).build());
        expressList.add(ExpressCompanyVO.builder().value("中国快递服务").code("CCES").id(16).sort(16).build());
        expressList.add(ExpressCompanyVO.builder().value("安能快递").code("ANE").id(17).sort(17).build());
        expressList.add(ExpressCompanyVO.builder().value("快捷快递").code("FAST").id(18).sort(18).build());
        expressList.add(ExpressCompanyVO.builder().value("ADP国际快递").code("ADP").id(19).sort(19).build());
        expressList.add(ExpressCompanyVO.builder().value("DHL").code("DHL").id(20).sort(20).build());
        
        return expressList;
    }

    /**
     * 管理员发货
     */
    @PostMapping("/deliver")
    public Result<String> deliver(@RequestBody Map<String, Object> deliveryInfo) {
        // 检查管理员权限

        try {
            Integer orderId = (Integer) deliveryInfo.get("orderId");
            String expressType = (String) deliveryInfo.get("expressType");
            String expressCode = (String) deliveryInfo.get("expressCode");
            String trackingNumber = (String) deliveryInfo.get("trackingNumber");

            if (orderId == null || expressType == null || expressCode == null || trackingNumber == null) {
                throw new WeshopWechatException(WeshopWechatResultStatus.PARAM_ERROR);
            }

            // 调用订单服务的发货方法
            boolean success = orderService.deliverOrder(orderId, expressType, expressCode, trackingNumber);
            if (success) {
                log.info("管理员发货订单{}成功，快递公司：{}，快递单号：{}",  orderId, expressType, trackingNumber);
                return Result.success("发货成功");
            } else {
                return Result.failure("发货失败");
            }
        } catch (Exception e) {
            log.error("管理员发货失败: {}", e.getMessage(), e);
            throw new WeshopWechatException(WeshopWechatResultStatus.SYSTEM_ERROR);
        }
    }

    /**
     * 导出配货单
     * @param orderIds 订单ID列表（可选，多个用逗号分隔）
     * @return 导出结果
     */
    @GetMapping("/export/delivery")
    public Result<String> exportDeliveryOrders(@RequestParam(value = "orderIds", required = false) String orderIds) {
        try {
            List<Integer> orderIdList = null;
            if (orderIds != null && !orderIds.trim().isEmpty()) {
                orderIdList = Arrays.stream(orderIds.split(","))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            }

            String filePath = deliveryOrderExportService.exportDeliveryOrdersToExcel(orderIdList);
            if (filePath != null) {
                return Result.success(filePath);
            } else {
                return Result.failure("配货单导出失败");
            }
        } catch (Exception e) {
            log.error("配货单导出失败: {}", e.getMessage(), e);
            return Result.failure("配货单导出失败: " + e.getMessage());
        }
    }

    /**
     * 获取配货单预览数据
     * @param orderId 订单ID
     * @return 配货单预览数据
     */
    @GetMapping("/delivery/preview/{orderId}")
    public Result<Map<String, Object>> getDeliveryOrderPreview(@PathVariable("orderId") Integer orderId) {
        try {
            Map<String, Object> previewData = deliveryOrderExportService.getDeliveryOrderPreview(orderId);
            return Result.success(previewData);
        } catch (Exception e) {
            log.error("获取配货单预览数据失败: {}", e.getMessage(), e);
            return Result.failure("获取配货单预览数据失败: " + e.getMessage());
        }
    }

    /**
     * 下载配货单Excel文件
     * @param orderIds 订单ID列表（可选，多个用逗号分隔）
     * @return Excel文件下载响应
     */
    @GetMapping("/export/delivery/download")
    public ResponseEntity<org.springframework.core.io.Resource> downloadDeliveryOrders(@RequestParam(value = "orderIds", required = false) String orderIds) {
        try {
            List<Integer> orderIdList = null;
            if (orderIds != null && !orderIds.trim().isEmpty()) {
                orderIdList = Arrays.stream(orderIds.split(","))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            }

            byte[] excelBytes = deliveryOrderExportService.exportDeliveryOrdersToExcelBytes(orderIdList);
            if (excelBytes == null) {
                return ResponseEntity.badRequest().body(null);
            }

            // 生成文件名
            String date = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
            String fileName = "配货单导出_" + date + ".xlsx";

            ByteArrayResource resource = new ByteArrayResource(excelBytes);

            return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"")
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .contentLength(excelBytes.length)
                .body(resource);

        } catch (Exception e) {
            log.error("配货单下载失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(null);
        }
    }
}
