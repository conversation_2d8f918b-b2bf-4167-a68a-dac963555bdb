package com.logic.code.controller.admin;

import com.logic.code.common.response.Result;
import com.logic.code.service.PointsConsistencyScheduleService;
import com.logic.code.service.PointsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 积分数据一致性管理控制器
 * 提供积分数据检查、修复等管理功能
 * 
 * <AUTHOR>
 * @date 2025/1/3
 */
@RestController
@RequestMapping("/adminapi/points/consistency")
@Slf4j
public class PointsConsistencyController {
    
    @Autowired
    private PointsService pointsService;
    
    @Autowired
    private PointsConsistencyScheduleService consistencyScheduleService;
    
    /**
     * 检查积分数据一致性
     * GET /adminapi/points/consistency/check
     */
    @GetMapping("/check")
    public Result<Map<String, Object>> checkPointsConsistency() {
        try {
            log.info("管理员触发积分数据一致性检查");
            
            Map<String, Object> result = pointsService.validateUserPointsConsistency();
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("积分数据一致性检查失败", e);
            return Result.failure("检查失败：" + e.getMessage());
        }
    }
    
    /**
     * 修复积分数据不一致问题
     * POST /adminapi/points/consistency/fix
     */
    @PostMapping("/fix")
    public Result<Map<String, Object>> fixPointsInconsistency(@RequestParam(defaultValue = "false") boolean autoFix) {
        try {
            log.info("管理员触发积分数据修复，自动修复：{}", autoFix);
            
            Map<String, Object> result = pointsService.fixUserPointsInconsistency(autoFix);
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("积分数据修复失败", e);
            return Result.failure("修复失败：" + e.getMessage());
        }
    }
    
    /**
     * 手动触发积分一致性检查（支持自动修复）
     * POST /adminapi/points/consistency/manual-check
     */
    @PostMapping("/manual-check")
    public Result<Map<String, Object>> manualConsistencyCheck(@RequestParam(defaultValue = "false") boolean autoFix) {
        try {
            log.info("管理员手动触发积分一致性检查，自动修复：{}", autoFix);
            
            Map<String, Object> result = consistencyScheduleService.manualPointsConsistencyCheck(autoFix);
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("手动积分一致性检查失败", e);
            return Result.failure("检查失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取积分一致性检查配置
     * GET /adminapi/points/consistency/config
     */
    @GetMapping("/config")
    public Result<Map<String, Object>> getConsistencyConfig() {
        try {
            Map<String, Object> config = consistencyScheduleService.getConsistencyCheckConfig();
            return Result.success(config);
        } catch (Exception e) {
            log.error("获取积分一致性检查配置失败", e);
            return Result.failure("获取配置失败：" + e.getMessage());
        }
    }
    
    /**
     * 修复单个用户的积分数据
     * POST /adminapi/points/consistency/fix-user/{userId}
     */
    @PostMapping("/fix-user/{userId}")
    public Result<String> fixUserPoints(@PathVariable Integer userId) {
        try {
            log.info("管理员触发修复用户{}的积分数据", userId);
            
            // 更新指定用户的积分
            pointsService.updateUserPoints(userId);
            
            return Result.success("用户积分修复成功");
        } catch (Exception e) {
            log.error("修复用户{}积分失败", userId, e);
            return Result.failure("修复失败：" + e.getMessage());
        }
    }
    
    /**
     * 检查单个用户的积分一致性
     * GET /adminapi/points/consistency/check-user/{userId}
     */
    @GetMapping("/check-user/{userId}")
    public Result<Map<String, Object>> checkUserPointsConsistency(@PathVariable Integer userId) {
        try {
            log.info("管理员检查用户{}的积分一致性", userId);

            Map<String, Object> result = pointsService.checkUserPointsConsistency(userId);

            return Result.success(result);
        } catch (Exception e) {
            log.error("检查用户{}积分一致性失败", userId, e);
            return Result.failure("检查失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户的详细积分信息
     * GET /adminapi/points/consistency/user-info/{userId}
     */
    @GetMapping("/user-info/{userId}")
    public Result<Map<String, Object>> getUserPointsInfo(@PathVariable Integer userId) {
        try {
            log.info("管理员获取用户{}的详细积分信息", userId);

            Map<String, Object> result = pointsService.getUserPointsInfo(userId);

            return Result.success(result);
        } catch (Exception e) {
            log.error("获取用户{}积分信息失败", userId, e);
            return Result.failure("获取用户积分信息失败：" + e.getMessage());
        }
    }

    /**
     * 获取积分统计信息
     * GET /adminapi/points/consistency/stats
     */
    @GetMapping("/stats")
    public Result<Map<String, Object>> getPointsStats() {
        try {
            // 执行一致性检查获取统计信息
            Map<String, Object> consistencyResult = pointsService.validateUserPointsConsistency();

            // 可以在这里添加更多统计信息
            // 比如：总积分数、今日积分变化等

            return Result.success(consistencyResult);
        } catch (Exception e) {
            log.error("获取积分统计信息失败", e);
            return Result.failure("获取统计失败：" + e.getMessage());
        }
    }
}
