package com.logic.code.controller.admin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.logic.code.common.response.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 菜单数据导入控制器
 * 用于将login.json中的数据导入到数据库表中
 * 
 * <AUTHOR>
 * @date 2025/1/27
 */
@Slf4j
@RestController
@RequestMapping("/adminapi/menu")
public class MenuImportController {

    /**
     * 生成菜单导入SQL脚本
     */
    @RequestMapping("/generateImportSQL")
    public Result<String> generateImportSQL() {
        try {
            String sql = generateMenuImportSQL();
            return Result.success(sql);
        } catch (Exception e) {
            log.error("生成菜单导入SQL失败", e);
            return Result.failure("生成失败：" + e.getMessage());
        }
    }

    /**
     * 生成完整的菜单导入SQL脚本
     */
    private String generateMenuImportSQL() throws IOException {
        // 读取login.json文件
        ClassPathResource resource = new ClassPathResource("json/login.json");
        String jsonContent = new String(resource.getInputStream().readAllBytes(), StandardCharsets.UTF_8);
        
        // 解析JSON
        JSONObject loginData = JSON.parseObject(jsonContent);
        JSONArray menus = loginData.getJSONArray("menus");
        JSONArray uniqueAuth = loginData.getJSONArray("unique_auth");
        
        StringBuilder sql = new StringBuilder();
        sql.append("-- 从login.json完整导入的菜单数据和权限数据\n");
        sql.append("-- 生成时间: ").append(new Date()).append("\n\n");
        
        // 清空现有数据
        sql.append("-- 清空现有菜单和权限数据\n");
        sql.append("DELETE FROM weshop_menu_permission;\n");
        sql.append("DELETE FROM weshop_system_menu;\n\n");
        
        // 生成菜单数据SQL
        sql.append("-- 插入菜单数据\n");
        List<MenuInfo> allMenus = new ArrayList<>();
        extractMenusRecursively(menus, allMenus);
        
        // 按ID排序确保父菜单先插入
        allMenus.sort(Comparator.comparing(m -> m.id));
        
        sql.append(String.format("-- 共找到 %d 个菜单项\n", allMenus.size()));
        
        for (MenuInfo menu : allMenus) {
            sql.append(generateMenuInsertSQL(menu));
        }
        
        sql.append("\n-- 插入菜单权限数据\n");
        Set<String> processedPermissions = new HashSet<>();
        int permissionCount = 0;
        
        // 为每个菜单插入权限数据
        for (MenuInfo menu : allMenus) {
            if (menu.auth != null && !menu.auth.isEmpty()) {
                for (String permission : menu.auth) {
                    String key = menu.id + "_" + permission;
                    if (!processedPermissions.contains(key)) {
                        sql.append(generatePermissionInsertSQL(menu.id, permission));
                        processedPermissions.add(key);
                        permissionCount++;
                    }
                }
            }
        }
        
        // 插入unique_auth中的权限（如果菜单中没有的话）
        sql.append("\n-- 插入系统级权限数据（来自unique_auth）\n");
        int systemPermissionCount = 0;
        if (uniqueAuth != null) {
            for (int i = 0; i < uniqueAuth.size(); i++) {
                String permission = uniqueAuth.getString(i);
                if (!isPermissionInMenus(permission, allMenus)) {
                    // 为系统级权限创建一个特殊的菜单ID (9999)
                    sql.append(generatePermissionInsertSQL(9999, permission));
                    systemPermissionCount++;
                }
            }
        }
        
        sql.append("\n-- 导入统计信息\n");
        sql.append(String.format("-- 菜单项数量: %d\n", allMenus.size()));
        sql.append(String.format("-- 菜单权限数量: %d\n", permissionCount));
        sql.append(String.format("-- 系统权限数量: %d\n", systemPermissionCount));
        sql.append(String.format("-- 总权限数量: %d\n", permissionCount + systemPermissionCount));
        
        return sql.toString();
    }

    /**
     * 递归提取菜单数据
     */
    private void extractMenusRecursively(JSONArray menuArray, List<MenuInfo> result) {
        if (menuArray == null) return;
        
        for (int i = 0; i < menuArray.size(); i++) {
            JSONObject menuJson = menuArray.getJSONObject(i);
            MenuInfo menu = parseMenuData(menuJson, i);
            result.add(menu);
            
            // 递归处理子菜单
            JSONArray children = menuJson.getJSONArray("children");
            if (children != null && children.size() > 0) {
                extractMenusRecursively(children, result);
            }
        }
    }

    /**
     * 解析单个菜单数据
     */
    private MenuInfo parseMenuData(JSONObject menuJson, int defaultSortOrder) {
        MenuInfo menu = new MenuInfo();
        menu.id = menuJson.getInteger("id");
        menu.pid = menuJson.getInteger("pid");
        menu.path = menuJson.getString("path");
        menu.title = menuJson.getString("title");
        menu.icon = menuJson.getString("icon");
        menu.header = menuJson.getString("header");
        menu.isHeader = menuJson.getInteger("is_header");
        menu.isShow = menuJson.getInteger("is_show");
        menu.sortOrder = defaultSortOrder;
        
        // 解析权限数组
        JSONArray authArray = menuJson.getJSONArray("auth");
        if (authArray != null) {
            menu.auth = new ArrayList<>();
            for (int i = 0; i < authArray.size(); i++) {
                menu.auth.add(authArray.getString(i));
            }
        }
        
        return menu;
    }

    /**
     * 生成菜单插入SQL
     */
    private String generateMenuInsertSQL(MenuInfo menu) {
        return String.format(
            "INSERT INTO weshop_system_menu (id, pid, path, title, icon, header, is_header, is_show, sort_order, create_time, update_time) " +
            "VALUES (%d, %d, '%s', '%s', '%s', '%s', %d, %d, %d, NOW(), NOW());\n",
            menu.id,
            menu.pid,
            escapeSqlString(menu.path),
            escapeSqlString(menu.title),
            escapeSqlString(menu.icon != null ? menu.icon : ""),
            escapeSqlString(menu.header != null ? menu.header : ""),
            menu.isHeader != null ? menu.isHeader : 0,
            menu.isShow != null ? menu.isShow : 1,
            menu.sortOrder
        );
    }

    /**
     * 生成权限插入SQL
     */
    private String generatePermissionInsertSQL(Integer menuId, String permission) {
        return String.format(
            "INSERT INTO weshop_menu_permission (menu_id, permission, create_time) " +
            "VALUES (%d, '%s', NOW());\n",
            menuId,
            escapeSqlString(permission)
        );
    }

    /**
     * 检查权限是否已经在菜单中定义
     */
    private boolean isPermissionInMenus(String permission, List<MenuInfo> menus) {
        for (MenuInfo menu : menus) {
            if (menu.auth != null && menu.auth.contains(permission)) {
                return true;
            }
        }
        return false;
    }

    /**
     * SQL字符串转义
     */
    private String escapeSqlString(String str) {
        if (str == null) return "";
        return str.replace("'", "''")
                 .replace("\\", "\\\\")
                 .replace("\n", "\\n")
                 .replace("\r", "\\r");
    }

    /**
     * 菜单信息内部类
     */
    private static class MenuInfo {
        Integer id;
        Integer pid;
        String path;
        String title;
        String icon;
        String header;
        Integer isHeader;
        Integer isShow;
        Integer sortOrder;
        List<String> auth;
    }
}