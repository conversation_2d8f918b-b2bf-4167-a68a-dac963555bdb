package com.logic.code.controller.app;

import com.logic.code.common.response.Result;
import com.logic.code.common.utils.JwtHelper;
import com.logic.code.entity.Footprint;
import com.logic.code.entity.User;
import com.logic.code.model.dto.GoodsFootprintDTO;
import com.logic.code.service.FootprintService;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/wechat/footprint")
@Validated
public class WechatFootprintController {


    @Resource
    private FootprintService footprintService;

    @GetMapping("/list")
    public Result<List<List<GoodsFootprintDTO>>> queryGoodsFootprintList() {
        return Result.success(footprintService.queryGoodsFootprintTimeLine());
    }

    @PostMapping("/delete")
    public Result deleteGoodsFootprint(@NotNull Integer goodsId) {
        User userInfo = JwtHelper.getUserInfo();
        return Result.success(footprintService.delete(new Footprint().setGoodsId(goodsId).setUserId(userInfo.getId())));
    }

}
