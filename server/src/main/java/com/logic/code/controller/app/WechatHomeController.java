package com.logic.code.controller.app;

import com.logic.code.common.response.Result;
import com.logic.code.model.vo.HomeIndexVO;
import com.logic.code.service.HomeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/wechat/home")
public class WechatHomeController {

    @Autowired
    private HomeService homeService;


    @GetMapping("/index")
    public Result<HomeIndexVO> index() {
        return Result.success(homeService.index());
    }

}
