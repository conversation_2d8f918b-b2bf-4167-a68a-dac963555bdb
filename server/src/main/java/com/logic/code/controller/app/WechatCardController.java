package com.logic.code.controller.app;

import com.logic.code.common.response.Result;
import com.logic.code.entity.Card;
import com.logic.code.entity.GoodsCardConfig;
import com.logic.code.service.CardService;
import com.logic.code.service.GoodsCardConfigService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/3 11:48
 * @desc
 */
@RestController
@RequestMapping("/wechat/card")
public class WechatCardController {

    @Resource
    private CardService cardService;

    @Resource
    private GoodsCardConfigService goodsCardConfigService;

    @RequestMapping("save")
    public Result<Card> save(@RequestBody Card card) {
        Card save = cardService.save(card);
        if (save == null) {
            return Result.failure("验证失败");
        }
        return Result.success(save);
    }

    @RequestMapping("list")
    public Result<List<Card>> list() {
        return Result.success(cardService.list());
    }


    @RequestMapping("listGoodsCardConfig")
    public Result<List<GoodsCardConfig>> listGoodsCardConfig() {
        return Result.success(goodsCardConfigService.list());
    }

}
