package com.logic.code.controller.app;

import com.logic.code.common.response.Result;
import com.logic.code.entity.Region;
import com.logic.code.service.RegionService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/wechat/region")
public class WechatRegionController {

    @Resource
    private RegionService regionService;

    @GetMapping("/list")
    public Result<List<Region>> queryList(Short parentId) {
        return Result.success(regionService.queryList(new Region().setParentId(parentId)));
    }

}
