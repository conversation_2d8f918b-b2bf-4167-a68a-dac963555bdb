package com.logic.code.controller.admin;

import com.logic.code.common.PageParamRequest;
import com.logic.code.common.response.Result;
import com.logic.code.model.income.IncomeDetailsRequest;
import com.logic.code.model.income.IncomeDetailsResponse;
import com.logic.code.model.income.IncomeStatisticsResponse;
import com.logic.code.service.IncomeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * Income management controller
 */
@Slf4j
@RestController
@RequestMapping("/adminapi/finance/income")
public class IncomeController {

    @Autowired
    private IncomeService incomeService;

    /**
     * Get user income statistics
     */
    @RequestMapping("/statistics")
    public Result<IncomeStatisticsResponse> getUserIncomeStatistics(@RequestParam Integer userId) {
        try {
            IncomeStatisticsResponse response = incomeService.getUserIncomeStatistics(userId);
            return Result.success(response);
        } catch (Exception e) {
            log.error("Failed to get user income statistics for userId: {}", userId, e);
            return Result.failure("Failed to get income statistics: " + e.getMessage());
        }
    }

    /**
     * Get user income details with pagination
     */
    @RequestMapping("/details")
    public Result<IncomeDetailsResponse> getUserIncomeDetails(
           IncomeDetailsRequest request,
            PageParamRequest pageParamRequest) {
        try {
            IncomeDetailsResponse response = incomeService.getUserIncomeDetails(request, pageParamRequest);
            return Result.success(response);
        } catch (Exception e) {
            log.error("Failed to get user income details for request: {}", request, e);
            return Result.failure("Failed to get income details: " + e.getMessage());
        }
    }

    /**
     * Export user income details
     */
    @RequestMapping("/export")
    public Result<String> exportUserIncomeDetails(@RequestBody IncomeDetailsRequest request) {
        try {
            String exportUrl = incomeService.exportUserIncomeDetails(request);
            return Result.success(exportUrl);
        } catch (Exception e) {
            log.error("Failed to export user income details for request: {}", request, e);
            return Result.failure("Failed to export income details: " + e.getMessage());
        }
    }
}
