package com.logic.code.controller.admin;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logic.code.entity.RechargeRecord;
import com.logic.code.entity.User;
import com.logic.code.mapper.RechargeRecordMapper;
import com.logic.code.mapper.UserMapper;
import com.logic.code.common.response.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 充值记录管理控制器
 */
@RestController
@RequestMapping("/adminapi/finance/recharge")
@Slf4j
public class RechargeRecordController {

    @Autowired
    private RechargeRecordMapper rechargeRecordMapper;

    @Autowired
    private UserMapper userMapper;

    /**
     * 充值记录列表
     * @param paid 支付状态
     * @param time 时间范围
     * @param nickname 用户昵称
     * @param page 页码
     * @param limit 每页数量
     * @return 充值记录列表
     */
    @GetMapping("")
    public Result<?> getRechargeList(
            @RequestParam(value = "paid", required = false) String paid,
            @RequestParam(value = "time", required = false) String time,
            @RequestParam(value = "nickname", required = false) String nickname,
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "limit", defaultValue = "20") Integer limit) {

        try {
            // 创建分页对象
            Page<RechargeRecord> pageObj = new Page<>(page, limit);
            
            // 构建查询条件
            QueryWrapper<RechargeRecord> queryWrapper = new QueryWrapper<>();
            
            // 支付状态筛选
            if (paid != null && !paid.trim().isEmpty()) {
                if ("1".equals(paid)) {
                    // 已支付
                    queryWrapper.eq("status", 1);
                } else if ("0".equals(paid)) {
                    // 未支付
                    queryWrapper.eq("status", 0);
                }
            }
            
            // 时间范围筛选
            if (time != null && !time.trim().isEmpty()) {
                String[] timeArray = time.split("-");
                if (timeArray.length == 2) {
                    try {
                        LocalDateTime startTime = LocalDateTime.parse(timeArray[0] + " 00:00:00", DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss"));
                        LocalDateTime endTime = LocalDateTime.parse(timeArray[1] + " 23:59:59", DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss"));
                        queryWrapper.between("create_time", startTime, endTime);
                    } catch (Exception e) {
                        log.warn("时间格式解析失败: {}", time);
                    }
                }
            }
            
            // 用户昵称搜索
            if (nickname != null && !nickname.trim().isEmpty()) {
                List<User> users = userMapper.selectList(
                    new QueryWrapper<User>().like("nickname", nickname)
                                            .or().like("username", nickname)
                );
                if (!users.isEmpty()) {
                    List<Integer> userIds = users.stream().map(User::getId).collect(Collectors.toList());
                    queryWrapper.in("user_id", userIds);
                } else {
                    // 如果没有找到匹配的用户，返回空结果
                    Map<String, Object> result = new HashMap<>();
                    result.put("list", new ArrayList<>());
                    result.put("count", 0);
                    return Result.success(result);
                }
            }
            
            // 按创建时间倒序排列
            queryWrapper.orderByDesc("create_time");
            
            // 执行分页查询
            IPage<RechargeRecord> pageResult = rechargeRecordMapper.selectPage(pageObj, queryWrapper);
            
            // 构建返回数据
            List<Map<String, Object>> list = new ArrayList<>();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            
            for (RechargeRecord record : pageResult.getRecords()) {
                Map<String, Object> item = new HashMap<>();
                item.put("id", record.getId());
                
                // 获取用户信息
                User user = userMapper.selectById(record.getUserId());
                item.put("avatar", user != null ? user.getAvatar() : "");
                item.put("nickname", user != null ? user.getNickname() : "未知用户");
                
                // 订单号
                item.put("order_id", "RC" + String.format("%08d", record.getId()));
                
                // 支付金额
                item.put("price", record.getAmount() != null ? record.getAmount().toString() : "0");
                
                // 支付状态
                String paidType = "";
                if (record.getStatus() != null) {
                    switch (record.getStatus()) {
                        case 0:
                            paidType = "未支付";
                            break;
                        case 1:
                            paidType = "已支付";
                            break;
                        case 2:
                            paidType = "已取消";
                            break;
                        default:
                            paidType = "未知";
                    }
                }
                item.put("paid_type", paidType);
                item.put("paid", record.getStatus() != null && record.getStatus() == 1);
                
                // 充值类型（可以根据业务需求扩展）
                item.put("_recharge_type", "余额充值");
                
                // 支付时间
                item.put("_pay_time", record.getPayTime() != null ? sdf.format(record.getPayTime()) : "");
                
                // 退款金额（如果有的话）
                item.put("refund_price", "0");
                
                // 充值类型（可以扩展）
                item.put("recharge_type", "user");
                
                list.add(item);
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("list", list);
            result.put("count", pageResult.getTotal());
            
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("获取充值记录列表失败", e);
            return Result.failure("获取充值记录列表失败：" + e.getMessage());
        }
    }

    /**
     * 用户充值统计数据
     * @param time 时间范围
     * @param paid 支付状态
     * @param nickname 用户昵称
     * @return 统计数据
     */
    @GetMapping("/user_recharge")
    public Result<?> getUserRechargeStats(
            @RequestParam(value = "data", required = false) String time,
            @RequestParam(value = "paid", required = false) String paid,
            @RequestParam(value = "nickname", required = false) String nickname) {

        try {
            // 构建查询条件
            QueryWrapper<RechargeRecord> queryWrapper = new QueryWrapper<>();
            
            // 时间范围筛选
            if (time != null && !time.trim().isEmpty()) {
                String[] timeArray = time.split("-");
                if (timeArray.length == 2) {
                    try {
                        LocalDateTime startTime = LocalDateTime.parse(timeArray[0] + " 00:00:00", DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss"));
                        LocalDateTime endTime = LocalDateTime.parse(timeArray[1] + " 23:59:59", DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss"));
                        queryWrapper.between("create_time", startTime, endTime);
                    } catch (Exception e) {
                        log.warn("时间格式解析失败: {}", time);
                    }
                }
            }
            
            // 用户昵称搜索
            if (nickname != null && !nickname.trim().isEmpty()) {
                List<User> users = userMapper.selectList(
                    new QueryWrapper<User>().like("nickname", nickname)
                                            .or().like("username", nickname)
                );
                if (!users.isEmpty()) {
                    List<Integer> userIds = users.stream().map(User::getId).collect(Collectors.toList());
                    queryWrapper.in("user_id", userIds);
                } else {
                    // 如果没有找到匹配的用户，返回空统计
                    List<Map<String, Object>> stats = new ArrayList<>();
                    Map<String, Object> stat1 = new HashMap<>();
                    stat1.put("name", "充值人数");
                    stat1.put("value", 0);
                    stat1.put("class", "text-warning");
                    stats.add(stat1);
                    
                    Map<String, Object> stat2 = new HashMap<>();
                    stat2.put("name", "充值金额");
                    stat2.put("value", "0.00");
                    stat2.put("class", "text-success");
                    stats.add(stat2);
                    
                    Map<String, Object> stat3 = new HashMap<>();
                    stat3.put("name", "退款金额");
                    stat3.put("value", "0.00");
                    stat3.put("class", "text-info");
                    stats.add(stat3);
                    
                    return Result.success(stats);
                }
            }
            
            // 查询符合条件的充值记录
            List<RechargeRecord> records = rechargeRecordMapper.selectList(queryWrapper);
            
            // 统计数据
            // 充值人数：统计在指定时间范围内有充值记录的用户数量（不区分支付状态）
            long rechargeUserCount = records.stream()
                    .map(RechargeRecord::getUserId)
                    .distinct()
                    .count();
            
            // 充值金额：统计在指定时间范围内所有充值记录的金额总和（不区分支付状态）
            BigDecimal totalRechargeAmount = records.stream()
                    .map(RechargeRecord::getAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            // 已支付金额：统计在指定时间范围内已支付充值记录的金额总和
            BigDecimal paidRechargeAmount = records.stream()
                    .filter(r -> r.getStatus() != null && r.getStatus() == 1) // 只统计已支付的
                    .map(RechargeRecord::getAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            // 退款金额（暂定为0，可以根据业务需求扩展）
            BigDecimal totalRefundAmount = BigDecimal.ZERO;
            
            // 构建返回数据
            List<Map<String, Object>> stats = new ArrayList<>();
            Map<String, Object> stat1 = new HashMap<>();
            stat1.put("name", "充值人数");
            stat1.put("value", rechargeUserCount);
            stat1.put("class", "text-warning");
            stats.add(stat1);
            
            Map<String, Object> stat2 = new HashMap<>();
            stat2.put("name", "充值金额");
            stat2.put("value", paidRechargeAmount.toString()); // 显示已支付的金额
            stat2.put("class", "text-success");
            stats.add(stat2);
            
            Map<String, Object> stat3 = new HashMap<>();
            stat3.put("name", "退款金额");
            stat3.put("value", totalRefundAmount.toString());
            stat3.put("class", "text-info");
            stats.add(stat3);
            
            return Result.success(stats);
            
        } catch (Exception e) {
            log.error("获取用户充值统计数据失败", e);
            return Result.failure("获取用户充值统计数据失败：" + e.getMessage());
        }
    }

    /**
     * 退款表单数据
     * @param id 充值记录ID
     * @return 退款表单数据
     */
    @GetMapping("/{id}/refund_edit")
    public Result<?> getRefundEditForm(@PathVariable Integer id) {
        try {
            RechargeRecord record = rechargeRecordMapper.selectById(id);
            if (record == null) {
                return Result.failure("充值记录不存在");
            }
            
            // 检查是否已支付
            if (record.getStatus() == null || record.getStatus() != 1) {
                return Result.failure("只有已支付的充值记录才能退款");
            }
            
            // 获取用户信息
            User user = userMapper.selectById(record.getUserId());
            
            Map<String, Object> result = new HashMap<>();
            result.put("id", record.getId());
            result.put("order_id", "RC" + String.format("%08d", record.getId()));
            result.put("nickname", user != null ? user.getNickname() : "未知用户");
            result.put("price", record.getAmount() != null ? record.getAmount().toString() : "0");
            result.put("refund_price", "0"); // 可退款金额
            
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("获取退款表单数据失败", e);
            return Result.failure("获取退款表单数据失败：" + e.getMessage());
        }
    }
}