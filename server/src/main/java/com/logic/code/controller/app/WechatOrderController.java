package com.logic.code.controller.app;

import com.logic.code.common.response.Result;
import com.logic.code.entity.order.Order;
import com.logic.code.entity.order.OrderExpress;
import com.logic.code.model.query.OrderQuery;
import com.logic.code.model.vo.OrderDetailVO;
import com.logic.code.model.vo.OrderListVO;
import com.logic.code.model.vo.OrderSubmitParamVO;
import com.logic.code.model.vo.OrderSubmitResultVO;
import com.logic.code.service.OrderExpressService;
import com.logic.code.service.OrderService;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/wechat/order")
@Validated
public class WechatOrderController {

    @Resource
    private OrderService orderService;

    @Autowired
    private OrderExpressService orderExpressService;

    @GetMapping({"/list"})
    public Result<List<OrderListVO>> queryOrderList(OrderQuery orderQuery) {
        return Result.success(orderService.queryOrderList(orderQuery));
    }

    @GetMapping("/detail")
    public Result<OrderDetailVO> queryOrderDetail(@NotNull Integer orderId) {
        return Result.success(orderService.queryOrderDetail(orderId));
    }

    /**
     * @return
     */
    @PostMapping("/submit")
    public Result<OrderSubmitResultVO> submitOrder(@Validated @RequestBody OrderSubmitParamVO orderSubmitParamDTO) {
        return Result.success(orderService.submitOrder(orderSubmitParamDTO));
    }


    @PostMapping("/submitCardOrder")
    public Result<OrderSubmitResultVO> submitCardOrder(@Validated @RequestBody OrderSubmitParamVO orderSubmitParamDTO) {
        return Result.success(orderService.submitCardOrder(orderSubmitParamDTO));
    }


    /**
     * 获取最新的订单物流信息
     *
     * @param orderId
     * @return
     */
    @GetMapping("/express")
    public Result<OrderExpress> queryLatestExpressInfo(@NotNull Integer orderId) {
        return Result.success(orderExpressService.queryOne(OrderExpress.builder().orderId(orderId).build()));
    }

    @RequestMapping("queryByCardId")
    public Result<Order> queryByCardId(@NotNull Integer cardId) {
        return Result.success(orderService.queryByCardId(cardId));
    }

    @RequestMapping("/cancel")
    public Result<Boolean> cancel(@NotNull Integer orderId) {
        return Result.success(orderService.cancel(orderId));
    }
    @RequestMapping("/confirm")
    public Result<Boolean> confirm(@NotNull Integer orderId) {
        return Result.success(orderService.confirm(orderId));
    }

}
