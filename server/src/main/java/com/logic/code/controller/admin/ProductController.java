package com.logic.code.controller.admin;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.logic.code.common.response.Result;
import com.logic.code.common.utils.StringUtils;
import com.logic.code.entity.goods.*;
import com.logic.code.model.dto.GoodsImportResultDTO;
import com.logic.code.model.query.OrderQuery;
import com.logic.code.model.query.admin.CategoryParam;
import com.logic.code.model.query.admin.GoodsParam;
import com.logic.code.model.vo.AttributeWithCategoryVO;
import com.logic.code.model.vo.CategoryCascaderVO;
import com.logic.code.model.vo.CategoryVO;
import com.logic.code.model.vo.GoodsAttributeVO;
import com.logic.code.model.vo.admin.GoodsVO;
import com.logic.code.service.*;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.HashMap;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/9 17:30
 * @desc
 */
@RestController
@RequestMapping("/adminapi/product")
@Slf4j
public class ProductController {

    @Resource
    private CategoryService categoryService;

    @Resource
    private GoodsService goodsService;

    @Resource
    private ProductService productService;

    @Resource
    private GoodsGalleryService goodsGalleryService;

    @Resource
    private GoodsSpecificationService goodsSpecificationService;

    @Resource
    private GoodsAttributeService goodsAttributeService;

    @Resource
    private AttributeService attributeService;

    @Resource
    private AttributeCategoryService attributeCategoryService;


    @Resource
    private SpecificationService specificationService;

    @Resource
    private SpecificationTemplateService specificationTemplateService;

    @Resource
    private ProductLabelCategoryService productLabelCategoryService;

    @Resource
    private ProductLabelService productLabelService;

    @Resource
    private GoodsLabelService goodsLabelService;

    @RequestMapping("/category")
    public Result<List<CategoryVO>> queryCategoryList(CategoryParam param) {
        return Result.success(categoryService.queryCategoryList(param));
    }

    @RequestMapping("/category/tree/{type}")
    public Result<List<CategoryCascaderVO>> tree(@PathVariable("type") Integer type) {
        return Result.success(categoryService.buildCascaderTree(type));
    }

    @RequestMapping("/category/create")
    public Result<Category> createCategory() {
        // Return an empty category for the form
        Category category = new Category();
        category.setIsShow(true);
        category.setShowIndex(false);
        category.setParentId(0);
        category.setSortOrder(0);
        return Result.success(category);
    }

    @RequestMapping("/category/{id}")
    public Result<Category> getCategoryById(@PathVariable("id") Integer id) {
        Category category = categoryService.queryById(id);
        return Result.success(category);
    }

    @RequestMapping(value = "/category/update")
    public Result<Boolean> updateCategory(@RequestBody Category category) {
        return Result.success(categoryService.updateById(category) == 1);
    }

    @RequestMapping(value = "/category/save")
    public Result<Category> saveCategory(@RequestBody Category category) {
        if(StringUtils.isBlank(category.getImgUrl())){
            category.setImgUrl(category.getIconUrl());
        }
        categoryService.insert(category);
        return Result.success(category);
    }

    @RequestMapping("/category/{id}/delete")
    public Result<Boolean> deleteCategory(@PathVariable("id") Integer id) {
        return Result.success(categoryService.deleteById(id) == 1);
    }

    @RequestMapping("/product")
    public Result<Page> getGoodsPage(GoodsParam param) {
        Page listVOS = goodsService.getGoodsPage(param);
        return Result.success(listVOS);
    }

    /**
     * 获取商品详情
     */
    @GetMapping("/product/{id}")
    public Result<Map<String, Object>> productInfo(@PathVariable("id") Integer id) {
        Map<String, Object> result = new HashMap<>();

        if (id != null && id > 0) {
            Goods goods = goodsService.queryById(id);
            if (goods != null) {
                // Convert goods to map to add displaySystems field
                Map<String, Object> productInfo = convertGoodsToMap(goods);
                result.put("productInfo", productInfo);

                // Get product specifications, attributes, etc.
                List<Product> products = productService.queryByGoodsId(id);
                if (products != null && !products.isEmpty()) {
                    // Convert products to include specifications
                    List<Map<String, Object>> productsWithSpecs = products.stream()
                            .map(this::convertProductToMapWithSpecifications)
                            .collect(Collectors.toList());
                    result.put("products", productsWithSpecs);
                }

                // Get goods gallery (slider images)
                List<GoodsGallery> goodsGallery = goodsGalleryService.queryByGoodsId(id);
                if (goodsGallery != null && !goodsGallery.isEmpty()) {
                    result.put("goodsGallery", goodsGallery);
                }

                // Get specifications for frontend attrs
                List<Map<String, Object>> specifications = getSpecificationsForFrontend(id);
                // Always include specifications field, even if empty
                result.put("specifications", specifications != null ? specifications : new ArrayList<>());

                // Get goods attributes and convert to expected format
                List<GoodsAttribute> goodsAttributes = goodsAttributeService.queryByGoodsId(id);
                if (goodsAttributes != null && !goodsAttributes.isEmpty()) {
                    // Convert to the format expected by frontend
                    List<GoodsAttributeVO> attributes = goodsAttributes.stream()
                            .map(attr -> {
                                // Get attribute name from weshop_attribute table
                                Attribute attribute = attributeService.queryById(attr.getAttributeId());
                                String attributeName = attribute != null ? attribute.getName() : "未知属性";
                                return new GoodsAttributeVO(attr.getAttributeId(), attributeName, attr.getValue());
                            })
                            .collect(Collectors.toList());
                    result.put("attributes", attributes);
                }
            }
        }
        log.info("result={}", JSONObject.toJSONString(result));
        return Result.success(result);
    }

    /**
     * 修复商品中文字符编码问题
     */
    @PostMapping("/fix-encoding/{id}")
    public Result<Boolean> fixProductEncoding(@PathVariable("id") Integer id, @RequestBody Map<String, String> fixData) {
        try {
            Goods goods = goodsService.queryById(id);
            if (goods != null) {
                // 修复商品名称
                if (fixData.containsKey("name") && StringUtils.isNotEmpty(fixData.get("name"))) {
                    goods.setName(fixData.get("name"));
                }

                // 修复关键词
                if (fixData.containsKey("keywords") && StringUtils.isNotEmpty(fixData.get("keywords"))) {
                    goods.setKeywords(fixData.get("keywords"));
                }

                // 修复商品简介
                if (fixData.containsKey("goodsBrief") && StringUtils.isNotEmpty(fixData.get("goodsBrief"))) {
                    goods.setGoodsBrief(fixData.get("goodsBrief"));
                }

                // 修复商品描述
                if (fixData.containsKey("goodsDesc") && StringUtils.isNotEmpty(fixData.get("goodsDesc"))) {
                    goods.setGoodsDesc(fixData.get("goodsDesc"));
                }

                // 修复商品单位
                if (fixData.containsKey("goodsUnit") && StringUtils.isNotEmpty(fixData.get("goodsUnit"))) {
                    goods.setGoodsUnit(fixData.get("goodsUnit"));
                }

                // 保存修复后的数据
                goodsService.updateById(goods);
                return Result.success(true);
            }
            return Result.failure("商品不存在");
        } catch (Exception e) {
            return Result.failure("修复失败: " + e.getMessage());
        }
    }

    /**
     * 添加或更新商品
     */
    @PostMapping("/product/{id}")
    public Result<Goods> saveProduct(@PathVariable("id") Integer id, @RequestBody GoodsVO goodsVO) {
        Goods goods;

        if (id != null && id > 0) {
            // Update existing product
            goods = goodsService.updateGoods(goodsVO);
        } else {
            // Create new product
            goods = goodsService.createGoods(goodsVO);
        }

        return Result.success(goods);
    }

    /**
     * 获取商品缓存
     */
    @RequestMapping("/product/cache")
    public Result<Map<String, Object>> productCache() {
        Map<String, Object> cache = goodsService.getProductCache();
        return Result.success(cache);
    }

    /**
     * 删除商品缓存
     */
    @RequestMapping("/product/cache/delete")
    public Result<Boolean> cacheDelete() {
        goodsService.clearProductCache();
        return Result.success(true);
    }

    /**
     * 获取规格属性模板
     */
    @RequestMapping("/product/get_rule")
    public Result<List<Map<String, Object>>> productGetRule() {
        List<Map<String, Object>> rules = goodsService.getProductRules();
        return Result.success(rules);
    }

    /**
     * 获取运费模板
     */
    @RequestMapping("/product/get_template")
    public Result<List<Map<String, Object>>> productGetTemplate() {
        List<Map<String, Object>> templates = goodsService.getFreightTemplates();
        return Result.success(templates);
    }

    /**
     * 检查商品是否可以修改类型
     */
    @RequestMapping("/product/check_activity/{id}")
    public Result<Boolean> checkActivity(@PathVariable("id") Integer id) {
        boolean canChange = goodsService.checkProductActivity(id);
        return Result.success(canChange);
    }

    /**
     * 移到回收站
     */
    @DeleteMapping("/product/{id}")
    public Result<Boolean> moveToRecycleBin(@PathVariable("id") Integer id) {
        boolean success = goodsService.moveToRecycleBin(id);
        return Result.success(success);
    }

    /**
     * 彻底删除商品
     */
    @DeleteMapping("/full_del/{id}")
    public Result<Boolean> permanentDelete(@PathVariable("id") Integer id) {
        boolean success = goodsService.permanentDelete(id);
        return Result.success(success);
    }

    /**
     * 从回收站恢复
     */
    @PutMapping("/product/{id}/restore")
    public Result<Boolean> restoreFromRecycleBin(@PathVariable("id") Integer id) {
        boolean success = goodsService.restoreFromRecycleBin(id);
        return Result.success(success);
    }

    /**
     * 获取商品状态头部统计
     */
    @GetMapping("/product/type_header")
    public Result<Map<String, Object>> getProductTypeHeader(GoodsParam param) {
        Map<String, Object> header = goodsService.getProductTypeHeader(param);
        return Result.success(header);
    }

    // ==================== 商品参数相关接口 ====================

    /**
     * 获取商品参数列表（联合查询weshop_attribute和weshop_attribute_category表）
     */
    @GetMapping("/param/list")
    public Result<Map<String, Object>> getParamList(
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "categoryId", required = false) Integer categoryId,
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "limit", defaultValue = "10") Integer limit) {

        // 使用联合查询获取属性和属性分类信息
        List<AttributeWithCategoryVO> attributeList = attributeService.getAttributesWithCategory(name, categoryId, page, limit);
        Long totalCount = attributeService.countAttributesWithCategory(name, categoryId);

        // 解析JSON字符串为数组对象
        ObjectMapper objectMapper = new ObjectMapper();
        List<Map<String, Object>> processedList = attributeList.stream().map(attr -> {
            Map<String, Object> item = new HashMap<>();
            item.put("id", attr.getId());
            item.put("attributeCategoryId", attr.getAttributeCategoryId());
            item.put("name", attr.getName());
            item.put("inputType", attr.getInputType());
            item.put("sortOrder", attr.getSortOrder());
            item.put("categoryName", attr.getCategoryName());
            item.put("categoryEnabled", attr.getCategoryEnabled());

            // 解析values字段的JSON字符串
            try {
                if (attr.getValues() != null && !attr.getValues().trim().isEmpty()) {
                    Object valuesArray = objectMapper.readValue(attr.getValues(), Object.class);
                    item.put("values", valuesArray);
                } else {
                    item.put("values", new ArrayList<>());
                }
            } catch (Exception e) {
                // 如果解析失败，返回空数组
                item.put("values", new ArrayList<>());
            }

            return item;
        }).collect(Collectors.toList());

        Map<String, Object> result = new HashMap<>();
        result.put("list", processedList);
        result.put("count", totalCount);
        result.put("page", page);
        result.put("limit", limit);

        return Result.success(result);
    }

    /**
     * 获取属性分类列表
     */
    @GetMapping("/param/categories")
    public Result<List<AttributeCategory>> getAttributeCategories() {
        List<AttributeCategory> categories = attributeCategoryService.queryAllEnabled();
        // 为每个分类添加参数数量统计
        for (AttributeCategory category : categories) {
            Long count = attributeService.countByCategoryId(category.getId());
            category.setAttributeCount(count != null ? count.intValue() : 0);
        }
        return Result.success(categories);
    }

    /**
     * 添加参数分类
     */
    @PostMapping("/param/categories")
    public Result<AttributeCategory> addParamCategory(@RequestBody AttributeCategory category) {
        try {
            if (category.getName() == null || category.getName().trim().isEmpty()) {
                return Result.failure("分类名称不能为空");
            }

            // 检查分类名称是否已存在
            AttributeCategory existing = attributeCategoryService.queryByName(category.getName().trim());
            if (existing != null) {
                return Result.failure("分类名称已存在");
            }

            category.setName(category.getName().trim());
            if (category.getEnabled() == null) {
                category.setEnabled(true);
            }

            AttributeCategory saved = attributeCategoryService.save(category);
            return Result.success(saved);
        } catch (Exception e) {
            log.error("添加参数分类失败", e);
            return Result.failure("添加参数分类失败：" + e.getMessage());
        }
    }

    /**
     * 编辑参数分类
     */
    @PutMapping("/param/categories/{id}")
    public Result<AttributeCategory> editParamCategory(
            @PathVariable("id") Integer id,
            @RequestBody AttributeCategory category) {
        try {
            AttributeCategory existing = attributeCategoryService.queryById(id);
            if (existing == null) {
                return Result.failure("分类不存在");
            }

            if (category.getName() == null || category.getName().trim().isEmpty()) {
                return Result.failure("分类名称不能为空");
            }

            // 检查分类名称是否已被其他分类使用
            AttributeCategory nameCheck = attributeCategoryService.queryByName(category.getName().trim());
            if (nameCheck != null && !nameCheck.getId().equals(id)) {
                return Result.failure("分类名称已存在");
            }

            existing.setName(category.getName().trim());
            if (category.getEnabled() != null) {
                existing.setEnabled(category.getEnabled());
            }

            AttributeCategory updated = attributeCategoryService.update(existing);
            return Result.success(updated);
        } catch (Exception e) {
            log.error("编辑参数分类失败", e);
            return Result.failure("编辑参数分类失败：" + e.getMessage());
        }
    }

    /**
     * 删除参数分类
     */
    @DeleteMapping("/param/categories/{id}")
    public Result<Boolean> deleteParamCategory(@PathVariable("id") Integer id) {
        try {
            AttributeCategory existing = attributeCategoryService.queryById(id);
            if (existing == null) {
                return Result.failure("分类不存在");
            }

            // 检查分类下是否有参数
            Long attributeCount = attributeService.countByCategoryId(id);
            if (attributeCount != null && attributeCount > 0) {
                return Result.failure("该分类下还有参数，无法删除");
            }

            int result = attributeCategoryService.deleteById(id);
            return Result.success(result > 0);
        } catch (Exception e) {
            log.error("删除参数分类失败", e);
            return Result.failure("删除参数分类失败：" + e.getMessage());
        }
    }

    /**
     * 获取属性分类详情及其属性列表
     */
    @GetMapping("/param/info/{id}")
    public Result<Map<String, Object>> getParamInfo(@PathVariable("id") Integer id) {
        AttributeCategory category = attributeCategoryService.queryById(id);
        if (category == null) {
            return Result.failure("属性分类不存在");
        }

        Map<String, Object> result = new HashMap<>();
        result.put("id", category.getId());
        result.put("categoryId", category.getId());
        result.put("name", category.getName());
        result.put("enabled", category.getEnabled());

        // 获取该分类下的所有属性
        List<Attribute> attributes = attributeService.queryByAttributeCategoryId(id);
        List<Map<String, Object>> attributeList = new ArrayList<>();

        for (Attribute attr : attributes) {
            Map<String, Object> attrMap = new HashMap<>();
            attrMap.put("attributeId", attr.getId());
            attrMap.put("name", attr.getName());
            attrMap.put("value", ""); // 默认值为空，由前端填写
            attributeList.add(attrMap);
        }

        result.put("value", attributeList);
        return Result.success(result);
    }

    /**
     * 保存属性分类和属性
     */
    @PostMapping("/param/save/{id}")
    public Result<Map<String, Object>> saveParam(@PathVariable("id") Integer id, @RequestBody Map<String, Object> request) {
        try {
            Integer categoryId = (Integer) request.get("categoryId");
            String categoryName = (String) request.get("name");
            Boolean enabled = (Boolean) request.get("enabled");
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> attributeList = (List<Map<String, Object>>) request.get("value");

            // 如果是新建分类
            if (categoryId == null) {
                // 创建新的属性分类
                AttributeCategory category = new AttributeCategory();
                category.setName(categoryName);
                category.setEnabled(true);
                attributeCategoryService.save(category);
                categoryId = category.getId();
            } else if (categoryId != null) {
                // 更新现有分类
                AttributeCategory category = attributeCategoryService.queryById(categoryId);
                if (category != null) {
                    category.setEnabled(enabled);
                    attributeCategoryService.updateById(category);
                }
            }

            // 处理属性数据
            if (attributeList != null && !attributeList.isEmpty() && categoryId != null) {
                for (Map<String, Object> attrData : attributeList) {
                    Integer attributeId = (Integer) attrData.get("attributeId");
                    String attributeName = (String) attrData.get("name");
                    String attributeValue = (String) attrData.get("value");

                    if (attributeName != null && !attributeName.trim().isEmpty()) {
                        Attribute attribute = new Attribute();
                        attribute.setAttributeCategoryId(categoryId);
                        attribute.setName(attributeName.trim());
                        attribute.setInputType(true); // 默认为下拉选择
                        attribute.setSortOrder((byte) 0);

                        // 如果有值，设置为JSON数组格式
                        if (attributeValue != null && !attributeValue.trim().isEmpty()) {
                            attribute.setValues("[\"" + attributeValue.trim() + "\"]");
                        } else {
                            attribute.setValues("[]");
                        }

                        if (attributeId != null && attributeId > 0) {
                            // 更新现有属性
                            attribute.setId(attributeId);
                            attributeService.updateById(attribute);
                        } else {
                            // 创建新属性
                            attributeService.create(attribute);
                        }
                    }
                }
            }

            Map<String, Object> result = new HashMap<>();
            result.put("categoryId", categoryId);
            result.put("name", categoryName);
            return Result.success(result);
        } catch (Exception e) {
            log.error("保存属性分类和属性失败", e);
            return Result.failure("保存失败：" + e.getMessage());
        }
    }

    /**
     * 删除属性分类
     */
    @DeleteMapping("/param/{id}")
    public Result<Boolean> deleteParam(@PathVariable("id") Integer id) {
        try {
            AttributeCategory category = attributeCategoryService.queryById(id);
            if (category == null) {
                return Result.failure("属性分类不存在");
            }

            // 检查分类下是否有属性
            List<Attribute> attributes = attributeService.queryByAttributeCategoryId(id);
            if (attributes != null && !attributes.isEmpty()) {
                return Result.failure("该分类下还有属性，无法删除");
            }

            int result = attributeCategoryService.deleteById(id);
            return Result.success(result > 0);
        } catch (Exception e) {
            log.error("删除属性分类失败", e);
            return Result.failure("删除失败：" + e.getMessage());
        }
    }
    @DeleteMapping("/param/deleteAttr/{id}")
    public Result<Boolean> deleteAttr(@PathVariable("id") Integer id) {
        try {

            int result = attributeService.deleteById(id);
            return Result.success(result > 0);
        } catch (Exception e) {
            log.error("删除属性分类失败", e);
            return Result.failure("删除失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除属性分类
     */
    @DeleteMapping("/param/batch")
    public Result<Boolean> batchDeleteParam(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Integer> ids = (List<Integer>) request.get("ids");

            if (ids == null || ids.isEmpty()) {
                return Result.failure("请选择要删除的属性分类");
            }

            int deletedCount = 0;
            int skippedCount = 0;

            for (Integer id : ids) {
                try {
                    int result = attributeService.deleteById(id);
                    if (result > 0) {
                        deletedCount++;
                    } else {
                        skippedCount++;
                    }
                } catch (Exception e) {
                    log.error("删除属性分类失败: " + id, e);
                    skippedCount++;
                }
            }

            if (deletedCount == ids.size()) {
                return Result.success(true).setMsg("批量删除成功，共删除 " + deletedCount + " 个属性分类");
            } else if (deletedCount > 0) {
                return Result.success(true).setMsg("部分删除成功，共删除 " + deletedCount + " 个属性分类，跳过 " + skippedCount + " 个");
            } else {
                return Result.failure("删除失败，所有分类都无法删除（可能包含属性或不存在）");
            }
        } catch (Exception e) {
            log.error("批量删除参数模板失败", e);
            return Result.failure("批量删除失败：" + e.getMessage());
        }
    }

    /**
     * 获取规格模板列表
     */
    @GetMapping("/product/rule")
    public Result<Map<String, Object>> getRuleList(
            @RequestParam(defaultValue = "") String rule_name,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer limit) {

        List<Map<String, Object>> list = specificationTemplateService.getTemplateList(rule_name, page, limit);
        long total = specificationTemplateService.getPageTotal(rule_name);

        Map<String, Object> result = new HashMap<>();
        result.put("list", list);
        result.put("count", total);

        return Result.success(result);
    }

    /**
     * 获取规格模板详情
     */
    @GetMapping("/product/rule/{id}")
    public Result<Map<String, Object>> getRuleInfo(@PathVariable("id") Integer id) {
        Map<String, Object> info = specificationTemplateService.getTemplateInfo(id);
        Map<String, Object> result = new HashMap<>();
        result.put("info", info);
        return Result.success(result);
    }

    /**
     * 保存规格模板
     */
    @PostMapping("/product/rule/{id}")
    public Result<SpecificationTemplate> saveRule(@PathVariable("id") Integer id, @RequestBody Map<String, Object> data) {
        SpecificationTemplate template = specificationTemplateService.saveTemplate(data, id);
        return Result.success(template);
    }

    /**
     * 删除规格模板
     */
    @DeleteMapping("/product/rule/delete")
    public Result<Boolean> deleteRule(@RequestBody Map<String, String> params) {
        String idsStr = params.get("ids");
        if (idsStr == null || idsStr.isEmpty()) {
            return Result.failure("请选择要删除的规格模板");
        }

        String[] idArray = idsStr.split(",");
        boolean success = true;

        for (String idStr : idArray) {
            try {
                Integer id = Integer.parseInt(idStr.trim());
                int result = specificationTemplateService.deleteById(id);
                if (result <= 0) {
                    success = false;
                }
            } catch (NumberFormatException e) {
                success = false;
            }
        }

        return Result.success(success);
    }

    /**
     * 获取商品属性列表（从weshop_attribute表）
     */
    @GetMapping("/attribute/list")
    public Result<List<Attribute>> getAttributeList(
            @RequestParam(value = "categoryId", required = false) Integer categoryId) {

        List<Attribute> attributes;
        if (categoryId != null && categoryId > 0) {
            attributes = attributeService.queryByCategoryId(categoryId);
        } else {
            attributes = attributeService.queryAllEnabled();
        }

        return Result.success(attributes);
    }

    /**
     * Convert Goods entity to Map and parse displaySystems JSON
     */
    private Map<String, Object> convertGoodsToMap(Goods goods) {
        Map<String, Object> result = new HashMap<>();

        // Copy all fields from goods
        result.put("id", goods.getId());
        result.put("categoryId", goods.getCategoryId());
        result.put("goodsSn", goods.getGoodsSn());
        result.put("name", goods.getName());
        result.put("brandId", goods.getBrandId());
        result.put("goodsNumber", goods.getGoodsNumber());
        result.put("keywords", goods.getKeywords());
        result.put("goodsBrief", goods.getGoodsBrief());
        result.put("isOnSale", goods.getIsOnSale());
        result.put("createTime", goods.getCreateTime());
        result.put("sortOrder", goods.getSortOrder());
        result.put("isDelete", goods.getIsDelete());
        result.put("attributeCategory", goods.getAttributeCategory());
        result.put("counterPrice", goods.getCounterPrice());
        result.put("extraPrice", goods.getExtraPrice());
        result.put("isNewly", goods.getIsNewly());
        result.put("goodsUnit", goods.getGoodsUnit());
        result.put("primaryPicUrl", goods.getPrimaryPicUrl());
        result.put("listPicUrl", goods.getListPicUrl());
        result.put("retailPrice", goods.getRetailPrice());
        result.put("sellVolume", goods.getSellVolume());
        result.put("primaryProductId", goods.getPrimaryProductId());
        result.put("unitPrice", goods.getUnitPrice());
        result.put("promotionDesc", goods.getPromotionDesc());
        result.put("promotionTag", goods.getPromotionTag());
        result.put("appExclusivePrice", goods.getAppExclusivePrice());
        result.put("isAppExclusive", goods.getIsAppExclusive());
        result.put("isLimited", goods.getIsLimited());
        result.put("isHot", goods.getIsHot());
        result.put("goodsDesc", goods.getGoodsDesc());
        result.put("detailTag", goods.getDetailTag());

        // Parse displaySystems JSON to List<Integer>
        if (StringUtils.isNotBlank(goods.getDisplaySystems())) {
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                List<Integer> displaySystems = objectMapper.readValue(
                    goods.getDisplaySystems(),
                    objectMapper.getTypeFactory().constructCollectionType(List.class, Integer.class)
                );
                result.put("displaySystems", displaySystems);
            } catch (Exception e) {
                log.error("Failed to parse displaySystems JSON: {}", goods.getDisplaySystems(), e);
                result.put("displaySystems", new ArrayList<>());
            }
        } else {
            result.put("displaySystems", new ArrayList<>());
        }

        return result;
    }

    /**
     * Get specifications formatted for frontend
     */
    private List<Map<String, Object>> getSpecificationsForFrontend(Integer goodsId) {
        log.info("Getting specifications for goods ID: {}", goodsId);
        List<GoodsSpecification> goodsSpecifications = goodsSpecificationService.queryByGoodsId(goodsId);
        log.info("Found {} goods specifications", goodsSpecifications != null ? goodsSpecifications.size() : 0);
        if (goodsSpecifications == null || goodsSpecifications.isEmpty()) {
            log.info("No specifications found for goods ID: {}", goodsId);
            return new ArrayList<>();
        }

        // Group specifications by specification name
        Map<String, List<GoodsSpecification>> specGroups = goodsSpecifications.stream()
                .collect(Collectors.groupingBy(spec -> {
                    // Get specification name by ID
                    Specification specification = specificationService.findById(spec.getSpecificationId());
                    return specification != null ? specification.getName() : "未知规格";
                }));

        List<Map<String, Object>> result = new ArrayList<>();
        for (Map.Entry<String, List<GoodsSpecification>> entry : specGroups.entrySet()) {
            Map<String, Object> specItem = new HashMap<>();
            specItem.put("value", entry.getKey());

            List<Map<String, Object>> details = entry.getValue().stream()
                    .map(spec -> {
                        Map<String, Object> detail = new HashMap<>();
                        detail.put("value", spec.getValue());
                        detail.put("pic", spec.getPicUrl());
                        return detail;
                    })
                    .collect(Collectors.toList());

            specItem.put("detail", details);
            specItem.put("add_pic", details.stream().anyMatch(d -> d.get("pic") != null && !d.get("pic").toString().isEmpty()) ? 1 : 0);

            result.add(specItem);
        }

        return result;
    }

    /**
     * Convert Product to Map with specifications
     */
    private Map<String, Object> convertProductToMapWithSpecifications(Product product) {
        Map<String, Object> result = new HashMap<>();

        // Copy basic product fields
        result.put("id", product.getId());
        result.put("goodsId", product.getGoodsId());
        result.put("goodsSn", product.getGoodsSn());
        result.put("goodsNumber", product.getGoodsNumber());
        result.put("retailPrice", product.getRetailPrice());
        result.put("costPrice", product.getCostPrice());
        result.put("otPrice", product.getOtPrice());
        result.put("barCode", product.getBarCode());
        result.put("barCodeNumber", product.getBarCodeNumber());
        result.put("goodsWeight", product.getGoodsWeight());
        result.put("goodsVolume", product.getGoodsVolume());
        result.put("picUrl", product.getPicUrl());
        result.put("brokerage", product.getBrokerage());
        result.put("brokerageTwo", product.getBrokerageTwo());
        result.put("quota", product.getQuota());
        result.put("quotaShow", product.getQuotaShow());
        result.put("createTime", product.getCreateTime());
        result.put("updateTime", product.getUpdateTime());

        // Add specifications
        if (product.getGoodsSpecificationIds() != null && !product.getGoodsSpecificationIds().trim().isEmpty()) {
            Map<String, String> specifications = getProductSpecifications(product.getGoodsSpecificationIds());
            result.put("specifications", specifications);
        } else {
            result.put("specifications", new HashMap<>());
        }

        return result;
    }

    /**
     * Get product specifications by specification IDs
     */
    private Map<String, String> getProductSpecifications(String goodsSpecificationIds) {
        Map<String, String> specifications = new HashMap<>();

        if (goodsSpecificationIds == null || goodsSpecificationIds.trim().isEmpty()) {
            return specifications;
        }

        try {
            // Parse specification IDs (assuming comma-separated)
            String[] specIds = goodsSpecificationIds.split(",");
            for (String specIdStr : specIds) {
                Integer specId = Integer.parseInt(specIdStr.trim());
                GoodsSpecification goodsSpec = goodsSpecificationService.queryById(specId);
                if (goodsSpec != null) {
                    // Get specification name
                    Specification specification = specificationService.findById(goodsSpec.getSpecificationId());
                    if (specification != null) {
                        specifications.put(specification.getName(), goodsSpec.getValue());
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error parsing goods specification IDs: {}", goodsSpecificationIds, e);
        }

        return specifications;
    }

    // ==================== 商品标签分类相关接口 ====================

    /**
     * 获取标签分类列表
     */
    @GetMapping("/label_cate/list")
    public Result<Map<String, Object>> getLabelCategoryList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer limit,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) Integer status) {

        Map<String, Object> params = new HashMap<>();
        params.put("page", page);
        params.put("limit", limit);
        if (name != null && !name.trim().isEmpty()) {
            params.put("name", name.trim());
        }
        if (status != null) {
            params.put("status", status);
        }

        Map<String, Object> result = productLabelCategoryService.queryPage(params);
        return Result.success(result);
    }

    /**
     * 获取标签分类表单数据
     */
    @GetMapping("/label_cate/form/{id}")
    public Result<Map<String, Object>> getLabelCategoryForm(@PathVariable("id") Integer id) {
        Map<String, Object> result = new HashMap<>();

        if (id > 0) {
            // 编辑模式，获取分类信息
            ProductLabelCategory category = productLabelCategoryService.queryById(id);
            result.put("info", category);
        } else {
            // 新增模式，返回空对象
            result.put("info", new ProductLabelCategory());
        }

        return Result.success(result);
    }

    /**
     * 保存标签分类
     */
    @PostMapping("/label_cate/save")
    public Result<ProductLabelCategory> saveLabelCategory(@RequestBody ProductLabelCategory category) {
        try {
            ProductLabelCategory saved = productLabelCategoryService.saveCategory(category);
            return Result.success(saved);
        } catch (Exception e) {
            log.error("保存标签分类失败", e);
            return Result.failure("保存失败：" + e.getMessage());
        }
    }

    /**
     * 删除标签分类
     */
    @DeleteMapping("/label_cate/{id}")
    public Result<Boolean> deleteLabelCategory(@PathVariable("id") Integer id) {
        try {
            // 检查分类下是否有标签
            List<ProductLabel> labels = productLabelService.getByCategoryId(id);
            if (labels != null && !labels.isEmpty()) {
                return Result.failure("该分类下还有标签，无法删除");
            }

            int result = productLabelCategoryService.deleteById(id);
            return Result.success(result > 0);
        } catch (Exception e) {
            log.error("删除标签分类失败", e);
            return Result.failure("删除失败：" + e.getMessage());
        }
    }

    // ==================== 商品标签相关接口 ====================

    /**
     * 获取标签列表
     */
    @GetMapping("/label/list")
    public Result<Map<String, Object>> getLabelList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer limit,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) Integer cate_id,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) Integer is_show) {

        Map<String, Object> params = new HashMap<>();
        params.put("page", page);
        params.put("limit", limit);
        if (name != null && !name.trim().isEmpty()) {
            params.put("name", name.trim());
        }
        if (cate_id != null) {
            params.put("cate_id", cate_id);
        }
        if (status != null) {
            params.put("status", status);
        }
        if (is_show != null) {
            params.put("is_show", is_show);
        }

        Map<String, Object> result = productLabelService.queryPage(params);
        return Result.success(result);
    }

    /**
     * 获取所有可用标签（用于商品选择）
     */
    @GetMapping("/label/use_list")
    public Result<List<ProductLabel>> getLabelUseList() {
        List<ProductLabel> labels = productLabelService.getAvailableList();
        return Result.success(labels);
    }

    /**
     * 获取商品标签（用于商品添加/编辑）
     */
    @GetMapping("/product_label")
    public Result<List<ProductLabel>> getProductLabel() {
        List<ProductLabel> labels = productLabelService.getAvailableList();
        return Result.success(labels);
    }

    /**
     * 获取标签详情
     */
    @GetMapping("/label/info/{id}")
    public Result<ProductLabel> getLabelInfo(@PathVariable("id") Integer id) {
        ProductLabel label = productLabelService.queryById(id);
        if (label == null) {
            return Result.failure("标签不存在");
        }
        return Result.success(label);
    }

    /**
     * 保存标签
     */
    @PostMapping("/label/save")
    public Result<ProductLabel> saveLabel(@RequestBody ProductLabel label) {
        try {
            ProductLabel saved = productLabelService.saveLabel(label);
            return Result.success(saved);
        } catch (Exception e) {
            log.error("保存标签失败", e);
            return Result.failure("保存失败：" + e.getMessage());
        }
    }

    /**
     * 更新标签状态
     */
    @PutMapping("/label/status/{id}/{status}")
    public Result<Boolean> updateLabelStatus(@PathVariable("id") Integer id, @PathVariable("status") Integer status) {
        try {
            boolean success = productLabelService.updateStatus(id, status);
            return Result.success(success);
        } catch (Exception e) {
            log.error("更新标签状态失败", e);
            return Result.failure("更新失败：" + e.getMessage());
        }
    }

    /**
     * 更新标签显示状态
     */
    @PutMapping("/label/is_show/{id}/{is_show}")
    public Result<Boolean> updateLabelIsShow(@PathVariable("id") Integer id, @PathVariable("is_show") Integer isShow) {
        try {
            boolean success = productLabelService.updateIsShow(id, isShow);
            return Result.success(success);
        } catch (Exception e) {
            log.error("更新标签显示状态失败", e);
            return Result.failure("更新失败：" + e.getMessage());
        }
    }

    /**
     * 删除标签
     */
    @DeleteMapping("/label/{id}")
    public Result<Boolean> deleteLabel(@PathVariable("id") Integer id) {
        try {
            // 先删除商品标签关联
            goodsLabelService.deleteByLabelId(id);

            // 再删除标签
            int result = productLabelService.deleteById(id);
            return Result.success(result > 0);
        } catch (Exception e) {
            log.error("删除标签失败", e);
            return Result.failure("删除失败：" + e.getMessage());
        }
    }

    // ==================== Excel导入相关接口 ====================

    /**
     * 下载商品导入Excel模板
     */
    @GetMapping("/product/import/template")
    public void downloadImportTemplate(HttpServletResponse response) {
        try {
            // 生成模板文件
            String templatePath = goodsService.generateGoodsImportTemplate();
            File templateFile = new File(templatePath);

            if (!templateFile.exists()) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return;
            }

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");

            String fileName = "商品导入模板.xlsx";
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
            response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"");

            // 输出文件
            try (FileInputStream fis = new FileInputStream(templateFile);
                 OutputStream os = response.getOutputStream()) {

                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = fis.read(buffer)) != -1) {
                    os.write(buffer, 0, bytesRead);
                }
                os.flush();
            }

            // 删除临时文件
            templateFile.delete();

        } catch (Exception e) {
            log.error("下载商品导入模板失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 通过Excel导入商品数据
     */
    @PostMapping("/product/import")
    public Result<GoodsImportResultDTO> importGoods(@RequestParam("file") MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return Result.failure("请选择要导入的Excel文件");
        }

        // 检查文件类型
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || (!originalFilename.endsWith(".xlsx") && !originalFilename.endsWith(".xls"))) {
            return Result.failure("请上传Excel格式文件（.xlsx或.xls）");
        }

        // 检查文件大小（限制10MB）
        if (file.getSize() > 10 * 1024 * 1024) {
            return Result.failure("文件大小不能超过10MB");
        }

        try {
            GoodsImportResultDTO result = goodsService.importGoodsFromExcel(file);
            return Result.success(result);

        } catch (IOException e) {
            log.error("导入商品Excel文件失败", e);
            return Result.failure("文件读取失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("导入商品数据失败", e);
            return Result.failure("导入失败：" + e.getMessage());
        }
    }
}
