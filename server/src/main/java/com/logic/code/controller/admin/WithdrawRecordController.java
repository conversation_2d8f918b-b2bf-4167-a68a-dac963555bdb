package com.logic.code.controller.admin;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logic.code.entity.WithdrawRecord;
import com.logic.code.entity.User;
import com.logic.code.mapper.WithdrawRecordMapper;
import com.logic.code.mapper.UserMapper;
import com.logic.code.common.response.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 提现记录管理控制器
 */
@RestController
@RequestMapping("/adminapi/finance/extract")
@Slf4j
public class WithdrawRecordController {

    @Autowired
    private WithdrawRecordMapper withdrawRecordMapper;

    @Autowired
    private UserMapper userMapper;

    /**
     * 提现记录列表
     * @param status 提现状态
     * @param extract_type 提现方式
     * @param nireid 搜索关键词
     * @param data 时间范围
     * @param page 页码
     * @param limit 每页数量
     * @return 提现记录列表
     */
    @GetMapping("")
    public Result<?> getWithdrawList(
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "extract_type", required = false) String extract_type,
            @RequestParam(value = "nireid", required = false) String nireid,
            @RequestParam(value = "data", required = false) String data,
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "limit", defaultValue = "20") Integer limit) {

        try {
            // 创建分页对象
            Page<WithdrawRecord> pageObj = new Page<>(page, limit);
            
            // 构建查询条件
            QueryWrapper<WithdrawRecord> queryWrapper = new QueryWrapper<>();
            
            // 提现状态筛选
            if (status != null && !status.trim().isEmpty()) {
                switch (status) {
                    case "-1":
                        queryWrapper.eq("status", "failed");
                        break;
                    case "0":
                        queryWrapper.eq("status", "pending");
                        break;
                    case "1":
                        queryWrapper.eq("status", "success");
                        break;
                    default:
                        break;
                }
            }
            
            // 提现方式筛选
            if (extract_type != null && !extract_type.trim().isEmpty()) {
                switch (extract_type) {
                    case "wx":
                        queryWrapper.eq("payment_method", "wechat");
                        break;
                    case "alipay":
                        queryWrapper.eq("payment_method", "alipay");
                        break;
                    case "bank":
                        queryWrapper.eq("payment_method", "bank");
                        break;
                    default:
                        break;
                }
            }
            
            // 时间范围筛选
            if (data != null && !data.trim().isEmpty()) {
                String[] timeArray = data.split("-");
                if (timeArray.length == 2) {
                    try {
                        LocalDateTime startTime = LocalDateTime.parse(timeArray[0] + " 00:00:00", DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss"));
                        LocalDateTime endTime = LocalDateTime.parse(timeArray[1] + " 23:59:59", DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss"));
                        queryWrapper.between("apply_time", startTime, endTime);
                    } catch (Exception e) {
                        log.warn("时间格式解析失败: {}", data);
                    }
                }
            }
            
            // 搜索关键词
            if (nireid != null && !nireid.trim().isEmpty()) {
                // 先搜索用户表获取匹配的用户ID
                List<User> users = userMapper.selectList(
                    new QueryWrapper<User>().like("nickname", nireid)
                                            .or().like("real_name", nireid)
                );
                
                if (!users.isEmpty()) {
                    List<Integer> userIds = users.stream().map(User::getId).collect(Collectors.toList());
                    queryWrapper.in("user_id", userIds);
                } else {
                    // 如果没有找到匹配的用户，返回空结果
                    Map<String, Object> result = new HashMap<>();
                    result.put("list", new ArrayList<>());
                    result.put("count", 0);
                    return Result.success(result);
                }
            }
            
            // 按申请时间倒序排列
            queryWrapper.orderByDesc("apply_time");
            
            // 执行分页查询
            IPage<WithdrawRecord> pageResult = withdrawRecordMapper.selectPage(pageObj, queryWrapper);
            
            // 构建返回数据
            List<Map<String, Object>> list = new ArrayList<>();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            
            for (WithdrawRecord record : pageResult.getRecords()) {
                Map<String, Object> item = new HashMap<>();
                item.put("id", record.getId());
                
                // 获取用户信息
                User user = userMapper.selectById(record.getUserId());
                item.put("uid", record.getUserId());
                item.put("nickname", user != null ? user.getNickname() : "未知用户");
                item.put("real_name", user != null ? user.getUsername() : "");
                item.put("wechat", user != null ? user.getWechatOpenId() : "");
               /* item.put("alipay_code", user != null ? user.getAlipayCode() : "");*/
                
                // 提现金额信息
                item.put("extract_price", record.getAmount() != null ? record.getAmount().toString() : "0");
                item.put("extract_fee", record.getFee() != null ? record.getFee().toString() : "0");
                item.put("receive_price", record.getActualAmount() != null ? record.getActualAmount().toString() : "0");
                
                // 提现方式信息
                item.put("extract_type", record.getPaymentMethod());
                item.put("bank_code", "");
                item.put("bank_address", "");
                item.put("qrcode_url", "");
                
                // 根据提现方式填充相应信息
                if ("bank".equals(record.getPaymentMethod()) && record.getBankCardSnapshotObject() != null) {
                    WithdrawRecord.BankCardSnapshot bankCard = record.getBankCardSnapshotObject();
                    item.put("bank_code", bankCard.getCardNumber() != null ? bankCard.getCardNumber() : "");
                    item.put("bank_address", bankCard.getBankAddress() != null ? bankCard.getBankAddress() : "");
                } else if ("weixin".equals(record.getPaymentMethod()) || "alipay".equals(record.getPaymentMethod())) {
                    // 这里可以根据实际业务逻辑获取收款码URL
                    item.put("qrcode_url", "");
                }
                
                // 时间信息
                item.put("add_time", record.getApplyTime() != null ? record.getApplyTime().getTime() / 1000 : 0);
                item.put("process_time", record.getProcessTime() != null ? record.getProcessTime().getTime() / 1000 : 0);
                item.put("complete_time", record.getCompleteTime() != null ? record.getCompleteTime().getTime() / 1000 : 0);
                
                // 状态信息
                item.put("status", getStatusValue(record.getStatus()));
                item.put("mark", record.getRemark() != null ? record.getRemark() : "");
                item.put("fail_msg", "");
                
                list.add(item);
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("list", list);
            result.put("count", pageResult.getTotal());
            
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("获取提现记录列表失败", e);
            return Result.failure("获取提现记录列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取提现记录状态值
     */
    private Integer getStatusValue(String status) {
        if (status == null) return 0;
        
        switch (status) {
            case "pending":
                return 0;
            case "success":
                return 1;
            case "failed":
                return -1;
            default:
                return 0;
        }
    }

    /**
     * 获取提现统计信息
     * @param data 时间范围
     * @param status 提现状态
     * @param nireid 搜索关键词
     * @return 统计信息
     */
    @GetMapping("/statistics")
    public Result<?> getWithdrawStatistics(
            @RequestParam(value = "data", required = false) String data,
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "nireid", required = false) String nireid) {

        try {
            // 构建查询条件
            QueryWrapper<WithdrawRecord> queryWrapper = new QueryWrapper<>();
            
            // 提现状态筛选
            if (status != null && !status.trim().isEmpty()) {
                switch (status) {
                    case "-1":
                        queryWrapper.eq("status", "failed");
                        break;
                    case "0":
                        queryWrapper.eq("status", "pending");
                        break;
                    case "1":
                        queryWrapper.eq("status", "success");
                        break;
                    default:
                        break;
                }
            }
            
            // 时间范围筛选
            if (data != null && !data.trim().isEmpty()) {
                String[] timeArray = data.split("-");
                if (timeArray.length == 2) {
                    try {
                        LocalDateTime startTime = LocalDateTime.parse(timeArray[0] + " 00:00:00", DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss"));
                        LocalDateTime endTime = LocalDateTime.parse(timeArray[1] + " 23:59:59", DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss"));
                        queryWrapper.between("apply_time", startTime, endTime);
                    } catch (Exception e) {
                        log.warn("时间格式解析失败: {}", data);
                    }
                }
            }
            
            // 搜索关键词
            if (nireid != null && !nireid.trim().isEmpty()) {
                // 先搜索用户表获取匹配的用户ID
                List<User> users = userMapper.selectList(
                    new QueryWrapper<User>().like("nickname", nireid)
                                            .or().like("real_name", nireid)
                );
                
                if (!users.isEmpty()) {
                    List<Integer> userIds = users.stream().map(User::getId).collect(Collectors.toList());
                    queryWrapper.in("user_id", userIds);
                } else {
                    // 如果没有找到匹配的用户，返回空统计
                    Map<String, Object> statistics = new HashMap<>();
                    statistics.put("brokerage_count", "0.00");
                    statistics.put("price", "0.00");
                    statistics.put("priced", "0.00");
                    statistics.put("brokerage_not", "0.00");
                    
                    return Result.success(statistics);
                }
            }
            
            // 查询符合条件的提现记录
            List<WithdrawRecord> records = withdrawRecordMapper.selectList(queryWrapper);
            
            // 统计数据
            // 佣金总金额（所有记录的金额总和）
            BigDecimal brokerageCount = records.stream()
                    .map(WithdrawRecord::getAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            // 待提现金额（待处理记录的金额总和）
            BigDecimal pendingAmount = records.stream()
                    .filter(r -> "pending".equals(r.getStatus()))
                    .map(WithdrawRecord::getAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            // 已提现金额（成功记录的金额总和）
            BigDecimal successAmount = records.stream()
                    .filter(r -> "success".equals(r.getStatus()))
                    .map(WithdrawRecord::getAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            // 未提现金额（失败记录的金额总和）
            BigDecimal failedAmount = records.stream()
                    .filter(r -> "failed".equals(r.getStatus()))
                    .map(WithdrawRecord::getAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            // 构建返回数据
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("brokerage_count", brokerageCount.toString());
            statistics.put("price", pendingAmount.toString());
            statistics.put("priced", successAmount.toString());
            statistics.put("brokerage_not", failedAmount.toString());
            
            return Result.success(statistics);
            
        } catch (Exception e) {
            log.error("获取提现统计数据失败", e);
            return Result.failure("获取提现统计数据失败：" + e.getMessage());
        }
    }

    /**
     * 编辑表单数据
     * @param id 提现记录ID
     * @return 表单数据
     */
    @GetMapping("/{id}/edit")
    public Result<?> getEditForm(@PathVariable Integer id) {
        try {
            WithdrawRecord record = withdrawRecordMapper.selectById(id);
            if (record == null) {
                return Result.failure("提现记录不存在");
            }
            
            // 获取用户信息
            User user = userMapper.selectById(record.getUserId());
            
            Map<String, Object> result = new HashMap<>();
            result.put("id", record.getId());
            result.put("uid", record.getUserId());
            result.put("nickname", user != null ? user.getNickname() : "未知用户");
            result.put("extract_price", record.getAmount() != null ? record.getAmount().toString() : "0");
            result.put("extract_fee", record.getFee() != null ? record.getFee().toString() : "0");
            result.put("receive_price", record.getActualAmount() != null ? record.getActualAmount().toString() : "0");
            result.put("extract_type", record.getPaymentMethod());
            result.put("mark", record.getRemark() != null ? record.getRemark() : "");
            result.put("fail_msg", "");
            result.put("status", getStatusValue(record.getStatus()));
            
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("获取编辑表单数据失败", e);
            return Result.failure("获取编辑表单数据失败：" + e.getMessage());
        }
    }

    /**
     * 拒绝申请
     * @param id 提现记录ID
     * @param data 拒绝原因
     * @return 操作结果
     */
    @PutMapping("/refuse/{id}")
    public Result<?> refuseApplication(@PathVariable Integer id, @RequestBody Map<String, String> data) {
        try {
            WithdrawRecord record = withdrawRecordMapper.selectById(id);
            if (record == null) {
                return Result.failure("提现记录不存在");
            }
            
            String failMsg = data.get("message");
            if (failMsg == null || failMsg.trim().isEmpty()) {
                return Result.failure("拒绝原因不能为空");
            }
            
            // 更新记录状态为失败
            record.setStatus("failed");
            record.setRemark(failMsg);
            record.setProcessTime(new Date());
            record.setUpdateTime(new Date());
            
            int result = withdrawRecordMapper.updateById(record);
            
            if (result > 0) {
                return Result.success("拒绝申请成功");
            } else {
                return Result.failure("拒绝申请失败");
            }
            
        } catch (Exception e) {
            log.error("拒绝申请失败", e);
            return Result.failure("拒绝申请失败：" + e.getMessage());
        }
    }

    /**
     * 通过申请
     * @param id 提现记录ID
     * @return 操作结果
     */
    @PutMapping("/adopt/{id}")
    public Result<?> adoptApplication(@PathVariable Integer id) {
        try {
            WithdrawRecord record = withdrawRecordMapper.selectById(id);
            if (record == null) {
                return Result.failure("提现记录不存在");
            }
            
            // 更新记录状态为成功
            record.setStatus("success");
            record.setProcessTime(new Date());
            record.setCompleteTime(new Date());
            record.setUpdateTime(new Date());
            
            int result = withdrawRecordMapper.updateById(record);
            
            if (result > 0) {
                return Result.success("通过申请成功");
            } else {
                return Result.failure("通过申请失败");
            }
            
        } catch (Exception e) {
            log.error("通过申请失败", e);
            return Result.failure("通过申请失败：" + e.getMessage());
        }
    }
}