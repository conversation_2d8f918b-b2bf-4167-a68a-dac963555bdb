package com.logic.code.controller.app;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logic.code.common.response.Result;
import com.logic.code.entity.MemberDayTip;
import com.logic.code.mapper.MemberDayTipMapper;
import com.logic.code.model.vo.MemberDayTipVO;
import com.logic.code.service.MemberDayTipService;
import jakarta.validation.constraints.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 会员日提示信息管理控制器
 * <AUTHOR>
 */
@RestController
@RequestMapping("/wechat/memberDayTip")
@Validated
public class MemberDayTipController {
    
    @Autowired
    private MemberDayTipService memberDayTipService;
    
    @Autowired
    private MemberDayTipMapper memberDayTipMapper;
    
    /**
     * 根据显示位置获取当前有效的提示信息
     * @param position 显示位置
     * @return 提示信息
     */
    @GetMapping("/active")
    public Result<MemberDayTipVO> getActiveByPosition(@RequestParam(value = "position", defaultValue = "goods") String position) {
        return Result.success(memberDayTipService.getActiveByPosition(position));
    }
    
    /**
     * 获取所有提示信息列表（分页）
     * @param page 页码
     * @param size 每页大小
     * @return 提示信息列表
     */
    @GetMapping("/list")
    public Result<IPage<MemberDayTip>> getList(
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "size", defaultValue = "10") Integer size,
            @RequestParam(value = "position", required = false) String position,
            @RequestParam(value = "enabled", required = false) Integer enabled) {
        
        Page<MemberDayTip> pageParam = new Page<>(page, size);
        QueryWrapper<MemberDayTip> queryWrapper = new QueryWrapper<>();
        
        if (position != null && !position.isEmpty()) {
            queryWrapper.eq("display_position", position);
        }
        
        if (enabled != null) {
            queryWrapper.eq("is_enabled", enabled);
        }
        
        queryWrapper.orderByDesc("priority", "create_time");
        
        IPage<MemberDayTip> result = memberDayTipMapper.selectPage(pageParam, queryWrapper);
        return Result.success(result);
    }
    
    /**
     * 创建新的提示信息
     * @param memberDayTip 提示信息
     * @return 创建结果
     */
    @PostMapping("/create")
    public Result<String> create(@RequestBody @Validated MemberDayTip memberDayTip) {
        memberDayTip.setCreateTime(LocalDateTime.now());
        memberDayTip.setUpdateTime(LocalDateTime.now());
        
        if (memberDayTip.getCreateBy() == null) {
            memberDayTip.setCreateBy("admin");
        }
        if (memberDayTip.getUpdateBy() == null) {
            memberDayTip.setUpdateBy("admin");
        }
        
        int result = memberDayTipMapper.insert(memberDayTip);
        
        if (result > 0) {
            return Result.success("创建成功");
        } else {
            return Result.error("创建失败");
        }
    }
    
    /**
     * 更新提示信息
     * @param id 提示信息ID
     * @param memberDayTip 提示信息
     * @return 更新结果
     */
    @PutMapping("/update/{id}")
    public Result<String> update(@PathVariable @NotNull Integer id, @RequestBody @Validated MemberDayTip memberDayTip) {
        memberDayTip.setId(id);
        memberDayTip.setUpdateTime(LocalDateTime.now());
        
        if (memberDayTip.getUpdateBy() == null) {
            memberDayTip.setUpdateBy("admin");
        }
        
        int result = memberDayTipMapper.updateById(memberDayTip);
        
        if (result > 0) {
            return Result.success("更新成功");
        } else {
            return Result.error("更新失败");
        }
    }
    
    /**
     * 删除提示信息
     * @param id 提示信息ID
     * @return 删除结果
     */
    @DeleteMapping("/delete/{id}")
    public Result<String> delete(@PathVariable @NotNull Integer id) {
        int result = memberDayTipMapper.deleteById(id);
        
        if (result > 0) {
            return Result.success("删除成功");
        } else {
            return Result.error("删除失败");
        }
    }
    
    /**
     * 启用/禁用提示信息
     * @param id 提示信息ID
     * @param enabled 是否启用
     * @return 操作结果
     */
    @PutMapping("/toggle/{id}")
    public Result<String> toggleEnabled(@PathVariable @NotNull Integer id, @RequestParam @NotNull Integer enabled) {
        MemberDayTip memberDayTip = new MemberDayTip();
        memberDayTip.setId(id);
        memberDayTip.setIsEnabled(enabled);
        memberDayTip.setUpdateTime(LocalDateTime.now());
        memberDayTip.setUpdateBy("admin");
        
        int result = memberDayTipMapper.updateById(memberDayTip);
        
        if (result > 0) {
            String action = enabled == 1 ? "启用" : "禁用";
            return Result.success(action + "成功");
        } else {
            return Result.error("操作失败");
        }
    }
}