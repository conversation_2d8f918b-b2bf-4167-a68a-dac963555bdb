package com.logic.code.controller.app;

import com.logic.code.common.response.Result;
import com.logic.code.common.utils.JwtHelper;
import com.logic.code.entity.User;
import com.logic.code.model.vo.PurchaseLimitVO;
import com.logic.code.service.PurchaseLimitService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 商品限购控制器
 * <AUTHOR>
 * @date 2025/8/17
 */
@RestController
@RequestMapping("/app/purchase-limit")
@Slf4j
public class PurchaseLimitController {

    @Resource
    private PurchaseLimitService purchaseLimitService;

    /**
     * 检查商品限购状态
     * @param goodsId 商品ID
     * @return 限购信息
     */
    @GetMapping("/check/{goodsId}")
    public Result<PurchaseLimitVO> checkPurchaseLimit(@PathVariable Integer goodsId) {
        try {
            User userInfo = JwtHelper.getUserInfo();
            PurchaseLimitVO limitInfo = purchaseLimitService.checkPurchaseLimit(goodsId, userInfo.getId());
            return Result.success(limitInfo);
        } catch (Exception e) {
            log.error("检查商品限购状态失败，商品ID：{}，错误：{}", goodsId, e.getMessage(), e);
            return Result.error("检查限购状态失败");
        }
    }

    /**
     * 获取商品限购信息（不需要用户登录）
     * @param goodsId 商品ID
     * @return 限购信息
     */
    @GetMapping("/info/{goodsId}")
    public Result<PurchaseLimitVO> getGoodsLimitInfo(@PathVariable Integer goodsId) {
        try {
            PurchaseLimitVO limitInfo = purchaseLimitService.getGoodsLimitInfo(goodsId);
            return Result.success(limitInfo);
        } catch (Exception e) {
            log.error("获取商品限购信息失败，商品ID：{}，错误：{}", goodsId, e.getMessage(), e);
            return Result.error("获取限购信息失败");
        }
    }

    /**
     * 验证购买数量是否超出限制
     * @param goodsId 商品ID
     * @param purchaseCount 购买数量
     * @return 验证结果
     */
    @GetMapping("/validate/{goodsId}/{purchaseCount}")
    public Result<Boolean> validatePurchaseCount(
            @PathVariable Integer goodsId, 
            @PathVariable Integer purchaseCount) {
        try {
            User userInfo = JwtHelper.getUserInfo();
            boolean isValid = purchaseLimitService.validatePurchaseCount(goodsId, userInfo.getId(), purchaseCount);
            return Result.success(isValid);
        } catch (Exception e) {
            log.error("验证购买数量失败，商品ID：{}，购买数量：{}，错误：{}", goodsId, purchaseCount, e.getMessage(), e);
            return Result.error("验证购买数量失败");
        }
    }
}