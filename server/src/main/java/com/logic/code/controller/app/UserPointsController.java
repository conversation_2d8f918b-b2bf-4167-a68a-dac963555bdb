package com.logic.code.controller.app;

import com.logic.code.common.response.Result;
import com.logic.code.common.utils.JwtHelper;
import com.logic.code.entity.User;
import com.logic.code.service.PointsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 用户积分控制器
 * 提供用户积分查询、记录查询等功能
 * 
 * <AUTHOR>
 * @date 2025/1/3
 */
@RestController
@RequestMapping("/app/user/points")
@Slf4j
public class UserPointsController {
    
    @Autowired
    private PointsService pointsService;
    
    /**
     * 获取用户积分信息（实时计算）
     * GET /app/user/points/info
     */
    @GetMapping("/info")
    public Result<Map<String, Object>> getUserPointsInfo(HttpServletRequest request) {
        try {
            User userInfo = JwtHelper.getUserInfo();
            if (userInfo == null) {
                return Result.failure("用户未登录");
            }
            
            log.debug("获取用户{}的积分信息", userInfo.getId());
            
            Map<String, Object> pointsInfo = pointsService.getUserPointsInfo(userInfo.getId());
            
            return Result.success(pointsInfo);
        } catch (Exception e) {
            log.error("获取用户积分信息失败", e);
            return Result.failure("获取积分信息失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取用户实时积分（简化版本）
     * GET /app/user/points/realtime
     */
    @GetMapping("/realtime")
    public Result<Integer> getUserRealTimePoints(HttpServletRequest request) {
        try {
            User userInfo = JwtHelper.getUserInfo();
            if (userInfo == null) {
                return Result.failure("用户未登录");
            }
            
            Integer points = pointsService.getUserRealTimePoints(userInfo.getId());
            
            return Result.success(points);
        } catch (Exception e) {
            log.error("获取用户实时积分失败", e);
            return Result.failure("获取实时积分失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取用户积分记录
     * GET /app/user/points/records
     */
    @GetMapping("/records")
    public Result<Map<String, Object>> getUserPointsRecords(
            HttpServletRequest request,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        try {
            User userInfo = JwtHelper.getUserInfo();
            if (userInfo == null) {
                return Result.failure("用户未登录");
            }
            
            Map<String, Object> records = pointsService.getUserPointsRecords(userInfo.getId(), page, size);
            
            return Result.success(records);
        } catch (Exception e) {
            log.error("获取用户积分记录失败", e);
            return Result.failure("获取积分记录失败：" + e.getMessage());
        }
    }
    
    /**
     * 检查用户积分数据一致性
     * GET /app/user/points/check-consistency
     */
    @GetMapping("/check-consistency")
    public Result<Map<String, Object>> checkUserPointsConsistency(HttpServletRequest request) {
        try {
            User userInfo = JwtHelper.getUserInfo();
            if (userInfo == null) {
                return Result.failure("用户未登录");
            }
            
            Map<String, Object> consistencyResult = pointsService.checkUserPointsConsistency(userInfo.getId());
            
            return Result.success(consistencyResult);
        } catch (Exception e) {
            log.error("检查用户积分一致性失败", e);
            return Result.failure("检查积分一致性失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取积分配置信息
     * GET /app/user/points/config
     */
    @GetMapping("/config")
    public Result<Object> getPointsConfig() {
        try {
            Object config = pointsService.getPointsConfig();
            return Result.success(config);
        } catch (Exception e) {
            log.error("获取积分配置失败", e);
            return Result.failure("获取积分配置失败：" + e.getMessage());
        }
    }
    
    /**
     * 计算订单可获得的积分
     * POST /app/user/points/calculate-earn
     */
    @PostMapping("/calculate-earn")
    public Result<Integer> calculateEarnPoints(@RequestBody Map<String, Object> params) {
        try {
            Object orderAmountObj = params.get("orderAmount");
            if (orderAmountObj == null) {
                return Result.failure("订单金额不能为空");
            }
            
            java.math.BigDecimal orderAmount = new java.math.BigDecimal(orderAmountObj.toString());
            Integer earnPoints = pointsService.calculateEarnPoints(orderAmount);
            
            return Result.success(earnPoints);
        } catch (Exception e) {
            log.error("计算订单积分失败", e);
            return Result.failure("计算积分失败：" + e.getMessage());
        }
    }
    
    /**
     * 计算积分可抵扣的金额
     * POST /app/user/points/calculate-value
     */
    @PostMapping("/calculate-value")
    public Result<java.math.BigDecimal> calculatePointsValue(@RequestBody Map<String, Object> params) {
        try {
            Object pointsObj = params.get("points");
            if (pointsObj == null) {
                return Result.failure("积分数量不能为空");
            }
            
            Integer points = Integer.valueOf(pointsObj.toString());
            java.math.BigDecimal value = pointsService.calculatePointsValue(points);
            
            return Result.success(value);
        } catch (Exception e) {
            log.error("计算积分价值失败", e);
            return Result.failure("计算积分价值失败：" + e.getMessage());
        }
    }
    
    /**
     * 计算最大可用积分数量
     * POST /app/user/points/calculate-max-usable
     */
    @PostMapping("/calculate-max-usable")
    public Result<Integer> calculateMaxUsablePoints(
            HttpServletRequest request,
            @RequestBody Map<String, Object> params) {
        try {
            User userInfo = JwtHelper.getUserInfo();
            if (userInfo == null) {
                return Result.failure("用户未登录");
            }
            
            Object orderAmountObj = params.get("orderAmount");
            if (orderAmountObj == null) {
                return Result.failure("订单金额不能为空");
            }
            
            // 获取用户实时积分
            Integer userPoints = pointsService.getUserRealTimePoints(userInfo.getId());
            java.math.BigDecimal orderAmount = new java.math.BigDecimal(orderAmountObj.toString());
            
            Integer maxUsablePoints = pointsService.calculateMaxUsablePoints(userPoints, orderAmount);
            
            return Result.success(maxUsablePoints);
        } catch (Exception e) {
            log.error("计算最大可用积分失败", e);
            return Result.failure("计算最大可用积分失败：" + e.getMessage());
        }
    }
}
