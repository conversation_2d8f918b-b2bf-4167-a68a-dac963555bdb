package com.logic.code.controller.app;

import com.logic.code.common.response.Result;
import com.logic.code.common.utils.JwtHelper;
import com.logic.code.entity.TieredPromotionGoods;
import com.logic.code.entity.User;
import com.logic.code.entity.UserGoodsPromotionStats;
import com.logic.code.service.TieredPromotionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 阶梯推广控制器
 */
@RestController
@RequestMapping("/wechat/tiered-promotion")
public class TieredPromotionController {
    
    @Autowired
    private TieredPromotionService tieredPromotionService;
    
    /**
     * 获取所有阶梯推广商品配置
     */
    @GetMapping("/goods/list")
    public Result<List<TieredPromotionGoods>> getTieredPromotionGoods() {
        try {
            List<TieredPromotionGoods> goodsList = tieredPromotionService.getAllTieredPromotionGoods();
            return Result.success(goodsList);
        } catch (Exception e) {
            return Result.failure("获取阶梯推广商品失败：" + e.getMessage());
        }
    }
    
    /**
     * 检查商品是否为阶梯推广商品
     */
    @GetMapping("/goods/check/{goodsId}")
    public Result<Map<String, Object>> checkTieredPromotionGoods(@PathVariable Integer goodsId) {
        try {
            boolean isTieredGoods = tieredPromotionService.isTieredPromotionGoods(goodsId);
            TieredPromotionGoods config = null;
            
            if (isTieredGoods) {
                config = tieredPromotionService.getTieredPromotionConfig(goodsId);
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("isTieredPromotion", isTieredGoods);
            result.put("config", config);
            
            return Result.success(result);
        } catch (Exception e) {
            return Result.failure("检查阶梯推广商品失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取用户的商品推广统计
     */
    @GetMapping("/stats/user")
    public Result<List<UserGoodsPromotionStats>> getUserPromotionStats() {
        try {
            User userInfo = JwtHelper.getUserInfo();
            List<UserGoodsPromotionStats> statsList = tieredPromotionService.getUserAllPromotionStats(userInfo.getId());
            return Result.success(statsList);
        } catch (Exception e) {
            return Result.failure("获取用户推广统计失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取用户对指定商品的推广统计
     */
    @GetMapping("/stats/user/{goodsId}")
    public Result<UserGoodsPromotionStats> getUserGoodsPromotionStats(@PathVariable Integer goodsId) {
        try {
            User userInfo = JwtHelper.getUserInfo();
            UserGoodsPromotionStats stats = tieredPromotionService.getUserPromotionStats(userInfo.getId(), goodsId);
            return Result.success(stats);
        } catch (Exception e) {
            return Result.failure("获取用户商品推广统计失败：" + e.getMessage());
        }
    }
    
    /**
     * 计算阶梯推广佣金（预览）
     */
    @PostMapping("/calculate")
    public Result<Map<String, Object>> calculateTieredCommission(@RequestBody Map<String, Object> params) {
        try {
            User userInfo = JwtHelper.getUserInfo();
            Integer goodsId = Integer.parseInt(params.get("goodsId").toString());
            BigDecimal orderAmount = new BigDecimal(params.get("orderAmount").toString());
            
            Map<String, Object> result = tieredPromotionService.calculateTieredCommission(
                userInfo.getId(), goodsId, orderAmount);
            
            return Result.success(result);
        } catch (Exception e) {
            return Result.failure("计算阶梯推广佣金失败：" + e.getMessage());
        }
    }
    
    /**
     * 检查用户对指定商品的推广资格
     */
    @GetMapping("/qualification/{goodsId}")
    public Result<Map<String, Object>> checkPromotionQualification(@PathVariable Integer goodsId) {
        try {
            User userInfo = JwtHelper.getUserInfo();
            Map<String, Object> qualification = tieredPromotionService.getPromotionQualification(
                userInfo.getId(), goodsId);
            
            return Result.success(qualification);
        } catch (Exception e) {
            return Result.failure("检查推广资格失败：" + e.getMessage());
        }
    }
    
    /**
     * 配置阶梯推广商品（管理员功能）
     */
    @PostMapping("/goods/configure")
    public Result<String> configureTieredPromotionGoods(@RequestBody Map<String, Object> params) {
        try {
            // 这里应该添加管理员权限检查
            User userInfo = JwtHelper.getUserInfo();
            // 简单的权限检查，实际项目中应该有更完善的权限系统
            if (userInfo.getUserLevelId() == null || userInfo.getUserLevelId() != 1) {
                return Result.failure("无权限配置阶梯推广商品");
            }
            
            Integer goodsId = Integer.parseInt(params.get("goodsId").toString());
            BigDecimal tier1Rate = new BigDecimal(params.get("tier1Rate").toString());
            BigDecimal tier2Rate = new BigDecimal(params.get("tier2Rate").toString());
            BigDecimal tier3Rate = new BigDecimal(params.get("tier3Rate").toString());
            BigDecimal tier4PlusRate = new BigDecimal(params.get("tier4PlusRate").toString());
            String description = params.get("description").toString();
            
            boolean success = tieredPromotionService.configureTieredPromotionGoods(
                goodsId, tier1Rate, tier2Rate, tier3Rate, tier4PlusRate, description);
            
            if (success) {
                return Result.success("阶梯推广商品配置成功");
            } else {
                return Result.failure("阶梯推广商品配置失败");
            }
        } catch (Exception e) {
            return Result.failure("配置阶梯推广商品失败：" + e.getMessage());
        }
    }
}