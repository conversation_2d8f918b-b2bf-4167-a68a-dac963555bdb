package com.logic.code.controller.admin;

import com.logic.code.common.response.Result;
import com.logic.code.model.query.AdminUserQuery;
import com.logic.code.model.vo.AdminUserListVO;
import com.logic.code.service.UserService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 管理员推广管理控制器
 * <AUTHOR>
 * @date 2025/9/5
 */
@Slf4j
@RestController
@RequestMapping("/adminapi/promotion")
public class AdminPromotionController {

    @Resource
    private UserService userService;

    /**
     * 获取推广用户详情
     * @param userId 用户ID
     * @param searchKeyword 搜索关键词
     * @param page 页码
     * @param size 每页大小
     * @return 推广用户详情
     */
    @PostMapping("/user/detail")
    public Result<Map<String, Object>> getPromotionUserDetail(
            @RequestParam(required = false) Integer userId,
            @RequestParam(required = false) String searchKeyword,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        try {
            // 调用UserService中的getPromotionUserDetail方法
            Map<String, Object> userDetail = userService.getPromotionUserDetail(null, userId, searchKeyword);
            return Result.success(userDetail);
        } catch (Exception e) {
            log.error("获取推广用户详情失败", e);
            return Result.failure("获取推广用户详情失败：" + e.getMessage());
        }
    }

    /**
     * 获取推广用户列表（管理员专用）
     * @param query 查询参数
     * @return 用户列表
     */
    @GetMapping("/user/list")
    public Result<AdminUserListVO> getPromotionUserList(AdminUserQuery query) {
        try {
            // 调用UserService中的getAdminUserList方法获取推广用户列表
            AdminUserListVO userList = userService.getAdminUserList(query);
            return Result.success(userList);
        } catch (Exception e) {
            log.error("获取推广用户列表失败", e);
            return Result.failure("获取推广用户列表失败：" + e.getMessage());
        }
    }

    /**
     * 搜索推广用户（支持用户名和手机号模糊搜索）
     * @param searchKeyword 搜索关键词
     * @param page 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    @GetMapping("/user/search")
    public Result<Map<String, Object>> searchPromotionUsers(
            @RequestParam(required = true) Integer userId,
            @RequestParam(required = false) String searchKeyword,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        try {
            // 调用UserService中的searchPromotionUsers方法
            Map<String, Object> searchResult = userService.searchPromotionUsers(userId, searchKeyword, page, size);
            return Result.success(searchResult);
        } catch (Exception e) {
            log.error("搜索推广用户失败", e);
            return Result.failure("搜索推广用户失败：" + e.getMessage());
        }
    }
}