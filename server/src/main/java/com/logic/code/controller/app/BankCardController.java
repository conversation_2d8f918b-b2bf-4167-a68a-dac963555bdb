package com.logic.code.controller.app;

import com.logic.code.common.response.Result;
import com.logic.code.common.utils.JwtHelper;
import com.logic.code.entity.BankCardInfo;
import com.logic.code.entity.User;
import com.logic.code.service.BankCardInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 银行卡管理控制器
 * <AUTHOR>
 * @date 2025-08-26
 */
@Slf4j
@RestController
@RequestMapping("/wechat/bankcard")
public class BankCardController {

    @Autowired
    private BankCardInfoService bankCardInfoService;

    /**
     * 获取用户银行卡列表
     * @return 银行卡列表
     */
    @RequestMapping("/list")
    public Result<List<BankCardInfo>> getBankCardList() {
        try {
            User userInfo = JwtHelper.getUserInfo();
            List<BankCardInfo> bankCards = bankCardInfoService.getBankCardsByUserId(userInfo.getId());
            
            // 对银行卡号进行脱敏处理
            bankCards.forEach(card -> {
                String originalCardNumber = card.getCardNumber();
                card.setCardNumber(card.getMaskedCardNumber());
            });
            
            return Result.success(bankCards);
        } catch (Exception e) {
            log.error("获取银行卡列表失败: {}", e.getMessage(), e);
            return Result.failure("获取银行卡列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取默认银行卡
     * @return 默认银行卡信息
     */
    @RequestMapping("/default")
    public Result<BankCardInfo> getDefaultBankCard() {
        try {
            User userInfo = JwtHelper.getUserInfo();
            BankCardInfo defaultCard = bankCardInfoService.getDefaultBankCard(userInfo.getId());
            
            if (defaultCard != null) {
                // 对银行卡号进行脱敏处理
                defaultCard.setCardNumber(defaultCard.getMaskedCardNumber());
            }
            
            return Result.success(defaultCard);
        } catch (Exception e) {
            log.error("获取默认银行卡失败: {}", e.getMessage(), e);
            return Result.failure("获取默认银行卡失败：" + e.getMessage());
        }
    }

    /**
     * 添加银行卡
     * @param params 银行卡信息参数
     * @return 添加结果
     */
    @RequestMapping("/add")
    public Result<String> addBankCard(@RequestBody Map<String, Object> params) {
        try {
            User userInfo = JwtHelper.getUserInfo();
            
            // 验证参数
            bankCardInfoService.validateBankCardInfo(params);
            
            // 保存银行卡信息
            BankCardInfo savedCard = bankCardInfoService.saveBankCardInfo(userInfo.getId(), params);
            
            if (savedCard != null) {
                return Result.success("银行卡添加成功");
            } else {
                return Result.failure("银行卡添加失败");
            }
        } catch (IllegalArgumentException e) {
            return Result.failure(e.getMessage());
        } catch (Exception e) {
            log.error("添加银行卡失败: {}", e.getMessage(), e);
            return Result.failure("添加银行卡失败：" + e.getMessage());
        }
    }

    /**
     * 设置默认银行卡
     * @param params 包含银行卡ID的参数
     * @return 设置结果
     */
    @RequestMapping("/setDefault")
    public Result<String> setDefaultBankCard(@RequestBody Map<String, Object> params) {
        try {
            User userInfo = JwtHelper.getUserInfo();
            Integer cardId = Integer.parseInt(params.get("cardId").toString());
            
            boolean success = bankCardInfoService.setDefaultBankCard(userInfo.getId(), cardId);
            
            if (success) {
                return Result.success("默认银行卡设置成功");
            } else {
                return Result.failure("默认银行卡设置失败");
            }
        } catch (Exception e) {
            log.error("设置默认银行卡失败: {}", e.getMessage(), e);
            return Result.failure("设置默认银行卡失败：" + e.getMessage());
        }
    }

    /**
     * 删除银行卡
     * @param params 包含银行卡ID的参数
     * @return 删除结果
     */
    @RequestMapping("/delete")
    public Result<String> deleteBankCard(@RequestBody Map<String, Object> params) {
        try {
            User userInfo = JwtHelper.getUserInfo();
            Integer cardId = Integer.parseInt(params.get("cardId").toString());
            
            boolean success = bankCardInfoService.deleteBankCard(userInfo.getId(), cardId);
            
            if (success) {
                return Result.success("银行卡删除成功");
            } else {
                return Result.failure("银行卡删除失败");
            }
        } catch (Exception e) {
            log.error("删除银行卡失败: {}", e.getMessage(), e);
            return Result.failure("删除银行卡失败：" + e.getMessage());
        }
    }

    /**
     * 更新银行卡信息
     * @param params 银行卡信息参数
     * @return 更新结果
     */
    @RequestMapping("/update")
    public Result<String> updateBankCard(@RequestBody Map<String, Object> params) {
        try {
            User userInfo = JwtHelper.getUserInfo();
            Integer cardId = Integer.parseInt(params.get("cardId").toString());
            
            // 验证参数
            bankCardInfoService.validateBankCardInfo(params);
            
            // 获取现有银行卡信息
            BankCardInfo existingCard = bankCardInfoService.getById(cardId);
            if (existingCard == null || !existingCard.getUserId().equals(userInfo.getId())) {
                return Result.failure("银行卡不存在或无权限操作");
            }
            
            // 更新银行卡信息
            existingCard.setBankName((String) params.get("bankName"));
            existingCard.setCardNumber((String) params.get("cardNumber"));
            existingCard.setCardHolder((String) params.get("cardHolder"));
            existingCard.setBankAddress((String) params.get("bankAddress"));
            existingCard.setCardImageUrl((String) params.get("cardImage"));
            
            boolean success = bankCardInfoService.updateById(existingCard);
            
            if (success) {
                return Result.success("银行卡信息更新成功");
            } else {
                return Result.failure("银行卡信息更新失败");
            }
        } catch (IllegalArgumentException e) {
            return Result.failure(e.getMessage());
        } catch (Exception e) {
            log.error("更新银行卡失败: {}", e.getMessage(), e);
            return Result.failure("更新银行卡失败：" + e.getMessage());
        }
    }
}