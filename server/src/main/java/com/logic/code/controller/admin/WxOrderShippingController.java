package com.logic.code.controller.admin;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logic.code.common.response.Result;
import com.logic.code.entity.User;
import com.logic.code.entity.order.Order;
import com.logic.code.entity.order.OrderExpress;
import com.logic.code.entity.order.WxShippingInfo;
import com.logic.code.model.dto.WxShippingInfoDTO;
import com.logic.code.model.vo.WxOrderInfoVO;
import com.logic.code.service.OrderExpressService;
import com.logic.code.service.OrderService;
import com.logic.code.service.UserService;
import com.logic.code.service.WxOrderShippingService;
import com.logic.code.service.WxShippingInfoService;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 微信小程序发货信息管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/admin/wx/shipping")
@Validated
public class WxOrderShippingController {

    @Resource
    private WxOrderShippingService wxOrderShippingService;

    @Resource
    private OrderService orderService;

    @Resource
    private OrderExpressService orderExpressService;

    @Resource
    private WxShippingInfoService wxShippingInfoService;

    @Resource
    private UserService userService;

    /**
     * 发货信息录入
     */
    @PostMapping("/upload")
    public Result<Boolean> uploadShippingInfo(@Validated @RequestBody WxShippingInfoDTO shippingInfoDTO) {
        try {
            boolean success = wxOrderShippingService.uploadShippingInfo(shippingInfoDTO);
            if (success) {
                return Result.success(true, "发货信息录入成功");
            } else {
                return Result.failure("发货信息录入失败");
            }
        } catch (Exception e) {
            log.error("发货信息录入异常", e);
            return Result.failure("发货信息录入异常: " + e.getMessage());
        }
    }

    /**
     * 根据订单ID自动录入发货信息
     */
    @PostMapping("/upload/order/{orderId}")
    public Result<Boolean> uploadShippingInfoByOrderId(@PathVariable("orderId") @NotNull Integer orderId) {
        try {
            // 查询订单信息
            Order order = orderService.queryById(orderId);
            if (order == null) {
                return Result.failure("订单不存在");
            }

            // 查询物流信息
            OrderExpress orderExpress = orderExpressService.queryByOrderId(orderId);
            if (orderExpress == null) {
                return Result.failure("订单物流信息不存在，请先添加物流信息");
            }

            // 构建发货信息DTO
            WxShippingInfoDTO shippingInfoDTO = buildShippingInfoFromOrder(order, orderExpress);

            boolean success = wxOrderShippingService.uploadShippingInfo(shippingInfoDTO);
            if (success) {
                // 更新订单发货状态
                order.setShippingInfoStatus(1);
                order.setShippingTime(new Date());
                orderService.updateNotNull(order);

                return Result.success(true, "发货信息录入成功");
            } else {
                return Result.failure("发货信息录入失败");
            }
        } catch (Exception e) {
            log.error("发货信息录入异常", e);
            return Result.failure("发货信息录入异常: " + e.getMessage());
        }
    }

    /**
     * 查询订单发货状态
     */
    @GetMapping("/order/status")
    public Result<WxOrderInfoVO> getOrderStatus(@RequestParam(required = false) String transactionId,
                                               @RequestParam(required = false) String merchantId,
                                               @RequestParam(required = false) String merchantTradeNo) {
        try {
            WxOrderInfoVO orderInfo = wxOrderShippingService.getOrderInfo(transactionId, merchantId, merchantTradeNo);
            if (orderInfo != null) {
                return Result.success(orderInfo);
            } else {
                return Result.failure("查询订单发货状态失败");
            }
        } catch (Exception e) {
            log.error("查询订单发货状态异常", e);
            return Result.failure("查询订单发货状态异常: " + e.getMessage());
        }
    }

    /**
     * 确认收货提醒
     */
    @PostMapping("/notify/confirm-receive")
    public Result<Boolean> notifyConfirmReceive(@RequestParam(required = false) String transactionId,
                                              @RequestParam(required = false) String merchantId,
                                              @RequestParam(required = false) String merchantTradeNo) {
        try {
            boolean success = wxOrderShippingService.notifyConfirmReceive(transactionId, merchantId,
                    merchantTradeNo, new Date());
            if (success) {
                return Result.success(true, "确认收货提醒发送成功");
            } else {
                return Result.failure("确认收货提醒发送失败");
            }
        } catch (Exception e) {
            log.error("确认收货提醒异常", e);
            return Result.failure("确认收货提醒异常: " + e.getMessage());
        }
    }

    /**
     * 获取发货信息列表
     */
    @GetMapping("/list")
    public Result<Page<WxShippingInfo>> getShippingList(@RequestParam(defaultValue = "1") Integer page,
                                                       @RequestParam(defaultValue = "20") Integer size,
                                                       @RequestParam(required = false) Integer orderId,
                                                       @RequestParam(required = false) String transactionId,
                                                       @RequestParam(required = false) Integer wxStatus) {
        try {
            Page<WxShippingInfo> pageInfo = new Page<>(page, size);

            // 构建查询条件
            QueryWrapper<WxShippingInfo> queryWrapper = new QueryWrapper<>();
            if (orderId != null) {
                queryWrapper.eq("order_id", orderId);
            }
            if (transactionId != null && !transactionId.trim().isEmpty()) {
                queryWrapper.like("transaction_id", transactionId);
            }
            if (wxStatus != null) {
                queryWrapper.eq("wx_status", wxStatus);
            }
            queryWrapper.orderByDesc("create_time");

            Page<WxShippingInfo> result = wxShippingInfoService.selectPage(pageInfo, queryWrapper);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取发货信息列表异常", e);
            return Result.failure("获取发货信息列表异常: " + e.getMessage());
        }
    }

    /**
     * 批量上传发货信息
     */
    @PostMapping("/batch-upload")
    public Result<Boolean> batchUploadShipping(@RequestBody Map<String, Object> params) {
        try {
            Integer orderStatus = (Integer) params.get("orderStatus");
            List<String> dateRange = (List<String>) params.get("dateRange");

            // 查询符合条件的订单
            QueryWrapper<Order> queryWrapper = new QueryWrapper<>();
            if (orderStatus != null) {
                queryWrapper.eq("order_status", orderStatus);
            }
            if (dateRange != null && dateRange.size() == 2) {
                queryWrapper.between("create_time", dateRange.get(0), dateRange.get(1));
            }

            List<Order> orders = orderService.queryList(queryWrapper);
            int successCount = 0;

            for (Order order : orders) {
                try {
                    OrderExpress orderExpress = orderExpressService.queryByOrderId(order.getId());
                    if (orderExpress != null) {
                        WxShippingInfoDTO shippingInfoDTO = buildShippingInfoFromOrder(order, orderExpress);
                        boolean success = wxOrderShippingService.uploadShippingInfo(shippingInfoDTO);
                        if (success) {
                            successCount++;
                            order.setShippingInfoStatus(1);
                            order.setShippingTime(new Date());
                            orderService.updateNotNull(order);
                        }
                    }
                } catch (Exception e) {
                    log.error("批量上传订单{}发货信息失败", order.getId(), e);
                }
            }

            return Result.success(true, String.format("批量上传完成，成功上传%d个订单", successCount));
        } catch (Exception e) {
            log.error("批量上传发货信息异常", e);
            return Result.failure("批量上传发货信息异常: " + e.getMessage());
        }
    }

    /**
     * 设置消息跳转路径
     */
    @PostMapping("/set-jump-path")
    public Result<Boolean> setMsgJumpPath(@RequestParam String path) {
        try {
            boolean success = wxOrderShippingService.setMsgJumpPath(path);
            if (success) {
                return Result.success(true, "消息跳转路径设置成功");
            } else {
                return Result.failure("消息跳转路径设置失败");
            }
        } catch (Exception e) {
            log.error("消息跳转路径设置异常", e);
            return Result.failure("消息跳转路径设置异常: " + e.getMessage());
        }
    }

    /**
     * 查询服务开通状态
     */
    @GetMapping("/service/status")
    public Result<Boolean> getServiceStatus() {
        try {
            Boolean isTradeManaged = wxOrderShippingService.isTradeManaged();
            if (isTradeManaged != null) {
                return Result.success(isTradeManaged);
            } else {
                return Result.failure("查询服务开通状态失败");
            }
        } catch (Exception e) {
            log.error("查询服务开通状态异常", e);
            return Result.failure("查询服务开通状态异常: " + e.getMessage());
        }
    }

    /**
     * 根据订单和物流信息构建发货信息DTO
     */
    private WxShippingInfoDTO buildShippingInfoFromOrder(Order order, OrderExpress orderExpress) {
        WxShippingInfoDTO dto = new WxShippingInfoDTO();

        // 订单信息
        WxShippingInfoDTO.OrderKey orderKey = new WxShippingInfoDTO.OrderKey();
        if (order.getTransactionId() != null) {
            orderKey.setOrderNumberType(2); // 微信支付单号
            orderKey.setTransactionId(order.getTransactionId());
        } else {
            orderKey.setOrderNumberType(1); // 商户订单号
            orderKey.setMchid(order.getMchid());
            orderKey.setOutTradeNo(order.getOrderSn());
        }
        dto.setOrderKey(orderKey);

        // 物流信息
        dto.setLogisticsType(1); // 实体物流配送
        dto.setDeliveryMode(1);  // 统一发货

        // 物流详情
        WxShippingInfoDTO.ShippingItem shippingItem = new WxShippingInfoDTO.ShippingItem();
        shippingItem.setTrackingNo(orderExpress.getLogisticCode());
        shippingItem.setExpressCompany(orderExpress.getShipperCode());
        shippingItem.setItemDesc("订单商品"); // 可以从订单商品中获取更详细的描述

        // 联系方式
        WxShippingInfoDTO.Contact contact = new WxShippingInfoDTO.Contact();
        contact.setReceiverContact(maskPhoneNumber(order.getMobile()));
        shippingItem.setContact(contact);

        dto.setShippingList(java.util.Arrays.asList(shippingItem));

        // 支付者信息
        WxShippingInfoDTO.Payer payer = new WxShippingInfoDTO.Payer();
        // 从用户表中获取openid
        try {
            User user = userService.queryById(order.getUserId());
            if (user != null && user.getWechatOpenId() != null) {
                payer.setOpenid(user.getWechatOpenId());
            } else {
                throw new RuntimeException("用户openid不存在，无法上传发货信息");
            }
        } catch (Exception e) {
            throw new RuntimeException("获取用户openid失败: " + e.getMessage());
        }
        dto.setPayer(payer);

        return dto;
    }

    /**
     * 手机号掩码处理
     */
    private String maskPhoneNumber(String phone) {
        if (phone == null || phone.length() < 4) {
            return phone;
        }
        return phone.substring(0, phone.length() - 4) + "****" + phone.substring(phone.length() - 4);
    }
}
