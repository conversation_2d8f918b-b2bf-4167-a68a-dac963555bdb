package com.logic.code.controller.admin;

import com.logic.code.common.enmus.WeshopWechatResultStatus;
import com.logic.code.common.exception.WeshopWechatException;
import com.logic.code.common.response.Result;
import com.logic.code.service.PromotionCompensationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 推广收益补偿控制器
 * 提供补偿缺失推广收益数据的管理接口
 */
@Slf4j
@RestController
@RequestMapping("/admin/promotion/compensation")
public class PromotionCompensationController {

    @Autowired
    private PromotionCompensationService promotionCompensationService;

    /**
     * 补偿缺失的推广收益数据
     */
    @PostMapping("/compensate-missing")
    public Result<Map<String, Object>> compensateMissingEarnings() {
        try {
            log.info("开始执行推广收益数据补偿");
            Map<String, Object> result = promotionCompensationService.compensateMissingEarnings();
            return Result.success(result);
        } catch (Exception e) {
            log.error("推广收益数据补偿失败: {}", e.getMessage(), e);
            throw new WeshopWechatException(WeshopWechatResultStatus.SYSTEM_ERROR);
        }
    }

    /**
     * 批量补偿缺失的推广收益数据
     * @param batchSize 批处理大小，默认100
     */
    @PostMapping("/compensate-missing-batch")
    public Result<Map<String, Object>> compensateMissingEarningsBatch(
            @RequestParam(defaultValue = "100") int batchSize) {
        try {
            log.info("开始执行批量推广收益数据补偿，批处理大小: {}", batchSize);
            Map<String, Object> result = promotionCompensationService.compensateMissingEarningsBatch(batchSize);
            return Result.success(result);
        } catch (Exception e) {
            log.error("批量推广收益数据补偿失败: {}", e.getMessage(), e);
            throw new WeshopWechatException(WeshopWechatResultStatus.SYSTEM_ERROR);
        }
    }

    /**
     * 更新已确认收货订单的推广收益状态
     */
    @PostMapping("/update-confirmed-status")
    public Result<Map<String, Object>> updateConfirmedEarningsStatus() {
        try {
            log.info("开始更新已确认收货订单的推广收益状态");
            Map<String, Object> result = promotionCompensationService.updateConfirmedEarningsStatus();
            return Result.success(result);
        } catch (Exception e) {
            log.error("推广收益状态更新失败: {}", e.getMessage(), e);
            throw new WeshopWechatException(WeshopWechatResultStatus.SYSTEM_ERROR);
        }
    }

    /**
     * 获取补偿统计信息
     */
    @GetMapping("/stats")
    public Result<Map<String, Object>> getCompensationStats() {
        try {
            Map<String, Object> stats = promotionCompensationService.getMigrationStats();
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取补偿统计信息失败: {}", e.getMessage(), e);
            throw new WeshopWechatException(WeshopWechatResultStatus.SYSTEM_ERROR);
        }
    }
}