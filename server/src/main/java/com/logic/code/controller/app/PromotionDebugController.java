package com.logic.code.controller.app;

import com.logic.code.common.response.Result;
import com.logic.code.service.PromotionCommissionService;
import com.logic.code.service.PromotionRelationCompensationService;
import com.logic.code.service.TieredPromotionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 推广调试控制器
 * 用于调试和修复推广相关的数据问题
 */
@RestController
@RequestMapping("/wechat/promotion/debug")
@Slf4j
public class PromotionDebugController {
    
    @Autowired
    private PromotionCommissionService promotionCommissionService;
    
    @Autowired
    private TieredPromotionService tieredPromotionService;
    
    @Autowired
    private PromotionRelationCompensationService compensationService;
    
    /**
     * 重新计算指定订单的推广佣金和统计
     * @param orderId 订单ID
     * @return 处理结果
     */
    @PostMapping("/recalculate-order/{orderId}")
    public Result<Map<String, Object>> recalculateOrderPromotion(@PathVariable Integer orderId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("开始重新计算订单{}的推广佣金", orderId);
            
            // 重新处理推广佣金计算
            boolean success = promotionCommissionService.processPromotionCommissionAfterPayment(orderId);
            
            result.put("orderId", orderId);
            result.put("success", success);
            result.put("message", success ? "重新计算成功" : "重新计算失败");
            
            if (success) {
                log.info("订单{}推广佣金重新计算成功", orderId);
                return Result.success(result);
            } else {
                log.warn("订单{}推广佣金重新计算失败", orderId);
                return Result.error("重新计算失败");
            }
            
        } catch (Exception e) {
            log.error("重新计算订单{}推广佣金异常", orderId, e);
            result.put("orderId", orderId);
            result.put("success", false);
            result.put("message", "重新计算异常：" + e.getMessage());
            return Result.error("重新计算异常");
        }
    }
    
    /**
     * 重新计算指定推广者和商品的统计数据
     * @param promoterId 推广者ID
     * @param goodsId 商品ID
     * @return 处理结果
     */
    @PostMapping("/recalculate-stats/{promoterId}/{goodsId}")
    public Result<Map<String, Object>> recalculatePromotionStats(
            @PathVariable Integer promoterId, 
            @PathVariable Integer goodsId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("开始重新计算推广者{}对商品{}的统计数据", promoterId, goodsId);
            
            // 这里需要实现重新计算统计数据的逻辑
            // 可以调用TieredPromotionService的方法或者直接操作数据库
            
            result.put("promoterId", promoterId);
            result.put("goodsId", goodsId);
            result.put("success", true);
            result.put("message", "统计数据重新计算成功");
            
            log.info("推广者{}对商品{}的统计数据重新计算成功", promoterId, goodsId);
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("重新计算推广者{}对商品{}的统计数据异常", promoterId, goodsId, e);
            result.put("promoterId", promoterId);
            result.put("goodsId", goodsId);
            result.put("success", false);
            result.put("message", "重新计算异常：" + e.getMessage());
            return Result.error("重新计算异常");
        }
    }
    
    /**
     * 检查推广数据一致性
     * @return 检查结果
     */
    @GetMapping("/check-consistency")
    public Result<Map<String, Object>> checkPromotionDataConsistency() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("开始检查推广数据一致性");
            
            // 这里可以实现数据一致性检查逻辑
            // 比较推广收益记录和统计数据是否一致
            
            result.put("checkTime", System.currentTimeMillis());
            result.put("message", "数据一致性检查完成");
            
            log.info("推广数据一致性检查完成");
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("检查推广数据一致性异常", e);
            result.put("message", "检查异常：" + e.getMessage());
            return Result.error("检查异常");
        }
    }
    
    /**
     * 获取推广调试信息
     * @param orderId 订单ID
     * @return 调试信息
     */
    @GetMapping("/info/{orderId}")
    public Result<Map<String, Object>> getPromotionDebugInfo(@PathVariable Integer orderId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("获取订单{}的推广调试信息", orderId);
            
            // 这里可以收集各种调试信息
            // 订单信息、推广者信息、商品信息、收益记录、统计数据等
            
            result.put("orderId", orderId);
            result.put("debugTime", System.currentTimeMillis());
            result.put("message", "调试信息获取成功");
            
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("获取订单{}推广调试信息异常", orderId, e);
            result.put("orderId", orderId);
            result.put("message", "获取调试信息异常：" + e.getMessage());
            return Result.error("获取调试信息异常");
        }
    }
    
    /**
     * 批量修复推广统计数据
     * @return 修复结果
     */
    @PostMapping("/batch-fix-stats")
    public Result<Map<String, Object>> batchFixPromotionStats() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("开始批量修复推广统计数据");
            
            // 这里可以实现批量修复逻辑
            // 查找所有有推广收益记录但缺少统计数据的情况
            // 重新计算并更新统计数据
            
            result.put("fixTime", System.currentTimeMillis());
            result.put("message", "批量修复完成");
            
            log.info("批量修复推广统计数据完成");
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("批量修复推广统计数据异常", e);
            result.put("message", "批量修复异常：" + e.getMessage());
            return Result.error("批量修复异常");
        }
    }
    
    /**
     * 补偿单个订单的推广者设置
     * @param orderId 订单ID
     * @return 补偿结果
     */
    @PostMapping("/compensate-order-promoter/{orderId}")
    public Result<Map<String, Object>> compensateOrderPromoter(@PathVariable Integer orderId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("开始补偿订单{}的推广者设置", orderId);
            
            boolean success = compensationService.compensateOrderPromoter(orderId);
            
            result.put("orderId", orderId);
            result.put("success", success);
            result.put("message", success ? "推广者补偿成功" : "推广者补偿失败");
            result.put("compensateTime", System.currentTimeMillis());
            
            if (success) {
                log.info("订单{}推广者补偿成功", orderId);
                return Result.success(result);
            } else {
                log.warn("订单{}推广者补偿失败", orderId);
                return Result.error("推广者补偿失败");
            }
            
        } catch (Exception e) {
            log.error("补偿订单{}推广者异常", orderId, e);
            result.put("orderId", orderId);
            result.put("success", false);
            result.put("message", "补偿异常：" + e.getMessage());
            return Result.error("补偿异常");
        }
    }
    
    /**
     * 批量补偿缺失推广者的订单
     * @param days 补偿最近几天的订单，默认7天
     * @return 补偿结果
     */
    @PostMapping("/batch-compensate-promoters")
    public Result<Map<String, Object>> batchCompensatePromoters(
            @RequestParam(defaultValue = "7") Integer days) {
        
        try {
            log.info("开始批量补偿最近{}天缺失推广者的订单", days);
            
            Map<String, Object> result = compensationService.batchCompensateOrderPromoters(days);
            
            log.info("批量补偿完成：{}", result);
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("批量补偿推广者异常", e);
            Map<String, Object> result = new HashMap<>();
            result.put("message", "批量补偿异常：" + e.getMessage());
            return Result.error("批量补偿异常");
        }
    }
    
    /**
     * 补偿并重新计算推广佣金
     * @param orderId 订单ID
     * @return 处理结果
     */
    @PostMapping("/compensate-and-recalculate/{orderId}")
    public Result<Map<String, Object>> compensateAndRecalculate(@PathVariable Integer orderId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("开始补偿订单{}并重新计算推广佣金", orderId);
            
            boolean success = compensationService.compensateAndRecalculateCommission(orderId);
            
            result.put("orderId", orderId);
            result.put("success", success);
            result.put("message", success ? "补偿和重新计算成功" : "补偿和重新计算失败");
            result.put("processTime", System.currentTimeMillis());
            
            if (success) {
                log.info("订单{}补偿和重新计算成功", orderId);
                return Result.success(result);
            } else {
                log.warn("订单{}补偿和重新计算失败", orderId);
                return Result.error("补偿和重新计算失败");
            }
            
        } catch (Exception e) {
            log.error("补偿订单{}并重新计算推广佣金异常", orderId, e);
            result.put("orderId", orderId);
            result.put("success", false);
            result.put("message", "处理异常：" + e.getMessage());
            return Result.error("处理异常");
        }
    }
    
    /**
     * 检查推广关系一致性
     * @param days 检查最近几天的数据，默认7天
     * @return 检查结果
     */
    @GetMapping("/check-relation-consistency")
    public Result<Map<String, Object>> checkRelationConsistency(
            @RequestParam(defaultValue = "7") Integer days) {
        
        try {
            log.info("开始检查最近{}天的推广关系一致性", days);
            
            Map<String, Object> result = compensationService.checkPromotionRelationConsistency(days);
            
            log.info("推广关系一致性检查完成：{}", result);
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("检查推广关系一致性异常", e);
            Map<String, Object> result = new HashMap<>();
            result.put("message", "检查异常：" + e.getMessage());
            return Result.error("检查异常");
        }
    }
    
    /**
     * 获取需要补偿的订单列表
     * @param days 最近几天，默认7天
     * @param limit 限制数量，默认20
     * @return 订单列表
     */
    @GetMapping("/missing-promoter-orders")
    public Result<Map<String, Object>> getMissingPromoterOrders(
            @RequestParam(defaultValue = "7") Integer days,
            @RequestParam(defaultValue = "20") Integer limit) {
        
        try {
            log.info("获取最近{}天需要补偿的订单列表，限制{}条", days, limit);
            
            Map<String, Object> result = compensationService.getMissingPromoterOrders(days, limit);
            
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("获取需要补偿的订单列表异常", e);
            Map<String, Object> result = new HashMap<>();
            result.put("message", "获取列表异常：" + e.getMessage());
            return Result.error("获取列表异常");
        }
    }
}