package com.logic.code.controller.app;

import com.logic.code.common.response.Result;
import com.logic.code.common.utils.JwtHelper;
import com.logic.code.entity.User;
import com.logic.code.model.query.AdminUserQuery;
import com.logic.code.model.vo.AdminUserListVO;
import com.logic.code.service.UserService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 管理员用户管理控制器
 * <AUTHOR>
 * @date 2025/1/27
 */
@RestController
@RequestMapping("/wechat/admin/user")
public class AdminUserController {

    @Resource
    private UserService userService;

    /**
     * 获取用户列表（管理员功能）
     */
    @RequestMapping("/list2")
    public Result<AdminUserListVO> getUserList(AdminUserQuery query) {
        try {
            // 检查管理员权限
            User currentUser = JwtHelper.getUserInfo();
            if (currentUser.getUserLevelId() == null || currentUser.getUserLevelId() != 1) {
                return Result.failure("权限不足");
            }

            AdminUserListVO result = userService.getAdminUserList(query);
            return Result.success(result);
        } catch (Exception e) {
            return Result.failure("获取用户列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户详情（管理员功能）
     */
    @RequestMapping("/detail")
    public Result<User> getUserDetail(Long userId) {
        try {
            // 检查管理员权限
            User currentUser = JwtHelper.getUserInfo();
            if (currentUser.getUserLevelId() == null || currentUser.getUserLevelId() != 1) {
                return Result.failure("权限不足");
            }

            User user = userService.queryById(userId);
            if (user == null) {
                return Result.failure("用户不存在");
            }

            return Result.success(user);
        } catch (Exception e) {
            return Result.failure("获取用户详情失败：" + e.getMessage());
        }
    }

    /**
     * 获取管理员统计数据
     */
    @RequestMapping("/stats")
    public Result<java.util.Map<String, Object>> getAdminStats() {
        try {
            // 检查管理员权限
            User currentUser = JwtHelper.getUserInfo();
            if (currentUser.getUserLevelId() == null || currentUser.getUserLevelId() != 1) {
                return Result.failure("权限不足");
            }

            java.util.Map<String, Object> stats = userService.getAdminStats();
            return Result.success(stats);
        } catch (Exception e) {
            return Result.failure("获取统计数据失败：" + e.getMessage());
        }
    }
}