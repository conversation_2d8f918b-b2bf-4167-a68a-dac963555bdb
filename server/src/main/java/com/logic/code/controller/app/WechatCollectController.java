package com.logic.code.controller.app;

import com.logic.code.common.response.Result;
import com.logic.code.common.utils.JwtHelper;
import com.logic.code.entity.User;
import com.logic.code.model.vo.CollectAddOrDeleteParamVO;
import com.logic.code.model.vo.CollectAddOrDeleteResultVO;
import com.logic.code.service.CollectService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/wechat/collect")
public class WechatCollectController {


    @Resource
    private CollectService collectService;

    @PostMapping("/add-or-delete")
    public Result<CollectAddOrDeleteResultVO> addOrDelete(@RequestBody CollectAddOrDeleteParamVO collectAddOrDeleteParamDTO) {
        return Result.success(collectService.addOrDelete(collectAddOrDeleteParamDTO));
    }

    @GetMapping("/list")
    public Result queryList(Integer typeId) {
        User userInfo = JwtHelper.getUserInfo();
        return Result.success(collectService.queryGoodsCollectList(userInfo.getId()));
    }

}
