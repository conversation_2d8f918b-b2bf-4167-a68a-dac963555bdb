package com.logic.code.controller.app;

import com.logic.code.common.response.Result;
import com.logic.code.entity.User;
import com.logic.code.model.vo.LoginAuthParamVO;
import com.logic.code.model.vo.LoginAuthResultVO;
import com.logic.code.model.vo.WechatPhoneParamVO;
import com.logic.code.model.vo.WechatPhoneResultVO;
import com.logic.code.service.WechatAuthService;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 微信认证控制器
 */
@RestController
@RequestMapping("/wechat/auth")
public class WechatAuthController {

    @Autowired
    private WechatAuthService wechatAuthService;

    /**
     * 微信登录
     * @param paramVO 登录参数
     * @return 登录结果
     * @throws WxErrorException 微信异常
     */
    @RequestMapping("/login")
    public Result<LoginAuthResultVO> loginByWeixin(@RequestBody LoginAuthParamVO paramVO) throws WxErrorException {
        return Result.success(wechatAuthService.login(paramVO));
    }

    /**
     * 获取微信绑定的手机号
     * @param phoneParamVO 包含获取手机号所需的参数
     * @return 手机号信息
     * @throws WxErrorException 微信异常
     */
    @RequestMapping("/bindMobile")
    public Result<WechatPhoneResultVO> bindMobile(@RequestBody @Validated WechatPhoneParamVO phoneParamVO) throws WxErrorException {
        return Result.success(wechatAuthService.getPhoneNumber(phoneParamVO));
    }

    /**
     * 测试接口
     * @return 测试结果
     */
    @RequestMapping("/test")
    public Result<String> test(){
        return Result.success("test");
    }
}
