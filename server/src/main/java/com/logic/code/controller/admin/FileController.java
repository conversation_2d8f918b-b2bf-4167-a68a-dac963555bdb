package com.logic.code.controller.admin;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logic.code.common.response.Result;
import com.logic.code.entity.file.FileAttachment;
import com.logic.code.service.FileAttachmentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * File upload controller for admin
 */
@RestController
@RequestMapping("/adminapi/file")
public class FileController {

    private static final Logger logger = LoggerFactory.getLogger(FileController.class);
    
    @Autowired
    private FileAttachmentService fileAttachmentService;
    
    /**
     * Upload file
     * @param file File to upload
     * @param pid Category ID
     * @return Uploaded file info
     */
    @PostMapping("/upload")
    public Result<Map<String, String>> uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "pid", required = false, defaultValue = "0") Integer pid) {
        if (file == null || file.isEmpty()) {
            logger.warn("Upload failed: Empty file received");
            return Result.failure("上传文件不能为空");
        }

        try {
            // For now, use hardcoded user ID and name
            // In a real application, get these from the authenticated user
            Integer userId = 1;
            String userName = "admin";
            
            Map<String, String> result = fileAttachmentService.uploadFile(file, pid, userId, userName);
            return Result.success(result);
        } catch (Exception e) {
            logger.error("File upload failed", e);
            return Result.failure("文件上传失败：" + e.getMessage());
        }
    }
    
    /**
     * Upload online file
     * @param images List of image URLs
     * @param pid Category ID
     * @return Success message
     */
    @PostMapping("/online_upload")
    public Result<String> uploadOnlineFile(
            @RequestBody Map<String, Object> requestBody) {
        try {
            Integer pid = Integer.valueOf(requestBody.get("pid").toString());
            @SuppressWarnings("unchecked")
            List<String> images = (List<String>) requestBody.get("images");
            
            if (images == null || images.isEmpty()) {
                return Result.failure("No images provided");
            }
            
            // For now, use hardcoded user ID and name
            Integer userId = 1;
            String userName = "admin";
            
            for (String url : images) {
                fileAttachmentService.uploadOnlineFile(url, pid, userId, userName);
            }
            
            return Result.success("上传成功");
        } catch (Exception e) {
            logger.error("Online file upload failed", e);
            return Result.failure("在线文件上传失败：" + e.getMessage());
        }
    }
    
    /**
     * Get file list
     * @param page Page number
     * @param limit Page size
     * @param pid Category ID
     * @param keyword Search keyword
     * @return Page of files
     */
    @GetMapping("/file")
    public Result<Page<FileAttachment>> getFileList(
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "limit", defaultValue = "10") Integer limit,
            @RequestParam(value = "pid", required = false) Integer pid,
            @RequestParam(value = "keyword", required = false) String keyword) {
        Page<FileAttachment> fileList = fileAttachmentService.getFileList(page, limit, pid, keyword);
        return Result.success(fileList);
    }
    
    /**
     * Move files to another category
     * @param requestBody Request body containing pid and images
     * @return Success message
     */
    @PutMapping("/file/do_move")
    public Result<String> moveFiles(@RequestBody Map<String, Object> requestBody) {
        try {
            Integer pid = Integer.valueOf(requestBody.get("pid").toString());
            @SuppressWarnings("unchecked")
            List<Integer> images = (List<Integer>) requestBody.get("images");
            
            if (images == null || images.isEmpty()) {
                return Result.failure("No images provided");
            }
            
            boolean success = fileAttachmentService.moveFiles(images, pid);
            if (success) {
                return Result.success("移动成功");
            } else {
                return Result.failure("移动失败");
            }
        } catch (Exception e) {
            logger.error("Move files failed", e);
            return Result.failure("移动文件失败：" + e.getMessage());
        }
    }
    
    /**
     * Update file
     * @param ids File IDs
     * @param requestBody Request body containing name
     * @return Success message
     */
    @PutMapping("/file/update/{ids}")
    public Result<String> updateFile(
            @PathVariable String ids,
            @RequestBody Map<String, String> requestBody) {
        try {
            String name = requestBody.get("name");
            
            if (name == null || name.isEmpty()) {
                return Result.failure("Name is required");
            }
            
            String[] idArray = ids.split(",");
            for (String id : idArray) {
                FileAttachment file = fileAttachmentService.getById(Integer.valueOf(id));
                if (file != null) {
                    file.setName(name);
                    fileAttachmentService.updateFile(file);
                }
            }
            
            return Result.success("更新成功");
        } catch (Exception e) {
            logger.error("Update file failed", e);
            return Result.failure("更新文件失败：" + e.getMessage());
        }
    }
    
    /**
     * Delete files
     * @param ids File IDs
     * @return Success message
     */
    @PostMapping("/file/delete")
    public Result<String> deleteFiles(@RequestBody List<Integer> ids) {
        try {
            if (ids == null || ids.isEmpty()) {
                return Result.failure("No files provided");
            }
            
            for (Integer id : ids) {
                fileAttachmentService.deleteFile(id);
            }
            
            return Result.success("删除成功");
        } catch (Exception e) {
            logger.error("Delete files failed", e);
            return Result.failure("删除文件失败：" + e.getMessage());
        }
    }
}
