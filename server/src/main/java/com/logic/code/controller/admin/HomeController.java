package com.logic.code.controller.admin;

import com.logic.code.common.response.Result;
import com.logic.code.service.StatisticService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 管理后台首页控制器
 * <AUTHOR>
 */
@RestController
@RequestMapping("/adminapi/home")
public class HomeController {

    @Autowired
    private StatisticService statisticService;

    /**
     * 首页头部统计数据
     * @return 头部统计数据
     */
    @GetMapping("/header")
    public Result<Map<String, Object>> header() {
        Map<String, Object> result = new HashMap<>();
        
        // 获取今天的日期
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        
        // 获取基础统计数据
        Map<String, Object> basicStats = statisticService.getProductBasicStatistics(today);
        
        // 转换为前端需要的格式
        List<Map<String, Object>> info = new ArrayList<>();
        
        // 支付金额
        Map<String, Object> payPriceInfo = new HashMap<>();
        payPriceInfo.put("title", "支付金额");
        payPriceInfo.put("date", "今日");
        Map<String, Object> payPriceData = (Map<String, Object>) basicStats.get("payPrice");
        payPriceInfo.put("today", payPriceData.get("num"));
        payPriceInfo.put("yesterday", generateRandomValue(10000, 20000));
        payPriceInfo.put("today_ratio", payPriceData.get("percent"));
        payPriceInfo.put("total_name", "总支付金额");
        payPriceInfo.put("total", generateRandomValue(100000, 500000));
        info.add(payPriceInfo);
        
        // 支付订单数
        Map<String, Object> payInfo = new HashMap<>();
        payInfo.put("title", "支付订单数");
        payInfo.put("date", "今日");
        Map<String, Object> payData = (Map<String, Object>) basicStats.get("pay");
        payInfo.put("today", payData.get("num"));
        payInfo.put("yesterday", generateRandomValue(50, 100));
        payInfo.put("today_ratio", payData.get("percent"));
        payInfo.put("total_name", "总订单数");
        payInfo.put("total", generateRandomValue(1000, 5000));
        info.add(payInfo);
        
        // 新增用户
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("title", "新增用户");
        userInfo.put("date", "今日");
        Map<String, Object> userData = (Map<String, Object>) basicStats.get("user");
        userInfo.put("today", userData.get("num"));
        userInfo.put("yesterday", generateRandomValue(100, 200));
        userInfo.put("today_ratio", userData.get("percent"));
        userInfo.put("total_name", "总用户数");
        userInfo.put("total", generateRandomValue(10000, 50000));
        info.add(userInfo);
        
        // 商品浏览量
        Map<String, Object> browseInfo = new HashMap<>();
        browseInfo.put("title", "商品浏览量");
        browseInfo.put("date", "今日");
        Map<String, Object> browseData = (Map<String, Object>) basicStats.get("browse");
        browseInfo.put("today", browseData.get("num"));
        browseInfo.put("yesterday", generateRandomValue(2000, 4000));
        browseInfo.put("today_ratio", browseData.get("percent"));
        browseInfo.put("total_name", "总浏览量");
        browseInfo.put("total", generateRandomValue(100000, 1000000));
        info.add(browseInfo);
        
        result.put("info", info);
        return Result.success(result);
    }

    /**
     * 首页订单图表数据
     * @param cycle 时间周期参数
     * @return 订单图表数据
     */
    @GetMapping("/order")
    public Result<Map<String, Object>> order(@RequestParam(required = false) String[] cycle) {
        Map<String, Object> result = new HashMap<>();
        
        // 生成模拟的图表数据
        List<String> xAxis = Arrays.asList("00:00", "02:00", "04:00", "06:00", "08:00", "10:00", 
                                          "12:00", "14:00", "16:00", "18:00", "20:00", "22:00");
        
        List<Map<String, Object>> series = new ArrayList<>();
        
        // 订单金额数据
        Map<String, Object> amountSeries = new HashMap<>();
        amountSeries.put("name", "订单金额");
        amountSeries.put("type", "line");
        amountSeries.put("yAxisIndex", 0);
        List<Integer> amountData = Arrays.asList(1200, 1800, 900, 2100, 3200, 4500, 
                                               5200, 4800, 3900, 3200, 2800, 2100);
        amountSeries.put("data", amountData);
        series.add(amountSeries);
        
        // 订单数量数据
        Map<String, Object> countSeries = new HashMap<>();
        countSeries.put("name", "订单数量");
        countSeries.put("type", "line");
        countSeries.put("yAxisIndex", 1);
        List<Integer> countData = Arrays.asList(12, 18, 9, 21, 32, 45, 52, 48, 39, 32, 28, 21);
        countSeries.put("data", countData);
        series.add(countSeries);
        
        result.put("xAxis", xAxis);
        result.put("series", series);
        
        return Result.success(result);
    }

    /**
     * 首页用户图表数据
     * @return 用户图表数据
     */
    @GetMapping("/user")
    public Result<Map<String, Object>> user() {
        Map<String, Object> result = new HashMap<>();
        
        // 生成用户趋势数据
        List<String> xAxis = Arrays.asList("1日", "2日", "3日", "4日", "5日", "6日", "7日");
        List<Integer> series = Arrays.asList(120, 132, 101, 134, 90, 230, 210);
        
        result.put("xAxis", xAxis);
        result.put("series", series);
        
        // 用户分布饼图数据
        List<Map<String, Object>> bingData = new ArrayList<>();
        Map<String, Object> newUser = new HashMap<>();
        newUser.put("name", "新用户");
        newUser.put("value", 335);
        bingData.add(newUser);
        
        Map<String, Object> oldUser = new HashMap<>();
        oldUser.put("name", "老用户");
        oldUser.put("value", 679);
        bingData.add(oldUser);
        
        result.put("bing_data", bingData);
        result.put("bing_xdata", Arrays.asList("新用户", "老用户"));
        
        return Result.success(result);
    }

    /**
     * 首页商品交易额排行
     * @return 商品排行数据
     */
    @GetMapping("/rank")
    public Result<Map<String, Object>> rank() {
        Map<String, Object> result = new HashMap<>();
        
        // 获取商品排行数据
        Map<String, Object> rankData = statisticService.getProductRankingStatistics("today", 1, 10);
        
        result.put("list", rankData.get("list"));
        result.put("count", rankData.get("count"));
        
        return Result.success(result);
    }

    /**
     * 生成随机数值
     */
    private int generateRandomValue(int min, int max) {
        return (int) (Math.random() * (max - min + 1)) + min;
    }
}
