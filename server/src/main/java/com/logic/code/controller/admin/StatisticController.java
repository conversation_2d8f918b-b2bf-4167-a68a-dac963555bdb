package com.logic.code.controller.admin;

import com.logic.code.common.response.Result;
import com.logic.code.service.StatisticService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 统计数据控制器
 * <AUTHOR>
 * @date 2025/1/5
 */
@RestController
@RequestMapping("/adminapi/statistic")
public class StatisticController {

    @Autowired
    private StatisticService statisticService;

    /**
     * 获取商品统计基础数据
     * @param data 时间范围参数，格式：yyyy/MM/dd-yyyy/MM/dd
     * @return 商品统计基础数据
     */
    @GetMapping("/product/get_basic")
    public Result<Map<String, Object>> getProductBasic(@RequestParam(value = "data", required = false) String data) {
        try {
            Map<String, Object> result = statisticService.getProductBasicStatistics(data);
            return Result.success(result);
        } catch (Exception e) {
            return Result.failure("获取商品统计数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取商品统计趋势数据
     * @param data 时间范围参数，格式：yyyy/MM/dd-yyyy/MM/dd
     * @return 商品统计趋势数据
     */
    @GetMapping("/product/get_trend")
    public Result<Map<String, Object>> getProductTrend(@RequestParam(value = "data", required = false) String data) {
        try {
            Map<String, Object> result = statisticService.getProductTrendStatistics(data);
            return Result.success(result);
        } catch (Exception e) {
            return Result.failure("获取商品趋势数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取商品排行数据
     * @param data 时间范围参数
     * @param page 页码
     * @param limit 每页数量
     * @return 商品排行数据
     */
    @GetMapping("/product/get_product_ranking")
    public Result<Map<String, Object>> getProductRanking(
            @RequestParam(value = "data", required = false) String data,
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "limit", defaultValue = "10") Integer limit) {
        try {
            Map<String, Object> result = statisticService.getProductRankingStatistics(data, page, limit);
            return Result.success(result);
        } catch (Exception e) {
            return Result.failure("获取商品排行数据失败：" + e.getMessage());
        }
    }

    /**
     * 导出商品统计数据
     * @param data 时间范围参数
     * @return 导出文件URL
     */
    @GetMapping("/product/get_excel")
    public Result<Map<String, Object>> exportProductStatistics(@RequestParam(value = "data", required = false) String data) {
        try {
            Map<String, Object> result = statisticService.exportProductStatistics(data);
            return Result.success(result);
        } catch (Exception e) {
            return Result.failure("导出商品统计数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户统计基础数据
     * @param data 时间范围参数
     * @param channelType 渠道类型
     * @return 用户统计基础数据
     */
    @GetMapping("/user/get_basic")
    public Result<Map<String, Object>> getUserBasic(
            @RequestParam(value = "data", required = false) String data,
            @RequestParam(value = "channel_type", required = false) String channelType) {
        try {
            Map<String, Object> result = statisticService.getUserBasicStatistics(data, channelType);
            return Result.success(result);
        } catch (Exception e) {
            return Result.failure("获取用户统计数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户统计趋势数据
     * @param data 时间范围参数
     * @param channelType 渠道类型
     * @return 用户统计趋势数据
     */
    @GetMapping("/user/get_trend")
    public Result<Map<String, Object>> getUserTrend(
            @RequestParam(value = "data", required = false) String data,
            @RequestParam(value = "channel_type", required = false) String channelType) {
        try {
            Map<String, Object> result = statisticService.getUserTrendStatistics(data, channelType);
            return Result.success(result);
        } catch (Exception e) {
            return Result.failure("获取用户趋势数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取微信用户统计数据
     * @param data 时间范围参数
     * @param channelType 渠道类型
     * @return 微信用户统计数据
     */
    @GetMapping("/user/get_wechat")
    public Result<Map<String, Object>> getWechatStatistics(
            @RequestParam(value = "data", required = false) String data,
            @RequestParam(value = "channel_type", required = false) String channelType) {
        try {
            Map<String, Object> result = statisticService.getWechatStatistics(data, channelType);
            return Result.success(result);
        } catch (Exception e) {
            return Result.failure("获取微信用户统计数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取微信用户趋势数据
     * @param data 时间范围参数
     * @param channelType 渠道类型
     * @return 微信用户趋势数据
     */
    @GetMapping("/user/get_wechat_trend")
    public Result<Map<String, Object>> getWechatTrend(
            @RequestParam(value = "data", required = false) String data,
            @RequestParam(value = "channel_type", required = false) String channelType) {
        try {
            Map<String, Object> result = statisticService.getWechatTrendStatistics(data, channelType);
            return Result.success(result);
        } catch (Exception e) {
            return Result.failure("获取微信用户趋势数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户地域分布数据
     * @param data 时间范围参数
     * @param channelType 渠道类型
     * @return 用户地域分布数据
     */
    @GetMapping("/user/get_region")
    public Result<Map<String, Object>> getUserRegion(
            @RequestParam(value = "data", required = false) String data,
            @RequestParam(value = "channel_type", required = false) String channelType) {
        try {
            Map<String, Object> result = statisticService.getUserRegionStatistics(data, channelType);
            return Result.success(result);
        } catch (Exception e) {
            return Result.failure("获取用户地域分布数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户性别分布数据
     * @param data 时间范围参数
     * @param channelType 渠道类型
     * @return 用户性别分布数据
     */
    @GetMapping("/user/get_sex")
    public Result<Map<String, Object>> getUserSex(
            @RequestParam(value = "data", required = false) String data,
            @RequestParam(value = "channel_type", required = false) String channelType) {
        try {
            Map<String, Object> result = statisticService.getUserSexStatistics(data, channelType);
            return Result.success(result);
        } catch (Exception e) {
            return Result.failure("获取用户性别分布数据失败：" + e.getMessage());
        }
    }

    /**
     * 导出用户统计数据
     * @param data 时间范围参数
     * @param channelType 渠道类型
     * @return 导出文件URL
     */
    @GetMapping("/user/get_excel")
    public Result<Map<String, Object>> exportUserStatistics(
            @RequestParam(value = "data", required = false) String data,
            @RequestParam(value = "channel_type", required = false) String channelType) {
        try {
            Map<String, Object> result = statisticService.exportUserStatistics(data, channelType);
            return Result.success(result);
        } catch (Exception e) {
            return Result.failure("导出用户统计数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取交易统计数据
     * @param data 时间范围参数
     * @return 交易统计数据
     */
    @GetMapping("/trade/top_trade")
    public Result<Map<String, Object>> getTopTrade(@RequestParam(value = "data", required = false) String data) {
        try {
            Map<String, Object> result = statisticService.getTopTradeStatistics(data);
            return Result.success(result);
        } catch (Exception e) {
            return Result.failure("获取交易统计数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取交易概况数据
     * @param data 时间范围参数
     * @return 交易概况数据
     */
    @GetMapping("/trade/bottom_trade")
    public Result<Map<String, Object>> getBottomTrade(@RequestParam(value = "data", required = false) String data) {
        try {
            Map<String, Object> result = statisticService.getBottomTradeStatistics(data);
            return Result.success(result);
        } catch (Exception e) {
            return Result.failure("获取交易概况数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取订单统计基础数据
     * @param data 时间范围参数
     * @return 订单统计基础数据
     */
    @GetMapping("/order/get_basic")
    public Result<Map<String, Object>> getOrderBasic(@RequestParam(value = "data", required = false) String data) {
        try {
            Map<String, Object> result = statisticService.getOrderBasicStatistics(data);
            return Result.success(result);
        } catch (Exception e) {
            return Result.failure("获取订单统计数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取订单统计趋势数据
     * @param data 时间范围参数
     * @return 订单统计趋势数据
     */
    @GetMapping("/order/get_trend")
    public Result<Map<String, Object>> getOrderTrend(@RequestParam(value = "data", required = false) String data) {
        try {
            Map<String, Object> result = statisticService.getOrderTrendStatistics(data);
            return Result.success(result);
        } catch (Exception e) {
            return Result.failure("获取订单趋势数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取订单来源分析数据
     * @param data 时间范围参数
     * @return 订单来源分析数据
     */
    @GetMapping("/order/get_channel")
    public Result<Map<String, Object>> getOrderChannel(@RequestParam(value = "data", required = false) String data) {
        try {
            Map<String, Object> result = statisticService.getOrderChannelStatistics(data);
            return Result.success(result);
        } catch (Exception e) {
            return Result.failure("获取订单来源分析数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取订单类型分析数据
     * @param data 时间范围参数
     * @return 订单类型分析数据
     */
    @GetMapping("/order/get_type")
    public Result<Map<String, Object>> getOrderType(@RequestParam(value = "data", required = false) String data) {
        try {
            Map<String, Object> result = statisticService.getOrderTypeStatistics(data);
            return Result.success(result);
        } catch (Exception e) {
            return Result.failure("获取订单类型分析数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取实时订单数据
     * @return 实时订单数据
     */
    @GetMapping("/order/get_realtime")
    public Result<Map<String, Object>> getOrderRealtime() {
        try {
            Map<String, Object> result = statisticService.getOrderRealtimeStatistics();
            return Result.success(result);
        } catch (Exception e) {
            return Result.failure("获取实时订单数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取订单状态分析数据
     * @param data 时间范围参数
     * @return 订单状态分析数据
     */
    @GetMapping("/order/get_status")
    public Result<Map<String, Object>> getOrderStatus(@RequestParam(value = "data", required = false) String data) {
        try {
            Map<String, Object> result = statisticService.getOrderStatusStatistics(data);
            return Result.success(result);
        } catch (Exception e) {
            return Result.failure("获取订单状态分析数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取热销商品排行数据
     * @param data 时间范围参数
     * @param limit 返回数量限制
     * @return 热销商品排行数据
     */
    @GetMapping("/order/get_hot_products")
    public Result<Map<String, Object>> getHotProducts(
            @RequestParam(value = "data", required = false) String data,
            @RequestParam(value = "limit", defaultValue = "10") Integer limit) {
        try {
            Map<String, Object> result = statisticService.getHotProductsStatistics(data, limit);
            return Result.success(result);
        } catch (Exception e) {
            return Result.failure("获取热销商品排行数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取余额统计基础数据
     * @param time 时间范围参数，格式：yyyy/MM/dd-yyyy/MM/dd
     * @return 余额统计基础数据
     */
    @GetMapping("/balance/get_basic")
    public Result<Map<String, Object>> getBalanceBasic(@RequestParam(value = "time", required = false) String time) {
        try {
            Map<String, Object> result = statisticService.getBalanceBasicStatistics(time);
            return Result.success(result);
        } catch (Exception e) {
            return Result.failure("获取余额统计数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取余额统计趋势数据
     * @param time 时间范围参数，格式：yyyy/MM/dd-yyyy/MM/dd
     * @return 余额统计趋势数据
     */
    @GetMapping("/balance/get_trend")
    public Result<Map<String, Object>> getBalanceTrend(@RequestParam(value = "time", required = false) String time) {
        try {
            Map<String, Object> result = statisticService.getBalanceTrendStatistics(time);
            return Result.success(result);
        } catch (Exception e) {
            return Result.failure("获取余额趋势数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取余额来源分析数据
     * @param time 时间范围参数，格式：yyyy/MM/dd-yyyy/MM/dd
     * @return 余额来源分析数据
     */
    @GetMapping("/balance/get_channel")
    public Result<Map<String, Object>> getBalanceChannel(@RequestParam(value = "time", required = false) String time) {
        try {
            Map<String, Object> result = statisticService.getBalanceChannelStatistics(time);
            return Result.success(result);
        } catch (Exception e) {
            return Result.failure("获取余额来源分析数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取余额类型分析数据
     * @param time 时间范围参数，格式：yyyy/MM/dd-yyyy/MM/dd
     * @return 余额类型分析数据
     */
    @GetMapping("/balance/get_type")
    public Result<Map<String, Object>> getBalanceType(@RequestParam(value = "time", required = false) String time) {
        try {
            Map<String, Object> result = statisticService.getBalanceTypeStatistics(time);
            return Result.success(result);
        } catch (Exception e) {
            return Result.failure("获取余额类型分析数据失败：" + e.getMessage());
        }
    }

}
