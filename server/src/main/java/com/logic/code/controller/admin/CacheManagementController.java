package com.logic.code.controller.admin;

import com.logic.code.common.response.Result;
import com.logic.code.service.CacheManagementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.Map;

/**
 * 缓存管理控制器
 * 提供缓存监控和管理的API接口
 * 
 * <AUTHOR>
 * @date 2025/8/4
 */
@RestController
@RequestMapping("/adminapi/cache")
public class CacheManagementController {

    @Autowired
    private CacheManagementService cacheManagementService;

    /**
     * 获取所有缓存名称
     */
    @GetMapping("/names")
    public Result<Collection<String>> getCacheNames() {
        return Result.success(cacheManagementService.getCacheNames());
    }

    /**
     * 获取所有缓存统计信息
     */
    @GetMapping("/stats")
    public Result<Map<String, Map<String, Object>>> getAllCacheStats() {
        return Result.success(cacheManagementService.getAllCacheStats());
    }

    /**
     * 获取指定缓存的详细信息
     */
    @GetMapping("/info/{cacheName}")
    public Result<Map<String, Object>> getCacheInfo(@PathVariable String cacheName) {
        return Result.success(cacheManagementService.getCacheInfo(cacheName));
    }

    /**
     * 清空指定缓存
     */
    @DeleteMapping("/evict/{cacheName}")
    public Result<String> evictCache(@PathVariable String cacheName) {
        cacheManagementService.evictCache(cacheName);
        return Result.success("缓存 " + cacheName + " 已清空");
    }

    /**
     * 清空所有缓存
     */
    @DeleteMapping("/evict-all")
    public Result<String> evictAllCaches() {
        cacheManagementService.evictAllCaches();
        return Result.success("所有缓存已清空");
    }

    /**
     * 清空商品相关缓存
     */
    @DeleteMapping("/evict-goods")
    public Result<String> evictGoodsRelatedCaches() {
        cacheManagementService.evictGoodsRelatedCaches();
        return Result.success("商品相关缓存已清空");
    }

    /**
     * 预热商品缓存
     */
    @PostMapping("/warmup-goods")
    public Result<String> warmUpGoodsCache() {
        cacheManagementService.warmUpGoodsCache();
        return Result.success("商品缓存预热完成");
    }

    /**
     * 检查缓存健康状态
     */
    @GetMapping("/health")
    public Result<Map<String, Object>> checkCacheHealth() {
        return Result.success(cacheManagementService.checkCacheHealth());
    }

    /**
     * 重置所有缓存统计信息
     */
    @PostMapping("/reset-stats")
    public Result<String> resetCacheStats() {
        cacheManagementService.resetCacheStats();
        return Result.success("缓存统计信息已重置");
    }

    /**
     * 重置指定缓存的统计信息
     */
    @PostMapping("/reset-stats/{cacheName}")
    public Result<String> resetCacheStats(@PathVariable String cacheName) {
        cacheManagementService.resetCacheStats(cacheName);
        return Result.success("缓存 " + cacheName + " 的统计信息已重置");
    }
}
