package com.logic.code.controller.app;

import com.github.binarywang.wxpay.bean.result.WxPayUnifiedOrderV3Result;
import com.logic.code.common.response.Result;
import com.logic.code.common.utils.JwtHelper;
import com.logic.code.entity.RechargeRecord;
import com.logic.code.entity.User;
import com.logic.code.service.RechargeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 充值控制器
 * <AUTHOR>
 * @date 2025/7/24
 */
@RestController
@RequestMapping("/wechat/recharge")
@Slf4j
public class RechargeController {

    @Autowired
    private RechargeService rechargeService;

    /**
     * 获取充值配置
     */
    @GetMapping("/config")
    public Result<Map<String, Object>> getRechargeConfig() {
        try {
            Map<String, Object> config = rechargeService.getRechargeConfig();
            return Result.success(config);
        } catch (Exception e) {
            log.error("获取充值配置失败", e);
            return Result.failure("获取充值配置失败：" + e.getMessage());
        }
    }

    /**
     * 创建充值订单
     */
    @PostMapping("/create")
    public Result<RechargeRecord> createRechargeOrder(@RequestBody Map<String, Object> params) {
        try {
            User userInfo = JwtHelper.getUserInfo();
            BigDecimal amount = new BigDecimal(params.get("amount").toString());
            
            if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                return Result.failure("充值金额必须大于0");
            }
            
            if (amount.compareTo(new BigDecimal("10000")) > 0) {
                return Result.failure("单次充值金额不能超过10000元");
            }
            
            RechargeRecord record = rechargeService.createRechargeOrder(userInfo.getId(), amount);
            return Result.success(record);
        } catch (Exception e) {
            log.error("创建充值订单失败", e);
            return Result.failure("创建充值订单失败：" + e.getMessage());
        }
    }

    /**
     * 获取充值预支付信息
     */
    @GetMapping("/prepay")
    public Result<WxPayUnifiedOrderV3Result.JsapiResult> prepayRecharge(@RequestParam("rechargeId") Integer rechargeId) {
        try {
            WxPayUnifiedOrderV3Result.JsapiResult result = rechargeService.prepayRecharge(rechargeId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取充值预支付信息失败", e);
            return Result.failure("获取支付信息失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户充值记录
     */
    @GetMapping("/records")
    public Result<List<RechargeRecord>> getUserRechargeRecords(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        try {
            User userInfo = JwtHelper.getUserInfo();
            List<RechargeRecord> records = rechargeService.getUserRechargeRecords(userInfo.getId(), page, size);
            return Result.success(records);
        } catch (Exception e) {
            log.error("获取用户充值记录失败", e);
            return Result.failure("获取充值记录失败：" + e.getMessage());
        }
    }

    /**
     * 充值支付成功通知（内部调用）
     */
    @PostMapping("/paySuccess")
    public Result<Boolean> handlePaySuccess(@RequestBody Map<String, Object> params) {
        try {
            Integer rechargeId = Integer.valueOf(params.get("rechargeId").toString());
            String wxTransactionId = params.get("wxTransactionId").toString();
            
            boolean success = rechargeService.handleRechargePaySuccess(rechargeId, wxTransactionId);
            return Result.success(success);
        } catch (Exception e) {
            log.error("处理充值支付成功通知失败", e);
            return Result.failure("处理支付通知失败：" + e.getMessage());
        }
    }

    /**
     * 取消充值订单
     */
    @PostMapping("/cancel")
    public Result<Boolean> cancelRechargeOrder(@RequestBody Map<String, Object> params) {
        try {
            User userInfo = JwtHelper.getUserInfo();
            Integer rechargeId = Integer.valueOf(params.get("rechargeId").toString());
            
            boolean success = rechargeService.cancelRechargeOrder(rechargeId, userInfo.getId());
            if (success) {
                return Result.success(true);
            } else {
                return Result.failure("取消充值订单失败");
            }
        } catch (Exception e) {
            log.error("取消充值订单失败", e);
            return Result.failure("取消充值订单失败：" + e.getMessage());
        }
    }
}