package com.logic.code.controller.app;

import com.logic.code.common.exception.WeshopException;
import com.logic.code.common.response.Result;
import com.logic.code.model.vo.*;
import com.logic.code.service.CartService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/wechat/cart")
public class WechatCartController {

    @Autowired
    private CartService cartService;

    @GetMapping("/index")
    public Result<CartResultVO> getCart() {
        return Result.success(cartService.getCart());
    }

    @PostMapping("/add")
    public Result<CartResultVO> addGoodsToCart(@RequestBody @Validated CartParamVO cartParamDTO) {
        cartService.addGoodsToCart(cartParamDTO);
        return Result.success(cartService.getCart());
    }

    @PostMapping("/update")
    public Result<CartResultVO> updateCartGoods(@RequestBody @Validated(CartParamVO.CartUpdateChecks.class) CartParamVO cartParamDTO) {
        cartService.updateGoods(cartParamDTO);
        return Result.success(cartService.getCart());
    }

    @PostMapping("/delete")
    public Result<CartResultVO> deleteCartGoods(@RequestBody @Validated CartGoodsDeleteVO cartGoodsDeleteVO) {
        cartService.deleteCartGoods(cartGoodsDeleteVO);
        return Result.success(cartService.getCart());
    }

    @PostMapping("/checked")
    public Result<CartResultVO> checkedCartGoods(@RequestBody @Validated CartCheckedVO cartCheckedVO) {
        cartService.checkedCartGoods(cartCheckedVO);
        return Result.success(cartService.getCart());
    }

    @GetMapping("/goods-count")
    public Result<Integer> goodsCount() {
        CartResultVO cart = cartService.getCart();
        CartResultVO.CartTotalVO cartTotal = cart.getCartTotal();
        return Result.success(cartTotal.getGoodsCount());
    }

    @GetMapping("/checkout")
    public Result<CartCheckoutVO> checkoutCartGoods(String addressId, Integer couponId) {
        return Result.success(cartService.checkoutCart(addressId, couponId));
    }

    /**
     * 高级结算接口，支持余额和优惠券组合使用
     */
    @PostMapping("/advanced-checkout")
    public Result<CartCheckoutVO> advancedCheckout(@RequestBody AdvancedCheckoutRequest request) {
        try {
            CartCheckoutVO result = cartService.advancedCheckout(
                request.getAddressId(), 
                request.getCouponId(), 
                request.getUseBalance(),
                request.getUsePoints()
            );
            return Result.success(result);
        } catch (Exception e) {
            return Result.failure("结算失败：" + e.getMessage());
        }
    }

    /**
     * 积分结算接口，支持积分抵扣
     */
    @GetMapping("/checkout-with-points")
    public Result<CartCheckoutVO> checkoutWithPoints(String addressId, Integer couponId, Integer usePoints) {
        return Result.success(cartService.checkoutCartWithPoints(addressId, couponId, usePoints));
    }

    /**
     * 高级结算请求参数
     */
    public static class AdvancedCheckoutRequest {
        private String addressId;
        private Integer couponId;
        private java.math.BigDecimal useBalance;
        private Integer usePoints;

        public String getAddressId() {
            return addressId;
        }

        public void setAddressId(String addressId) {
            this.addressId = addressId;
        }

        public Integer getCouponId() {
            return couponId;
        }

        public void setCouponId(Integer couponId) {
            this.couponId = couponId;
        }

        public java.math.BigDecimal getUseBalance() {
            return useBalance;
        }

        public void setUseBalance(java.math.BigDecimal useBalance) {
            this.useBalance = useBalance;
        }

        public Integer getUsePoints() {
            return usePoints;
        }

        public void setUsePoints(Integer usePoints) {
            this.usePoints = usePoints;
        }
    }

}
