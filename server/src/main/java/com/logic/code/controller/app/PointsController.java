package com.logic.code.controller.app;

import com.logic.code.common.response.Result;
import com.logic.code.common.utils.JwtHelper;
import com.logic.code.entity.User;
import com.logic.code.service.PointsService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 积分控制器
 */
@RestController
@RequestMapping("/wechat/points")
public class PointsController {
    
    @Resource
    private PointsService pointsService;
    
    /**
     * 获取用户积分信息
     */
    @GetMapping("/info")
    public Result getUserPointsInfo() {
        User user = JwtHelper.getUserInfo();
        Map<String, Object> pointsInfo = pointsService.getUserPointsInfo(user.getId());
        return Result.success(pointsInfo);
    }
    
    /**
     * 获取用户积分记录
     */
    @GetMapping("/records")
    public Result getUserPointsRecords(@RequestParam(defaultValue = "1") Integer page,
                                     @RequestParam(defaultValue = "20") Integer size) {
        User user = JwtHelper.getUserInfo();
        Map<String, Object> records = pointsService.getUserPointsRecords(user.getId(), page, size);
        return Result.success(records);
    }
    
    /**
     * 计算积分可抵扣金额
     */
    @GetMapping("/calculate")
    public Result calculatePointsValue(@RequestParam Integer points) {
        if (points == null || points <= 0) {
            return Result.success(0);
        }
        
        return Result.success(pointsService.calculatePointsValue(points));
    }
}