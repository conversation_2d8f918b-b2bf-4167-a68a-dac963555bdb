package com.logic.code.controller.admin;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.logic.code.common.response.Result;
import com.logic.code.entity.PointsRecord;
import com.logic.code.entity.User;
import com.logic.code.mapper.PointsRecordMapper;
import com.logic.code.mapper.UserMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 积分统计控制器
 * 提供积分相关的统计分析接口
 */
@RestController
@RequestMapping("/adminapi/marketing")
@Slf4j
public class PointStatisticController {

    @Resource
    private PointsRecordMapper pointsRecordMapper;
    
    @Resource
    private UserMapper userMapper;

    /**
     * 获取积分基础统计数据
     * GET /adminapi/marketing/point/get_basic
     */
    @GetMapping("/point/get_basic")
    public Result<Map<String, Object>> getPointBasic(@RequestParam(value = "time", required = false) String time) {
        try {
            log.info("获取积分基础统计数据 - time: {}", time);
            
            Map<String, Object> result = new HashMap<>();
            
            // 获取当前积分总数
            QueryWrapper<User> userQuery = new QueryWrapper<>();
            userQuery.select("COALESCE(SUM(points), 0) as total_points");
            Map<String, Object> currentPoints = userMapper.selectMaps(userQuery).get(0);
            Integer nowPoint = ((Number) currentPoints.get("total_points")).intValue();
            
            // 获取累计总积分（所有获得的积分）
            QueryWrapper<PointsRecord> earnQuery = new QueryWrapper<>();
            earnQuery.eq("type", "earn");
            if (StringUtils.hasText(time)) {
                addTimeCondition(earnQuery, time);
            }
            earnQuery.select("COALESCE(SUM(points), 0) as total_earn");
            List<Map<String, Object>> earnResult = pointsRecordMapper.selectMaps(earnQuery);
            Integer allPoint = earnResult.isEmpty() ? 0 : ((Number) earnResult.get(0).get("total_earn")).intValue();
            
            // 获取累计消耗积分（所有使用的积分）
            QueryWrapper<PointsRecord> useQuery = new QueryWrapper<>();
            useQuery.eq("type", "use");
            if (StringUtils.hasText(time)) {
                addTimeCondition(useQuery, time);
            }
            useQuery.select("COALESCE(SUM(ABS(points)), 0) as total_use");
            List<Map<String, Object>> useResult = pointsRecordMapper.selectMaps(useQuery);
            Integer payPoint = useResult.isEmpty() ? 0 : ((Number) useResult.get(0).get("total_use")).intValue();
            
            result.put("now_point", nowPoint);
            result.put("all_point", allPoint);
            result.put("pay_point", payPoint);
            
            log.info("积分基础统计 - 当前积分: {}, 累计积分: {}, 消耗积分: {}", nowPoint, allPoint, payPoint);
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("获取积分基础统计数据失败", e);
            return Result.failure("获取统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取积分趋势统计数据
     * GET /adminapi/marketing/point/get_trend
     */
    @GetMapping("/point/get_trend")
    public Result<Map<String, Object>> getPointTrend(@RequestParam(value = "time", required = false) String time) {
        try {
            log.info("获取积分趋势统计数据 - time: {}", time);
            
            Map<String, Object> result = new HashMap<>();
            
            // 解析时间范围
            String[] dateRange = parseDateRange(time);
            String startDate = dateRange[0];
            String endDate = dateRange[1];
            
            // 生成日期序列
            List<String> dateList = generateDateRange(startDate, endDate);
            
            // 获取每日积分获得数据
            Map<String, Integer> earnData = getDailyPointData("earn", startDate, endDate);
            
            // 获取每日积分使用数据
            Map<String, Integer> useData = getDailyPointData("use", startDate, endDate);
            
            // 构建图表数据
            List<String> xAxis = new ArrayList<>();
            List<Integer> earnSeries = new ArrayList<>();
            List<Integer> useSeries = new ArrayList<>();
            
            for (String date : dateList) {
                xAxis.add(date);
                earnSeries.add(earnData.getOrDefault(date, 0));
                useSeries.add(Math.abs(useData.getOrDefault(date, 0))); // 使用绝对值
            }
            
            // 构建series数据
            List<Map<String, Object>> series = new ArrayList<>();
            
            Map<String, Object> earnSeriesData = new HashMap<>();
            earnSeriesData.put("name", "积分获得");
            earnSeriesData.put("data", earnSeries);
            series.add(earnSeriesData);
            
            Map<String, Object> useSeriesData = new HashMap<>();
            useSeriesData.put("name", "积分使用");
            useSeriesData.put("data", useSeries);
            series.add(useSeriesData);
            
            result.put("xAxis", xAxis);
            result.put("series", series);
            
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("获取积分趋势统计数据失败", e);
            return Result.failure("获取趋势数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取积分来源分析数据
     * GET /adminapi/marketing/point/get_channel
     */
    @GetMapping("/point/get_channel")
    public Result<Map<String, Object>> getPointChannel(@RequestParam(value = "time", required = false) String time) {
        try {
            log.info("获取积分来源分析数据 - time: {}", time);
            
            QueryWrapper<PointsRecord> query = new QueryWrapper<>();
            query.eq("type", "earn");
            if (StringUtils.hasText(time)) {
                addTimeCondition(query, time);
            }
            query.select("source, COALESCE(SUM(points), 0) as total_points");
            query.groupBy("source");
            
            List<Map<String, Object>> sourceData = pointsRecordMapper.selectMaps(query);
            
            // 计算总积分
            int totalPoints = sourceData.stream()
                    .mapToInt(item -> ((Number) item.get("total_points")).intValue())
                    .sum();
            
            List<Map<String, Object>> list = new ArrayList<>();
            List<Map<String, Object>> chartData = new ArrayList<>();
            
            for (Map<String, Object> item : sourceData) {
                String source = (String) item.get("source");
                Integer points = ((Number) item.get("total_points")).intValue();
                
                // 计算占比
                double percent = totalPoints > 0 ? (double) points / totalPoints * 100 : 0;
                
                // 表格数据
                Map<String, Object> listItem = new HashMap<>();
                listItem.put("name", getSourceName(source));
                listItem.put("value", points);
                listItem.put("percent", String.format("%.1f", percent));
                list.add(listItem);
                
                // 图表数据
                Map<String, Object> chartItem = new HashMap<>();
                chartItem.put("name", getSourceName(source));
                chartItem.put("value", points);
                chartData.add(chartItem);
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("list", list);
            result.put("data", chartData);
            
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("获取积分来源分析数据失败", e);
            return Result.failure("获取来源分析数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取积分消耗分析数据
     * GET /adminapi/marketing/point/get_type
     */
    @GetMapping("/point/get_type")
    public Result<Map<String, Object>> getPointType(@RequestParam(value = "time", required = false) String time) {
        try {
            log.info("获取积分消耗分析数据 - time: {}", time);
            
            QueryWrapper<PointsRecord> query = new QueryWrapper<>();
            query.eq("type", "use");
            if (StringUtils.hasText(time)) {
                addTimeCondition(query, time);
            }
            query.select("source, COALESCE(SUM(ABS(points)), 0) as total_points");
            query.groupBy("source");
            
            List<Map<String, Object>> sourceData = pointsRecordMapper.selectMaps(query);
            
            // 计算总积分
            int totalPoints = sourceData.stream()
                    .mapToInt(item -> ((Number) item.get("total_points")).intValue())
                    .sum();
            
            List<Map<String, Object>> list = new ArrayList<>();
            List<Map<String, Object>> chartData = new ArrayList<>();
            
            for (Map<String, Object> item : sourceData) {
                String source = (String) item.get("source");
                Integer points = ((Number) item.get("total_points")).intValue();
                
                // 计算占比
                double percent = totalPoints > 0 ? (double) points / totalPoints * 100 : 0;
                
                // 表格数据
                Map<String, Object> listItem = new HashMap<>();
                listItem.put("name", getSourceName(source));
                listItem.put("value", points);
                listItem.put("percent", String.format("%.1f", percent));
                list.add(listItem);
                
                // 图表数据
                Map<String, Object> chartItem = new HashMap<>();
                chartItem.put("name", getSourceName(source));
                chartItem.put("value", points);
                chartData.add(chartItem);
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("list", list);
            result.put("data", chartData);
            
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("获取积分消耗分析数据失败", e);
            return Result.failure("获取消耗分析数据失败: " + e.getMessage());
        }
    }

    // ========== 私有辅助方法 ==========
    
    /**
     * 添加时间查询条件
     */
    private void addTimeCondition(QueryWrapper<PointsRecord> query, String time) {
        if (StringUtils.hasText(time)) {
            String[] dateRange = time.split("-");
            if (dateRange.length == 2) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
                    Date startDate = sdf.parse(dateRange[0].trim());
                    Date endDate = sdf.parse(dateRange[1].trim());
                    
                    // 设置结束时间为当天的23:59:59
                    Calendar cal = Calendar.getInstance();
                    cal.setTime(endDate);
                    cal.set(Calendar.HOUR_OF_DAY, 23);
                    cal.set(Calendar.MINUTE, 59);
                    cal.set(Calendar.SECOND, 59);
                    endDate = cal.getTime();
                    
                    query.between("create_time", startDate, endDate);
                } catch (ParseException e) {
                    log.warn("时间格式解析失败: {}", time, e);
                }
            }
        }
    }
    
    /**
     * 解析日期范围
     */
    private String[] parseDateRange(String time) {
        if (StringUtils.hasText(time)) {
            String[] parts = time.split("-");
            if (parts.length == 2) {
                return new String[]{parts[0].trim(), parts[1].trim()};
            }
        }
        
        // 默认近30天
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(29);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
        
        return new String[]{startDate.format(formatter), endDate.format(formatter)};
    }
    
    /**
     * 生成日期范围列表
     */
    private List<String> generateDateRange(String startDate, String endDate) {
        List<String> dateList = new ArrayList<>();
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
            LocalDate start = LocalDate.parse(startDate, formatter);
            LocalDate end = LocalDate.parse(endDate, formatter);
            
            while (!start.isAfter(end)) {
                dateList.add(start.format(DateTimeFormatter.ofPattern("MM/dd")));
                start = start.plusDays(1);
            }
        } catch (Exception e) {
            log.error("生成日期范围失败", e);
        }
        return dateList;
    }
    
    /**
     * 获取每日积分数据
     */
    private Map<String, Integer> getDailyPointData(String type, String startDate, String endDate) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
            Date start = sdf.parse(startDate);
            Date end = sdf.parse(endDate);
            
            // 设置结束时间为当天的23:59:59
            Calendar cal = Calendar.getInstance();
            cal.setTime(end);
            cal.set(Calendar.HOUR_OF_DAY, 23);
            cal.set(Calendar.MINUTE, 59);
            cal.set(Calendar.SECOND, 59);
            end = cal.getTime();
            
            QueryWrapper<PointsRecord> query = new QueryWrapper<>();
            query.eq("type", type);
            query.between("create_time", start, end);
            query.select("DATE_FORMAT(create_time, '%m/%d') as date_str, " +
                    (type.equals("use") ? "SUM(points)" : "SUM(points)") + " as total_points");
            query.groupBy("DATE_FORMAT(create_time, '%m/%d')");
            
            List<Map<String, Object>> result = pointsRecordMapper.selectMaps(query);
            
            return result.stream().collect(Collectors.toMap(
                    item -> (String) item.get("date_str"),
                    item -> ((Number) item.get("total_points")).intValue()
            ));
        } catch (Exception e) {
            log.error("获取每日积分数据失败", e);
            return new HashMap<>();
        }
    }
    
    /**
     * 获取来源名称
     */
    private String getSourceName(String source) {
        if (source == null) return "未知";
        
        switch (source) {
            case "order":
                return "订单消费";
            case "promotion":
                return "推广奖励";
            case "register":
                return "注册奖励";
            case "sign":
                return "签到奖励";
            case "manual":
                return "手动调整";
            case "refund":
                return "退款返还";
            default:
                return source;
        }
    }
}