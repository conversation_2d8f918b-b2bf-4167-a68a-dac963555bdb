package com.logic.code.controller.admin;

import com.logic.code.common.response.Result;
import com.logic.code.service.UserQueryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 用户管理控制器
 * <AUTHOR>
 * @date 2025/9/5
 */
@RestController
@RequestMapping("/adminapi/user/user")
public class UserManageController {

    @Autowired
    private UserQueryService userQueryService;

    /**
     * 根据User实体字段进行分页查询
     * 
     * @param id 用户ID
     * @param username 用户名
     * @param nickname 昵称
     * @param mobile 手机号
     * @param gender 性别
     * @param promotionLevel 推广等级
     * @param minBalance 最小余额
     * @param maxBalance 最大余额
     * @param minPoints 最小积分
     * @param maxPoints 最大积分
     * @param registerTimeStart 注册开始时间
     * @param registerTimeEnd 注册结束时间
     * @param page 页码
     * @param limit 每页大小
     * @return 查询结果
     */
    @GetMapping("/query")
    public Result<Map<String, Object>> queryUsers(
            @RequestParam(required = false) Integer id,
            @RequestParam(required = false) String username,
            @RequestParam(required = false) String nickname,
            @RequestParam(required = false) String mobile,
            @RequestParam(required = false) String gender,
            @RequestParam(required = false) Integer promotionLevel,
            @RequestParam(required = false) String minBalance,
            @RequestParam(required = false) String maxBalance,
            @RequestParam(required = false) Integer minPoints,
            @RequestParam(required = false) Integer maxPoints,
            @RequestParam(required = false) String registerTimeStart,
            @RequestParam(required = false) String registerTimeEnd,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer limit) {
        
        try {
            // 构建查询参数Map
            Map<String, Object> queryParams = new java.util.HashMap<>();
            queryParams.put("id", id);
            queryParams.put("username", username);
            queryParams.put("nickname", nickname);
            queryParams.put("mobile", mobile);
            queryParams.put("gender", gender);
            queryParams.put("promotionLevel", promotionLevel);
            queryParams.put("minBalance", minBalance);
            queryParams.put("maxBalance", maxBalance);
            queryParams.put("minPoints", minPoints);
            queryParams.put("maxPoints", maxPoints);
            queryParams.put("registerTimeStart", registerTimeStart);
            queryParams.put("registerTimeEnd", registerTimeEnd);
            queryParams.put("page", page);
            queryParams.put("limit", limit);
            
            // 调用服务层方法
            Map<String, Object> result = userQueryService.queryUsersByFields(queryParams);
            
            return Result.success(result);
        } catch (Exception e) {
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @PutMapping("/updateLevel")
    public Result<String> updateUserLevel(@RequestBody Map<String, Object> params) {
        try {
            Integer userId = (Integer) params.get("userId");
            Integer userLevelId = (Integer) params.get("userLevelId");

            if (userId == null || userLevelId == null) {
                return Result.error("参数不能为空");
            }

            // 调用服务层方法更新用户等级
            boolean success = userQueryService.updateUserLevel(userId, userLevelId);

            if (success) {
                return Result.success("更新用户等级成功");
            } else {
                return Result.error("更新用户等级失败");
            }
        } catch (Exception e) {
            return Result.error("更新失败: " + e.getMessage());
        }
    }
}
