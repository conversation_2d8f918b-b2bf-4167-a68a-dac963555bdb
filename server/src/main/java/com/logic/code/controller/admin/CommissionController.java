package com.logic.code.controller.admin;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.logic.code.entity.PromotionEarnings;
import com.logic.code.entity.User;
import com.logic.code.service.PromotionEarningsService;
import com.logic.code.service.UserService;
import com.logic.code.common.response.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 佣金管理控制器
 */
@RestController
@RequestMapping("/adminapi/finance/finance")
@Slf4j
public class CommissionController {

    @Autowired
    private PromotionEarningsService promotionEarningsService;

    @Autowired
    private UserService userService;

    /**
     * 佣金记录列表
     * @param nickname 用户昵称
     * @param priceMin 佣金最小值
     * @param priceMax 佣金最大值
     * @param page 页码
     * @param limit 每页数量
     * @return 佣金记录列表
     */
    @GetMapping("/commission_list")
    public Result<?> getCommissionList(
            @RequestParam(value = "nickname", required = false) String nickname,
            @RequestParam(value = "price_min", required = false) BigDecimal priceMin,
            @RequestParam(value = "price_max", required = false) BigDecimal priceMax,
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "limit", defaultValue = "20") Integer limit) {

        try {
            log.info("查询佣金记录列表：nickname={}, priceMin={}, priceMax={}, page={}, limit={}", 
                    nickname, priceMin, priceMax, page, limit);

            // 构建查询条件
            Map<String, Object> params = new HashMap<>();
            params.put("nickname", nickname);
            params.put("priceMin", priceMin);
            params.put("priceMax", priceMax);
            params.put("page", page);
            params.put("limit", limit);

            // 获取佣金汇总数据
            Map<String, Object> result = getCommissionSummaryData(params);

            return Result.success(result);

        } catch (Exception e) {
            log.error("查询佣金记录列表失败", e);
            return Result.error("查询佣金记录列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户佣金详情
     * @param id 用户ID
     * @return 用户佣金详情
     */
    @GetMapping("/user_info/{id}")
    public Result<?> getUserInfo(@PathVariable Integer id) {
        try {
            log.info("查询用户佣金详情：userId={}", id);

            // 获取用户基本信息
            User user = userService.queryById(id);
            if (user == null) {
                return Result.error("用户不存在");
            }

            // 获取用户推广收益统计
            Map<String, Object> earningsStats = promotionEarningsService.getEarningsStats(id);
            
            // 构建返回数据
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("nickname", user.getNickname());
            userInfo.put("spread_name", getSponsorName(user.getPromoterId())); // 上级推广人
            userInfo.put("number", earningsStats.get("totalEarnings")); // 佣金总收入
            userInfo.put("now_money", user.getBalance()); // 用户余额
            userInfo.put("add_time", user.getRegisterTime()); // 创建时间

            Map<String, Object> result = new HashMap<>();
            result.put("user_info", userInfo);

            return Result.success(result);

        } catch (Exception e) {
            log.error("查询用户佣金详情失败：userId={}", id, e);
            return Result.error("查询用户佣金详情失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户提取记录
     * @param id 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param page 页码
     * @param limit 每页数量
     * @return 提取记录列表
     */
    @GetMapping("/extract_list/{id}")
    public Result<?> getExtractList(
            @PathVariable Integer id,
            @RequestParam(value = "start_time", required = false) String startTime,
            @RequestParam(value = "end_time", required = false) String endTime,
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "limit", defaultValue = "20") Integer limit) {

        try {
            log.info("查询用户提取记录：userId={}, startTime={}, endTime={}, page={}, limit={}", 
                    id, startTime, endTime, page, limit);

            // 构建查询条件
            QueryWrapper<PromotionEarnings> wrapper = new QueryWrapper<>();
            wrapper.eq("promoter_id", id)
                   .in("status", Arrays.asList("confirmed", "pending"));

            // 添加时间范围条件
            if (startTime != null && !startTime.trim().isEmpty()) {
                wrapper.ge("order_create_time", startTime + " 00:00:00");
            }
            if (endTime != null && !endTime.trim().isEmpty()) {
                wrapper.le("order_create_time", endTime + " 23:59:59");
            }

            // 分页查询
            wrapper.orderByDesc("create_time")
                   .last("LIMIT " + ((page - 1) * limit) + ", " + limit);

            List<PromotionEarnings> earningsList = promotionEarningsService.list(wrapper);

            // 查询总数
            QueryWrapper<PromotionEarnings> countWrapper = new QueryWrapper<>();
            countWrapper.eq("promoter_id", id)
                       .in("status", Arrays.asList("confirmed", "pending"));
            if (startTime != null && !startTime.trim().isEmpty()) {
                countWrapper.ge("order_create_time", startTime + " 00:00:00");
            }
            if (endTime != null && !endTime.trim().isEmpty()) {
                countWrapper.le("order_create_time", endTime + " 23:59:59");
            }
            long total = promotionEarningsService.count(countWrapper);

            // 转换数据格式
            List<Map<String, Object>> dataList = earningsList.stream().map(earnings -> {
                Map<String, Object> item = new HashMap<>();
                item.put("number", earnings.getCommissionAmount());
                item.put("_add_time", earnings.getOrderCreateTime());
                item.put("mark", earnings.getDescription());
                return item;
            }).collect(Collectors.toList());

            Map<String, Object> result = new HashMap<>();
            result.put("data", dataList);
            result.put("count", total);

            return Result.success(result);

        } catch (Exception e) {
            log.error("查询用户提取记录失败：userId={}", id, e);
            return Result.error("查询用户提取记录失败：" + e.getMessage());
        }
    }

    /**
     * 获取佣金汇总数据
     */
    private Map<String, Object> getCommissionSummaryData(Map<String, Object> params) {
        String nickname = (String) params.get("nickname");
        BigDecimal priceMin = (BigDecimal) params.get("priceMin");
        BigDecimal priceMax = (BigDecimal) params.get("priceMax");
        Integer page = (Integer) params.get("page");
        Integer limit = (Integer) params.get("limit");

        // 1. 获取所有有推广收益的用户
        QueryWrapper<PromotionEarnings> earningsWrapper = new QueryWrapper<>();
        earningsWrapper.select("DISTINCT promoter_id")
                      .in("status", Arrays.asList("confirmed", "pending"));
        
        List<PromotionEarnings> distinctEarnings = promotionEarningsService.list(earningsWrapper);
        List<Integer> promoterIds = distinctEarnings.stream()
                                                  .map(PromotionEarnings::getPromoterId)
                                                  .distinct()
                                                  .collect(Collectors.toList());

        if (promoterIds.isEmpty()) {
            // 没有推广收益数据
            Map<String, Object> result = new HashMap<>();
            result.put("list", new ArrayList<>());
            result.put("count", 0);
            return result;
        }

        // 2. 查询用户信息并过滤
        QueryWrapper<User> userWrapper = new QueryWrapper<>();
        userWrapper.in("id", promoterIds);
        if (nickname != null && !nickname.trim().isEmpty()) {
            userWrapper.like("nickname", nickname);
        }

        List<User> users = userService.queryList(userWrapper);
        
        // 3. 为每个用户计算佣金统计
        List<Map<String, Object>> userCommissionList = new ArrayList<>();
        
        for (User user : users) {
            Map<String, Object> commissionStats = calculateUserCommissionStats(user.getId());
            
            BigDecimal sumNumber = (BigDecimal) commissionStats.get("sum_number");
            
            // 应用佣金范围过滤
            if (priceMin != null && sumNumber.compareTo(priceMin) < 0) {
                continue;
            }
            if (priceMax != null && sumNumber.compareTo(priceMax) > 0) {
                continue;
            }

            Map<String, Object> userCommission = new HashMap<>();
            userCommission.put("uid", user.getId());
            userCommission.put("nickname", user.getNickname());
            userCommission.put("sum_number", sumNumber);
            userCommission.put("brokerage_price", commissionStats.get("brokerage_price"));
            userCommission.put("extract_price", commissionStats.get("extract_price"));
            userCommission.put("now_money", user.getBalance());

            userCommissionList.add(userCommission);
        }

        // 4. 排序和分页
        userCommissionList.sort((a, b) -> {
            BigDecimal sumA = (BigDecimal) a.get("sum_number");
            BigDecimal sumB = (BigDecimal) b.get("sum_number");
            return sumB.compareTo(sumA); // 降序排列
        });

        int total = userCommissionList.size();
        int start = (page - 1) * limit;
        int end = Math.min(start + limit, total);
        
        List<Map<String, Object>> pageData = userCommissionList.subList(start, end);

        Map<String, Object> result = new HashMap<>();
        result.put("list", pageData);
        result.put("count", total);

        return result;
    }

    /**
     * 计算用户佣金统计
     */
    private Map<String, Object> calculateUserCommissionStats(Integer userId) {
        Map<String, Object> stats = new HashMap<>();

        // 查询用户所有推广收益
        QueryWrapper<PromotionEarnings> wrapper = new QueryWrapper<>();
        wrapper.eq("promoter_id", userId);
        List<PromotionEarnings> allEarnings = promotionEarningsService.list(wrapper);

        // 计算总佣金金额（包括已确认和待确认）
        BigDecimal sumNumber = allEarnings.stream()
                .filter(e -> "confirmed".equals(e.getStatus()) || "pending".equals(e.getStatus()))
                .map(PromotionEarnings::getCommissionAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 计算账户佣金（已确认的）
        BigDecimal brokeragePrice = allEarnings.stream()
                .filter(e -> "confirmed".equals(e.getStatus()) && 
                           (e.getIsWithdrawn() == null || e.getIsWithdrawn() == 0))
                .map(PromotionEarnings::getCommissionAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 计算提现佣金（已提现的）
        BigDecimal extractPrice = allEarnings.stream()
                .filter(e -> "confirmed".equals(e.getStatus()) && 
                           e.getIsWithdrawn() != null && e.getIsWithdrawn() == 1)
                .map(PromotionEarnings::getCommissionAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        stats.put("sum_number", sumNumber);
        stats.put("brokerage_price", brokeragePrice);
        stats.put("extract_price", extractPrice);

        return stats;
    }

    /**
     * 获取推荐人姓名
     */
    private String getSponsorName(Integer spreadId) {
        if (spreadId == null || spreadId == 0) {
            return null;
        }
        
        User sponsor = userService.queryById(spreadId);
        return sponsor != null ? sponsor.getNickname() : null;
    }
}