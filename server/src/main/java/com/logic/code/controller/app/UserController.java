package com.logic.code.controller.app;

import com.logic.code.common.response.Result;
import com.logic.code.common.utils.JwtHelper;
import com.logic.code.entity.User;
import com.logic.code.model.vo.UserInfoVO;
import com.logic.code.model.vo.WechatPhoneResultVO;
import com.logic.code.service.MsgNoticeService;
import com.logic.code.service.UserService;
import com.logic.code.service.PromotionPosterService;
import jakarta.annotation.Resource;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/13 11:15
 * @desc
 */

@RestController
@RequestMapping("/wechat/user")
public class UserController {


    @Resource
    private UserService userService;

    @Resource
    private MsgNoticeService msgNoticeService;

    @Resource
    private PromotionPosterService promotionPosterService;

    @RequestMapping("/info")
    public Result<UserInfoVO> getUserInfo() {
        return Result.success(userService.getUserInfo());
    }

    @RequestMapping("/balance")
    public Result<Map<String, Object>> getUserBalance() {
        try {
            User userInfo = JwtHelper.getUserInfo();
            Map<String, Object> balance = userService.getUserBalance(userInfo.getId());
            return Result.success(balance);
        } catch (Exception e) {
            return Result.failure("获取用户余额失败：" + e.getMessage());
        }
    }




    @RequestMapping("/messageNotice")
    public Result<Boolean> messageNotice(String id) {
        return Result.success(msgNoticeService.insertOrUpdate(id));
    }

    @RequestMapping("/updateNickname")
    public Result<Boolean> updateNickname(String nickname) throws WxErrorException {
        User userInfo = JwtHelper.getUserInfo();
        userInfo.setNickname(nickname);
        userInfo.setUsername(nickname);
        return Result.success(userService.updateById(userInfo) == 1);
    }
    @RequestMapping("/updateAvatar")
    public Result<Boolean> updateAvatar(String avatar) throws WxErrorException {
        User userInfo = JwtHelper.getUserInfo();
        userInfo.setAvatar(avatar);
        return Result.success(userService.updateById(userInfo) == 1);
    }

    @RequestMapping("/generatePromotionQrCode")
    public Result<Map<String, String>> generatePromotionQrCode() {
        try {
            User userInfo = JwtHelper.getUserInfo();
            Map<String, String> qrCodeInfo = userService.generatePromotionQrCode(userInfo.getId());
            return Result.success(qrCodeInfo);
        } catch (Exception e) {
            return Result.failure("生成推广二维码失败：" + e.getMessage());
        }
    }
    @RequestMapping("/generatePromotionQrCodeTM")
    public Result<Map<String, String>> generatePromotionQrCodeTM() {
        try {
            User userInfo = JwtHelper.getUserInfo();
            Map<String, String> qrCodeInfo = userService.generatePromotionQrCodeTM(userInfo.getId());
            return Result.success(qrCodeInfo);
        } catch (Exception e) {
            return Result.failure("生成推广二维码失败：" + e.getMessage());
        }
    }

    @RequestMapping("/getPromotionStats")
    public Result<Map<String, Object>> getPromotionStats(Integer userId) {
        try {
            User userInfo = JwtHelper.getUserInfo();
            if(userId==null){
                userId = userInfo.getId();
            }
            Map<String, Object> stats = userService.getPromotionStats(userId);
            return Result.success(stats);
        } catch (Exception e) {
            return Result.failure("获取推广统计失败：" + e.getMessage());
        }
    }

    @RequestMapping("/getPromotionUserDetail")
    public Result<Map<String, Object>> getPromotionUserDetail(@RequestBody Map<String, Object> params) {
        try {
            User userInfo = JwtHelper.getUserInfo();
            Integer userId = null;
            // 支持两种模式：通过userId获取特定用户详情，或通过搜索条件获取用户列表
            if(params.get("userId")==null){
                userId =  userInfo.getId();
            }else {
                userId = Integer.parseInt(params.get("userId").toString());
            }
            String searchKeyword = params.get("searchKeyword").toString();
            Map<String, Object> userDetail = userService.getPromotionUserDetail(userInfo.getId(), userId,searchKeyword);
            return Result.success(userDetail);
        } catch (Exception e) {
            return Result.failure("获取推广用户详情失败：" + e.getMessage());
        }
    }

    @RequestMapping("/getEarningsStats")
    public Result<Map<String, Object>> getEarningsStats() {
        try {
            User userInfo = JwtHelper.getUserInfo();
            Map<String, Object> stats = userService.getEarningsStats(userInfo.getId());
            return Result.success(stats);
        } catch (Exception e) {
            return Result.failure("获取收益统计失败：" + e.getMessage());
        }
    }

    /**
     * 根据参数查询用户收益统计（支持管理员查看其他用户数据）
     * @param userId 用户ID（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @param status 订单状态（可选）
     * @return 收益统计信息
     */
    @RequestMapping("/queryEarningsStats")
    public Result<Map<String, Object>> queryEarningsStats(
            Integer userId, 
            String startDate, 
            String endDate, 
            String status) {
        try {
            User currentUser = JwtHelper.getUserInfo();
            
            // 获取目标用户ID（如果未指定则默认为当前用户）
            Integer targetUserId = userId != null ? userId : currentUser.getId();
            
            // 权限检查：管理员可以查看所有用户数据，普通用户只能查看自己的数据
           /* if (!targetUserId.equals(currentUser.getId()) &&
                (currentUser.getUserLevelId() == null || currentUser.getUserLevelId() != 1)) {
                return Result.failure("权限不足，无法查看其他用户数据");
            }
            */
            // 构造查询参数
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("userId", targetUserId);
            if (startDate != null && !startDate.isEmpty()) {
                queryParams.put("startDate", startDate);
            }
            if (endDate != null && !endDate.isEmpty()) {
                queryParams.put("endDate", endDate);
            }
            if (status != null && !status.isEmpty()) {
                queryParams.put("status", status);
            }
            
            Map<String, Object> stats = userService.getEarningsStatsWithFilters(queryParams);
            return Result.success(stats);
        } catch (Exception e) {
            return Result.failure("获取收益统计失败：" + e.getMessage());
        }
    }

    @RequestMapping("/submitWithdraw")
    public Result<String> submitWithdraw(@RequestBody Map<String, Object> params) {
        try {
            User userInfo = JwtHelper.getUserInfo();
            String amount = params.get("amount").toString();
            String paymentMethod = params.get("paymentMethod").toString();
            
            // 获取银行卡信息（如果传了的话）
            Map<String, Object> bankInfo = null;
            if (params.containsKey("bankInfo") && params.get("bankInfo") != null) {
                bankInfo = (Map<String, Object>) params.get("bankInfo");
            }

            boolean success = userService.submitWithdrawWithBankInfo(userInfo.getId(), amount, paymentMethod, bankInfo);
            if (success) {
                return Result.success("提现申请已提交");
            } else {
                return Result.failure("提现申请失败");
            }
        } catch (Exception e) {
            return Result.failure("提现申请失败：" + e.getMessage());
        }
    }

    @RequestMapping("/getDailyEarningsStats")
    public Result<Map<String, Object>> getDailyEarningsStats() {
        try {
            User userInfo = JwtHelper.getUserInfo();
            Map<String, Object> stats = userService.getDailyEarningsStats(userInfo.getId());
            return Result.success(stats);
        } catch (Exception e) {
            return Result.failure("获取每日收益统计失败：" + e.getMessage());
        }
    }

    @RequestMapping("/getMonthlyEarningsStats")
    public Result<Map<String, Object>> getMonthlyEarningsStats() {
        try {
            User userInfo = JwtHelper.getUserInfo();
            Map<String, Object> stats = userService.getMonthlyEarningsStats(userInfo.getId());
            return Result.success(stats);
        } catch (Exception e) {
            return Result.failure("获取月度收益统计失败：" + e.getMessage());
        }
    }

    @RequestMapping("/getDayOrderDetail")
    public Result<Map<String, Object>> getDayOrderDetail(@RequestBody Map<String, Object> params) {
        try {
            User userInfo = JwtHelper.getUserInfo();
            String date = params.get("date").toString();
            Map<String, Object> detail = userService.getDayOrderDetail(userInfo.getId(), date);
            return Result.success(detail);
        } catch (Exception e) {
            return Result.failure("获取每日订单明细失败：" + e.getMessage());
        }
    }

    @RequestMapping("/getMonthOrderDetail")
    public Result<Map<String, Object>> getMonthOrderDetail(@RequestBody Map<String, Object> params) {
        try {
            User userInfo = JwtHelper.getUserInfo();
            String month = params.get("month").toString();
            Map<String, Object> detail = userService.getMonthOrderDetail(userInfo.getId(), month);
            return Result.success(detail);
        } catch (Exception e) {
            return Result.failure("获取月度订单明细失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户提现记录
     * @param params 包含分页参数的Map
     * @return 提现记录列表
     */
    @RequestMapping("/getWithdrawRecords")
    public Result<Map<String, Object>> getWithdrawRecords(@RequestBody Map<String, Object> params) {
        try {
            User userInfo = JwtHelper.getUserInfo();
            Integer page = params.get("page") != null ? Integer.parseInt(params.get("page").toString()) : 1;
            Integer size = params.get("size") != null ? Integer.parseInt(params.get("size").toString()) : 20;
            
            Map<String, Object> records = userService.getUserWithdrawRecords(userInfo.getId(), page, size);
            return Result.success(records);
        } catch (Exception e) {
            return Result.failure("获取提现记录失败：" + e.getMessage());
        }
    }

    /**
     * 建立推广关系（用于已登录用户扫码推广二维码的情况）
     * @param params 包含推广者ID的参数
     * @return 建立结果
     */
    @RequestMapping("/establishPromotionRelation")
    public Result<String> establishPromotionRelation(@RequestBody Map<String, Object> params) {
        try {
            User userInfo = JwtHelper.getUserInfo();
            String promotionScene = params.get("promotionScene").toString();

            boolean success = userService.establishPromotionRelationWithLevel(userInfo.getId(), promotionScene);
            if (success) {
                return Result.success("推广关系建立成功");
            } else {
                return Result.failure("推广关系建立失败");
            }
        } catch (Exception e) {
            return Result.failure("建立推广关系失败：" + e.getMessage());
        }
    }

    /**
     * 获取推广海报配置
     * @return 海报配置信息
     */
    @RequestMapping("/getPosterConfig")
    public Result<Map<String, Object>> getPosterConfig() {
        try {
            Map<String, Object> config = promotionPosterService.getPosterConfig();
            return Result.success(config);
        } catch (Exception e) {
            return Result.failure("获取海报配置失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户推广等级信息
     * @return 推广等级信息
     */
    @RequestMapping("/getPromotionLevelInfo")
    public Result<Map<String, Object>> getPromotionLevelInfo() {
        try {
            User userInfo = JwtHelper.getUserInfo();
            Map<String, Object> levelInfo = userService.getUserPromotionLevelInfo(userInfo.getId());
            return Result.success(levelInfo);
        } catch (Exception e) {
            return Result.failure("获取推广等级信息失败：" + e.getMessage());
        }
    }

    /**
     * 获取推广积分记录
     * @param params 包含分页参数的Map
     * @return 推广积分记录
     */
    @RequestMapping("/getPromotionPointsRecords")
    public Result<Map<String, Object>> getPromotionPointsRecords(@RequestBody Map<String, Object> params) {
        try {
            User userInfo = JwtHelper.getUserInfo();
            Integer page = params.get("page") != null ? Integer.parseInt(params.get("page").toString()) : 1;
            Integer size = params.get("size") != null ? Integer.parseInt(params.get("size").toString()) : 20;
            
            Map<String, Object> records = userService.getPromotionPointsRecords(userInfo.getId(), page, size);
            return Result.success(records);
        } catch (Exception e) {
            return Result.failure("获取推广积分记录失败：" + e.getMessage());
        }
    }

    /**
     * 性能测试接口 - 测试getPromotionStats优化效果
     * 仅用于开发测试，生产环境应移除
     */
    @RequestMapping("/performanceTest")
    public Result<Map<String, Object>> performanceTest() {
        try {
            User userInfo = JwtHelper.getUserInfo();
            Map<String, Object> testResult = userService.performanceTest(userInfo.getId());
            return Result.success(testResult);
        } catch (Exception e) {
            return Result.failure("性能测试失败：" + e.getMessage());
        }
    }

    /**
     * 获取区域总监统计信息（管理员专用）
     * @return 区域总监统计信息
     */
    @RequestMapping("/getDirectorStats")
    public Result<Map<String, Object>> getDirectorStats() {
        try {
            User currentUser = JwtHelper.getUserInfo();
            // 检查管理员权限
            if (currentUser.getUserLevelId() == null || currentUser.getUserLevelId() != 1) {
                return Result.failure("权限不足");
            }
            
            Map<String, Object> stats = userService.getDirectorStatistics();
            return Result.success(stats);
        } catch (Exception e) {
            return Result.failure("获取区域总监统计信息失败：" + e.getMessage());
        }
    }


}
