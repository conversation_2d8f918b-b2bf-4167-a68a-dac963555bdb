package com.logic.code.controller.admin;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logic.code.entity.BalanceRecord;
import com.logic.code.entity.User;
import com.logic.code.mapper.BalanceRecordMapper;
import com.logic.code.mapper.UserMapper;
import com.logic.code.service.BalanceService;
import com.logic.code.common.response.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 余额管理控制器
 */
@RestController
@RequestMapping("/adminapi/finance/balance")
@Slf4j
public class BalanceController {

    @Autowired
    private BalanceService balanceService;

    @Autowired
    private BalanceRecordMapper balanceRecordMapper;

    @Autowired
    private UserMapper userMapper;

    /**
     * 余额记录列表
     * @param trading_type 交易类型
     * @param time 时间范围
     * @param keywords 关键词
     * @param page 页码
     * @param limit 每页数量
     * @return 余额记录列表
     */
    @GetMapping("/list")
    public Result<?> getBalanceList(
            @RequestParam(value = "trading_type", required = false) String trading_type,
            @RequestParam(value = "time", required = false) String time,
            @RequestParam(value = "keywords", required = false) String keywords,
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "limit", defaultValue = "20") Integer limit) {

        try {
            // 创建分页对象
            Page<BalanceRecord> pageObj = new Page<>(page, limit);
            
            // 构建查询条件
            QueryWrapper<BalanceRecord> queryWrapper = new QueryWrapper<>();
            
            // 交易类型筛选
            if (trading_type != null && !trading_type.trim().isEmpty()) {
                queryWrapper.eq("type", trading_type);
            }
            
            // 时间范围筛选
            if (time != null && !time.trim().isEmpty()) {
                String[] timeArray = time.split("-");
                if (timeArray.length == 2) {
                    try {
                        LocalDateTime startTime = LocalDateTime.parse(timeArray[0] + " 00:00:00", DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss"));
                        LocalDateTime endTime = LocalDateTime.parse(timeArray[1] + " 23:59:59", DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss"));
                        queryWrapper.between("create_time", startTime, endTime);
                    } catch (Exception e) {
                        log.warn("时间格式解析失败: {}", time);
                    }
                }
            }
            
            // 关键词搜索（搜索用户昵称、描述等）
            if (keywords != null && !keywords.trim().isEmpty()) {
                queryWrapper.and(wrapper -> {
                    // 通过用户昵称搜索
                    List<User> users = userMapper.selectList(
                        new QueryWrapper<User>().like("nickname", keywords)
                                                .or().like("username", keywords)
                    );
                    if (!users.isEmpty()) {
                        List<Integer> userIds = users.stream().map(User::getId).collect(Collectors.toList());
                        wrapper.in("user_id", userIds);
                    }
                    // 或者通过描述搜索
                    wrapper.or().like("description", keywords);
                    // 或者通过备注搜索
                    wrapper.or().like("mark", keywords);
                });
            }
            
            // 按创建时间倒序排列
            queryWrapper.orderByDesc("create_time");
            
            // 执行分页查询
            IPage<BalanceRecord> pageResult = balanceRecordMapper.selectPage(pageObj, queryWrapper);
            
            // 构建返回数据
            List<Map<String, Object>> list = new ArrayList<>();
            for (BalanceRecord record : pageResult.getRecords()) {
                Map<String, Object> item = new HashMap<>();
                item.put("id", record.getId());
                
                // 获取用户信息
                User user = userMapper.selectById(record.getUserId());
                item.put("nickname", user != null ? user.getNickname() : "未知用户");
                
                // 关联订单信息
                String relation = "";
                String relationSource = record.getSource();
                Integer relationSourceId = record.getSourceId();
                boolean canJump = false; // 是否可以跳转
                
                if (record.getSourceId() != null) {
                    if ("order".equals(record.getSource()) || "order_cancel".equals(record.getSource())) {
                        relation = "订单#" + record.getSourceId();
                        canJump = true; // 订单可以跳转
                    } else if ("recharge".equals(record.getSource())) {
                        relation = "充值#" + record.getSourceId();
                    } else if ("manual".equals(record.getSource())) {
                        relation = "手动调整#" + record.getSourceId();
                    } else {
                        relation = record.getSource() + "#" + record.getSourceId();
                    }
                }
                item.put("relation", relation);
                item.put("relationSource", relationSource);
                item.put("relationSourceId", relationSourceId);
                item.put("canJump", canJump);
                
                // 交易时间
                item.put("add_time", record.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                
                // 交易金额
                item.put("number", record.getAmount().abs().toString());
                item.put("pm", record.getAmount().compareTo(BigDecimal.ZERO) > 0); // true为正数（增加），false为负数（减少）
                
                // 交易类型名称
                String typeName = getTypeDisplayName(record.getType());
                item.put("type_name", typeName);
                
                // 备注
                item.put("mark", record.getMark() != null ? record.getMark() : "");
                
                list.add(item);
            }
            
            // 获取交易类型状态映射
            Map<String, String> status = getBalanceTypeMapping();
            
            Map<String, Object> result = new HashMap<>();
            result.put("list", list);
            result.put("count", pageResult.getTotal());
            result.put("status", status);
            
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("获取余额记录列表失败", e);
            return Result.failure("获取余额记录列表失败：" + e.getMessage());
        }
    }

    /**
     * 设置余额记录备注
     * @param id 记录ID
     * @param params 参数
     * @return 操作结果
     */
    @PostMapping("/set_mark/{id}")
    public Result<?> setBalanceMark(@PathVariable Integer id, @RequestBody Map<String, Object> params) {
        try {
            String mark = (String) params.get("mark");
            
            BalanceRecord record = balanceRecordMapper.selectById(id);
            if (record == null) {
                return Result.failure("余额记录不存在");
            }
            
            record.setMark(mark);
            record.setUpdateTime(LocalDateTime.now());
            int result = balanceRecordMapper.updateById(record);
            
            if (result > 0) {
                return Result.success("备注设置成功");
            } else {
                return Result.failure("备注设置失败");
            }
            
        } catch (Exception e) {
            log.error("设置余额记录备注失败", e);
            return Result.failure("设置备注失败：" + e.getMessage());
        }
    }

    /**
     * 获取交易类型映射
     */
    private Map<String, String> getBalanceTypeMapping() {
        Map<String, String> mapping = new LinkedHashMap<>();
        mapping.put("", "全部");
        mapping.put(BalanceRecord.TYPE_RECHARGE, "余额充值");
        mapping.put(BalanceRecord.TYPE_USE, "余额使用");
        mapping.put(BalanceRecord.TYPE_REFUND, "余额退回");
        mapping.put(BalanceRecord.TYPE_ADJUST, "手动调整");
        return mapping;
    }

    /**
     * 获取类型显示名称
     */
    private String getTypeDisplayName(String type) {
        switch (type) {
            case BalanceRecord.TYPE_RECHARGE:
                return "余额充值";
            case BalanceRecord.TYPE_USE:
                return "余额使用";
            case BalanceRecord.TYPE_REFUND:
                return "余额退回";
            case BalanceRecord.TYPE_ADJUST:
                return "手动调整";
            default:
                return "未知类型";
        }
    }
}