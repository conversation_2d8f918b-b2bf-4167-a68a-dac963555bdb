package com.logic.code.controller.app;

import com.logic.code.common.response.Result;
import com.logic.code.common.utils.JwtHelper;
import com.logic.code.entity.Comment;
import com.logic.code.entity.User;
import com.logic.code.model.query.CommentQuery;
import com.logic.code.model.vo.CommentCountVO;
import com.logic.code.model.vo.CommentPostVO;
import com.logic.code.model.vo.CommentResultVO;
import com.logic.code.service.CommentService;
import com.logic.code.service.PointsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/wechat/comment")
@Slf4j
public class WechatCommentController {

    @Autowired
    private CommentService commentService;
    
    @Autowired
    private PointsService pointsService;


    @GetMapping("/list")
    public Result<List<CommentResultVO>> queryList(@Validated CommentQuery commentQuery) {
        return Result.success(commentService.queryList(commentQuery));
    }

    @GetMapping("/count")
    public Result<CommentCountVO> countList(@Validated CommentQuery commentQuery) {
        return Result.success(commentService.countList(commentQuery));
    }

    @GetMapping("/check-evaluated")
    public Result<Map<String, Object>> checkUserEvaluated(@RequestParam Integer goodsId) {
        User userInfo = JwtHelper.getUserInfo();
        boolean hasEvaluated = commentService.hasUserCommented(userInfo.getId(), goodsId, 0); // typeId=0 表示商品评价

        Map<String, Object> result = new HashMap<>();
        result.put("hasEvaluated", hasEvaluated);
        result.put("message", hasEvaluated ? "您已经评价过该商品" : "可以进行评价");

        return Result.success(result);
    }

    @PostMapping("post")
    public Result postComment(@RequestBody @Validated CommentPostVO commentPostDTO) {
        User userInfo = JwtHelper.getUserInfo();
        commentPostDTO.setUserId(userInfo.getId());

        // 检查用户是否已经评价过该商品
        boolean hasEvaluated = commentService.hasUserCommented(
            userInfo.getId(),
            commentPostDTO.getValueId(),
            commentPostDTO.getTypeId()
        );

        if (hasEvaluated) {
            return Result.error("您已经评价过该商品，不能重复评价");
        }

        // 使用带图片保存的方法
        Comment comment = commentService.createWithPictures(
            commentPostDTO.toPO(),
            commentPostDTO.getPicList()
        );
        
        // 检查是否符合积分奖励条件
        boolean qualifiedForReward = checkCommentRewardQualification(commentPostDTO);
        Map<String, Object> result = new HashMap<>();
        result.put("comment", comment);
        result.put("pointsRewarded", false);
        result.put("rewardPoints", 0);
        
        if (qualifiedForReward) {
            try {
                // 奖励100积分
                boolean success = pointsService.adjustUserPoints(
                    userInfo.getId(), 
                    100, 
                    "16字好评或配图评价奖励"
                );
                
                if (success) {
                    result.put("pointsRewarded", true);
                    result.put("rewardPoints", 100);
                    log.info("用户{}因评价获得100积分奖励", userInfo.getId());
                } else {
                    log.warn("用户{}评价积分奖励失败", userInfo.getId());
                }
            } catch (Exception e) {
                log.error("用户{}评价积分奖励异常", userInfo.getId(), e);
            }
        }
        
        return Result.success(result);
    }
    
    /**
     * 检查评论是否符合积分奖励条件
     * 条件：16字以上好评(4星及以上) 或 有配图
     */
    private boolean checkCommentRewardQualification(CommentPostVO commentPostVO) {
        // 检查是否有配图
        boolean hasPictures = commentPostVO.getPicList() != null && !commentPostVO.getPicList().isEmpty();
        
        // 检查是否为好评且16字以上
        boolean isGoodRating = commentPostVO.getRating() != null && commentPostVO.getRating() >= 4;
        boolean hasLongContent = commentPostVO.getContent() != null && 
                                commentPostVO.getContent().trim().length() >= 16;
        
        // 符合条件：有配图 或者 (好评 且 16字以上)
        return hasPictures  && hasLongContent ;
        //return hasPictures || (isGoodRating && hasLongContent);
    }

}
