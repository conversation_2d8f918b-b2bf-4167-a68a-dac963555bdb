package com.logic.code.controller.app;

import com.logic.code.common.constants.WechatConstants;
import com.logic.code.common.enmus.WeshopWechatResultStatus;
import com.logic.code.common.exception.WeshopWechatException;
import com.logic.code.common.response.Result;
import com.logic.code.common.utils.JwtHelper;
import com.logic.code.common.utils.StringUtils;
import com.logic.code.common.utils.TokenUtils;
import com.logic.code.config.TokenConfig;
import com.logic.code.service.TokenService;
import io.jsonwebtoken.Claims;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Token管理控制器
 * 
 * <AUTHOR>
 * @date 2025/7/29
 */
@Slf4j
@RestController
@RequestMapping("/wechat/token")
public class WechatTokenController {

    @Autowired
    private TokenService tokenService;

    @Autowired
    private TokenConfig tokenConfig;

    /**
     * 手动刷新token
     *
     * @param request HTTP请求
     * @return 新的token
     */
    @PostMapping("/refresh")
    public Result<Map<String, Object>> refreshToken(HttpServletRequest request) {
        String token = TokenUtils.getTokenFromRequest(request);
        if (StringUtils.isBlank(token)) {
            throw new WeshopWechatException(WeshopWechatResultStatus.WECHAT_LOGIN_ERROR);
        }

        String newToken = tokenService.refreshToken(token);

        Map<String, Object> result = new HashMap<>();
        result.put("token", newToken);
        result.put("expiresIn", tokenConfig.getTtl());
        result.put("refreshThreshold", tokenConfig.getRefreshThreshold());

        return Result.success(result);
    }

    /**
     * 检查token状态
     *
     * @param request HTTP请求
     * @return token状态信息
     */
    @GetMapping("/status")
    public Result<Map<String, Object>> checkTokenStatus(HttpServletRequest request) {
        if (!tokenConfig.isStatusCheckEnabled()) {
            throw new WeshopWechatException(WeshopWechatResultStatus.UNAUTHORIZED);
        }

        String token = TokenUtils.getTokenFromRequest(request);
        Map<String, Object> status = tokenService.getTokenStatus(token);

        return Result.success(status);
    }

    /**
     * 验证token有效性（简单验证）
     *
     * @return 验证结果
     */
    @GetMapping("/validate")
    public Result<Map<String, Object>> validateToken() {
        try {
            Claims claims = JwtHelper.getCurrentClaims();
            if (claims == null) {
                throw new WeshopWechatException(WeshopWechatResultStatus.WECHAT_LOGIN_ERROR);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("valid", true);
            result.put("userId", claims.get("uid"));
            result.put("shouldRefresh", JwtHelper.shouldRefreshToken(claims));
            result.put("statusDesc", TokenUtils.getTokenStatusDescription(claims));

            return Result.success(result);
        } catch (Exception e) {
            Map<String, Object> result = new HashMap<>();
            result.put("valid", false);
            result.put("error", e.getMessage());

            return Result.success(result);
        }
    }

    /**
     * 撤销token
     *
     * @param request HTTP请求
     * @return 撤销结果
     */
    @PostMapping("/revoke")
    public Result<String> revokeToken(HttpServletRequest request) {
        String token = TokenUtils.getTokenFromRequest(request);
        if (StringUtils.isBlank(token)) {
            throw new WeshopWechatException(WeshopWechatResultStatus.WECHAT_LOGIN_ERROR);
        }

        tokenService.revokeToken(token);
        return Result.success("Token已撤销");
    }

    /**
     * 获取token配置信息
     *
     * @return 配置信息
     */
    @GetMapping("/config")
    public Result<Map<String, Object>> getConfig() {
        Map<String, Object> config = tokenService.getConfig();
        return Result.success(config);
    }
}
