package com.logic.code.controller.app;

import com.logic.code.common.response.Result;
import com.logic.code.entity.Topic;
import com.logic.code.service.Criteria;
import com.logic.code.service.TopicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RequestMapping("/wechat/topic")
@RestController
public class WechatTopicController {

    @Autowired
    private TopicService topicService;

    @GetMapping("/related")
    public Result<List<Topic>> relatedTopic() {
        return Result.success(topicService.queryByCriteria(Criteria.of(Topic.class).page(1, 4)));
    }

    @GetMapping("/list")
    public Result<List<Topic>> list(Topic topic, Integer pageSize, Integer pageNum) {
        //FIXME 此处需要条件查询
        Criteria<Topic, Object> criteria = Criteria.of(Topic.class).page(pageNum, pageSize);
        return Result.success(topicService.queryByCriteria(criteria))
                .addExtra("total", topicService.countByCriteria(criteria));
    }

    @GetMapping
    public Result<Topic> query(Integer id) {
        return Result.success(topicService.queryById(id));
    }

}
