package com.logic.code.controller.admin;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logic.code.common.response.Result;
import com.logic.code.entity.Card;
import com.logic.code.entity.order.Order;
import com.logic.code.model.query.CardParams;
import com.logic.code.model.vo.OrderDetailVO;
import com.logic.code.service.CardService;
import com.logic.code.service.OrderService;
import jakarta.annotation.Resource;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

import jakarta.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2025/6/15 10:30
 * @desc 卡券管理控制器
 */
@RestController
@RequestMapping("/adminapi/card")
public class AdminCardController {

    @Resource
    private CardService cardService;
    @Resource
    private OrderService orderService;

    /**
     * 分页查询卡券列表
     *
     * @param page           页码
     * @param limit          每页数量
     * @param no             卡券编号
     * @param code           卡券代码
     * @param status         状态
     * @param type           类型
     * @param name           名称
     * @param phone          手机号
     * @param startDateBegin 开始日期-起始
     * @param startDateEnd   开始日期-结束
     * @param endDateBegin   结束日期-起始
     * @param endDateEnd     结束日期-结束
     * @return 分页结果
     */
    @RequestMapping("/list")
    public Result<Map<String, Object>> list(
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "limit", defaultValue = "10") Integer limit,
            @RequestParam(value = "no", required = false) String no,
            @RequestParam(value = "code", required = false) String code,
            @RequestParam(value = "status", required = false) Integer status,
            @RequestParam(value = "type", required = false) Integer type,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "phone", required = false) String phone,
            @RequestParam(value = "startDateBegin", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDateBegin,
            @RequestParam(value = "startDateEnd", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDateEnd,
            @RequestParam(value = "endDateBegin", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDateBegin,
            @RequestParam(value = "endDateEnd", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDateEnd) {

        CardParams params = new CardParams();
        params.setNo(no);
        params.setCode(code);
        params.setStatus(status);
        params.setType(type);
        params.setName(name);
        params.setPhone(phone);
        params.setStartDateBegin(startDateBegin);
        params.setStartDateEnd(startDateEnd);
        params.setEndDateBegin(endDateBegin);
        params.setEndDateEnd(endDateEnd);

        Page<Card> pageData = cardService.getPage(params, page, limit);

        // 转换为包含商品名称的格式
        List<Map<String, Object>> records = pageData.getRecords().stream()
                .map(card -> {
                    Map<String, Object> cardMap = new HashMap<>();
                    cardMap.put("id", card.getId());
                    cardMap.put("phone", card.getPhone());
                    cardMap.put("startDate", card.getStartDate());
                    cardMap.put("endDate", card.getEndDate());
                    cardMap.put("no", card.getNo());
                    cardMap.put("code", card.getCode());
                    cardMap.put("createUser", card.getCreateUser());
                    cardMap.put("createDate", card.getCreateDate());
                    cardMap.put("status", card.getStatus());
                    cardMap.put("type", card.getType());
                    cardMap.put("name", card.getName());
                    cardMap.put("goodsId", card.getGoodsId());

                    // 添加商品名称
                    if (StringUtils.hasText(card.getGoodsId())) {
                        List<String> goodsIds = Arrays.asList(card.getGoodsId().split(","));
                        List<String> goodsNames = cardService.getGoodsNamesByIds(goodsIds);
                        cardMap.put("goodsNames", goodsNames);
                    } else {
                        cardMap.put("goodsNames", new ArrayList<>());
                    }

                    return cardMap;
                })
                .collect(Collectors.toList());

        Map<String, Object> result = new HashMap<>();
        result.put("records", records);
        result.put("total", pageData.getTotal());
        result.put("size", pageData.getSize());
        result.put("current", pageData.getCurrent());
        result.put("pages", pageData.getPages());

        return Result.success(result);
    }

    /**
     * 获取卡券详情
     *
     * @param id 卡券ID
     * @return 卡券详情
     */
    @GetMapping("/{id}")
    public Result<Card> getById(@PathVariable("id") Integer id) {
        Card card = cardService.getById(id);
        return Result.success(card);
    }

    /**
     * 新增卡券
     *
     * @param card 卡券信息
     * @return 新增结果
     */
    @PostMapping("/save")
    public Result<Card> save(@RequestBody Card card) {
        cardService.save(card);
        return Result.success(card);
    }

    /**
     * 更新卡券
     *
     * @param card 卡券信息
     * @return 更新结果
     */
    @PostMapping("/update")
    public Result<Boolean> update(@RequestBody Card card) {
        return Result.success(cardService.updateById(card) > 0);
    }

    /**
     * 删除卡券
     *
     * @param id 卡券ID
     * @return 删除结果
     */
    @PostMapping("/{id}/delete")
    public Result<Boolean> delete(@PathVariable("id") Integer id) {
        return Result.success(cardService.deleteById(id) > 0);
    }

    /**
     * 更新卡券状态
     *
     * @param requestBody 包含id和status的请求体
     * @return 更新结果
     */
    @PostMapping("/updateStatus")
    public Result<Boolean> updateStatus(@RequestBody Map<String, Object> requestBody) {
        try {
            Integer id = (Integer) requestBody.get("id");
            Integer status = (Integer) requestBody.get("status");

            if (id == null || status == null) {
                return Result.failure("参数不能为空");
            }

            Card card = new Card();
            card.setId(id);
            card.setStatus(status);

            boolean success = cardService.updateById(card) > 0;
            if (success) {
                return Result.success(true);
            } else {
                return Result.failure("状态更新失败");
            }
        } catch (Exception e) {
            return Result.failure("状态更新失败: " + e.getMessage());
        }
    }

    /**
     * 批量更新卡券状态
     *
     * @param requestBody 包含ids数组和status的请求体
     * @return 更新结果
     */
    @PostMapping("/batchUpdateStatus")
    public Result<Boolean> batchUpdateStatus(@RequestBody Map<String, Object> requestBody) {
        try {
            @SuppressWarnings("unchecked")
            List<Integer> ids = (List<Integer>) requestBody.get("ids");
            Integer status = (Integer) requestBody.get("status");

            if (ids == null || ids.isEmpty() || status == null) {
                return Result.failure("参数不能为空");
            }

            int successCount = 0;
            for (Integer id : ids) {
                Card card = new Card();
                card.setId(id);
                card.setStatus(status);

                if (cardService.updateById(card) > 0) {
                    successCount++;
                }
            }

            if (successCount == ids.size()) {
                return Result.success(true);
            } else if (successCount > 0) {
                return Result.success(true);
            } else {
                return Result.failure("批量状态更新失败");
            }
        } catch (Exception e) {
            return Result.failure("批量状态更新失败: " + e.getMessage());
        }
    }

    /**
     * 导出卡券数据
     *
     * @param page           页码
     * @param limit          每页数量
     * @param no             卡券编号
     * @param code           卡券代码
     * @param status         状态
     * @param type           类型
     * @param name           名称
     * @param phone          手机号
     * @param startDateBegin 开始日期-起始
     * @param startDateEnd   开始日期-结束
     * @param endDateBegin   结束日期-起始
     * @param endDateEnd     结束日期-结束
     * @param exportType     导出类型
     * @return CSV文件
     */
    @GetMapping("/export")
    public void export(
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "limit", defaultValue = "10") Integer limit,
            @RequestParam(value = "no", required = false) String no,
            @RequestParam(value = "code", required = false) String code,
            @RequestParam(value = "status", required = false) Integer status,
            @RequestParam(value = "type", required = false) Integer type,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "phone", required = false) String phone,
            @RequestParam(value = "startDateBegin", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDateBegin,
            @RequestParam(value = "startDateEnd", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDateEnd,
            @RequestParam(value = "endDateBegin", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDateBegin,
            @RequestParam(value = "endDateEnd", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDateEnd,
            @RequestParam(value = "exportType", defaultValue = "csv") String exportType,
            HttpServletResponse response) {

        CardParams params = new CardParams();
        params.setNo(no);
        params.setCode(code);
        params.setStatus(status);
        params.setType(type);
        params.setName(name);
        params.setPhone(phone);
        params.setStartDateBegin(startDateBegin);
        params.setStartDateEnd(startDateEnd);
        params.setEndDateBegin(endDateBegin);
        params.setEndDateEnd(endDateEnd);

        cardService.exportCards(params, response);
    }

    @RequestMapping("/{id}/order")
    public Result<OrderDetailVO> order(@PathVariable("id") Integer id) {

        Order order = orderService.queryOne(Order.builder().couponId(id).build());
        if (Objects.nonNull(order)) {
            OrderDetailVO detailVO = orderService.queryOrderDetail(order.getId());
            return Result.success(detailVO);
        }
        return Result.failure("无卡劵订单");
    }

    /**
     * 根据商品ID列表获取商品信息
     *
     * @param requestBody 包含商品ID列表的请求体
     * @return 商品信息列表
     */
    @PostMapping("/getGoodsByIds")
    public Result<List<Map<String, Object>>> getGoodsByIds(@RequestBody Map<String, Object> requestBody) {
        try {
            @SuppressWarnings("unchecked")
            List<String> ids = (List<String>) requestBody.get("ids");

            if (ids == null || ids.isEmpty()) {
                return Result.success(List.of());
            }

            List<Map<String, Object>> goodsList = cardService.getGoodsByIds(ids);
            return Result.success(goodsList);
        } catch (Exception e) {
            return Result.failure("获取商品信息失败: " + e.getMessage());
        }
    }

    /**
     * 关联商品到卡券
     *
     * @param requestBody 包含卡券ID和商品ID列表的请求体
     * @return 关联结果
     */
    @PostMapping("/linkGoods")
    public Result<Boolean> linkGoods(@RequestBody Map<String, Object> requestBody) {
        try {
            Integer cardId = (Integer) requestBody.get("cardId");
            String goodsIds = (String) requestBody.get("goodsIds");

            if (cardId == null) {
                return Result.failure("卡券ID不能为空");
            }

            Card card = new Card();
            card.setId(cardId);
            card.setGoodsId(goodsIds);

            boolean success = cardService.updateById(card) > 0;
            if (success) {
                return Result.success(true);
            } else {
                return Result.failure("关联商品失败");
            }
        } catch (Exception e) {
            return Result.failure("关联商品失败: " + e.getMessage());
        }
    }
}
