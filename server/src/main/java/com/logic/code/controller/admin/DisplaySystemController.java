package com.logic.code.controller.admin;

import com.logic.code.common.response.Result;
import com.logic.code.entity.goods.DisplaySystem;
import com.logic.code.service.DisplaySystemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 展示系统管理控制器
 *
 * <AUTHOR>
 * @date 2025/7/6
 */
@RestController
@RequestMapping("/adminapi/display-system")
public class DisplaySystemController {

    @Autowired
    private DisplaySystemService displaySystemService;

    /**
     * 获取所有启用的展示系统
     */
    @GetMapping("/enabled")
    public Result<List<DisplaySystem>> getEnabledSystems() {
        List<DisplaySystem> systems = displaySystemService.getEnabledSystems();
        return Result.success(systems);
    }

    /**
     * 获取所有展示系统
     */
    @GetMapping("/all")
    public Result<List<DisplaySystem>> getAllSystems() {
        List<DisplaySystem> systems = displaySystemService.getAllSystems();
        return Result.success(systems);
    }
}
