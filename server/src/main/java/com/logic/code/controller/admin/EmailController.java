package com.logic.code.controller.admin;

import com.alibaba.fastjson.JSONObject;
import com.logic.code.common.response.Result;
import com.logic.code.common.response.ResultStatus;
import com.logic.code.service.EmailService;
import com.logic.code.util.EmailConfigDiagnostic;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 邮件发送控制器
 *
 * <AUTHOR>
 * @date 2025/7/13
 */
@Slf4j
@RestController
@RequestMapping("/adminapi/email")
public class EmailController {

    @Autowired
    private EmailService emailService;

    @Autowired
    private EmailConfigDiagnostic emailConfigDiagnostic;

    /**
     * 发送简单文本邮件
     *
     * @param to 收件人
     * @param subject 主题
     * @param content 内容
     * @return 发送结果
     */
    @PostMapping("/send/simple")
    public Result<String> sendSimpleEmail(
            @RequestParam String to,
            @RequestParam String subject,
            @RequestParam String content) {

        try {
            if (!emailService.isValidEmail(to)) {
                return Result.failure("邮箱地址格式不正确");
            }

            boolean success = emailService.sendSimpleEmail(to, subject, content);
            if (success) {
                return Result.success("邮件发送成功");
            } else {
                return Result.failure("邮件发送失败");
            }
        } catch (Exception e) {
            log.error("发送简单邮件失败", e);
            return Result.failure("邮件发送失败：" + e.getMessage());
        }
    }

    /**
     * 发送HTML邮件
     *
     * @param to 收件人
     * @param subject 主题
     * @param content 内容
     * @return 发送结果
     */
    @PostMapping("/send/html")
    public Result<String> sendHtmlEmail(
            @RequestParam String to,
            @RequestParam String subject,
            @RequestParam String content) {

        try {
            if (!emailService.isValidEmail(to)) {
                return Result.failure("邮箱地址格式不正确");
            }

            String htmlContent = emailService.buildHtmlContent(subject, content);
            boolean success = emailService.sendHtmlEmail(to, subject, htmlContent);
            if (success) {
                return Result.success("HTML邮件发送成功");
            } else {
                return Result.failure("HTML邮件发送失败");
            }
        } catch (Exception e) {
            log.error("发送HTML邮件失败", e);
            return Result.failure("HTML邮件发送失败：" + e.getMessage());
        }
    }

    /**
     * 发送带附件的邮件
     *
     * @param to 收件人
     * @param subject 主题
     * @param content 内容
     * @param file 附件文件
     * @return 发送结果
     */
    @PostMapping("/send/attachment")
    public Result<String> sendEmailWithAttachment(
            @RequestParam String to,
            @RequestParam String subject,
            @RequestParam String content,
            @RequestParam("file") MultipartFile file) {

        try {
            if (!emailService.isValidEmail(to)) {
                return Result.failure("邮箱地址格式不正确");
            }

            if (file.isEmpty()) {
                return Result.failure("附件文件不能为空");
            }

            // 保存上传的文件到临时目录
            String tempDir = System.getProperty("java.io.tmpdir");
            String fileName = file.getOriginalFilename();
            File tempFile = new File(tempDir, fileName);
            file.transferTo(tempFile);

            String htmlContent = emailService.buildHtmlContent(subject, content);
            boolean success = emailService.sendEmailWithAttachment(to, subject, htmlContent, tempFile.getAbsolutePath());

            // 删除临时文件
            tempFile.delete();

            if (success) {
                return Result.success("带附件邮件发送成功");
            } else {
                return Result.failure("带附件邮件发送失败");
            }
        } catch (IOException e) {
            log.error("处理附件文件失败", e);
            return Result.failure("处理附件文件失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("发送带附件邮件失败", e);
            return Result.failure("带附件邮件发送失败：" + e.getMessage());
        }
    }

    /**
     * 发送生成的文件
     *
     * @param recipients 收件人（多个用逗号分隔）
     * @param fileType 文件类型
     * @param filePath 文件路径
     * @param description 文件描述
     * @return 发送结果
     */
    @PostMapping("/send/generated-file")
    public Result<String> sendGeneratedFile(
            @RequestParam String recipients,
            @RequestParam String fileType,
            @RequestParam String filePath,
            @RequestParam(required = false) String description) {

        try {
            String[] recipientArray = recipients.split(",");

            // 验证邮箱地址
            for (String email : recipientArray) {
                if (!emailService.isValidEmail(email.trim())) {
                    return Result.failure("邮箱地址格式不正确：" + email);
                }
            }

            // 验证文件是否存在
            File file = new File(filePath);
            if (!file.exists() || !file.isFile()) {
                return Result.failure("文件不存在：" + filePath);
            }

            if (description == null || description.trim().isEmpty()) {
                description = "系统生成的" + fileType + "文件";
            }

            boolean success = emailService.sendGeneratedFile(recipientArray, fileType, filePath, description);
            if (success) {
                return Result.success("文件邮件发送成功");
            } else {
                return Result.failure("文件邮件发送失败");
            }
        } catch (Exception e) {
            log.error("发送生成文件邮件失败", e);
            return Result.failure("文件邮件发送失败：" + e.getMessage());
        }
    }

    /**
     * 发送系统通知
     *
     * @param subject 主题
     * @param content 内容
     * @return 发送结果
     */
    @PostMapping("/send/system-notification")
    public Result<String> sendSystemNotification(
            @RequestParam String subject,
            @RequestParam String content) {

        try {
            String htmlContent = emailService.buildHtmlContent(subject, content);
            boolean success = emailService.sendSystemNotification(subject, htmlContent);
            if (success) {
                return Result.success("系统通知发送成功");
            } else {
                return Result.failure("系统通知发送失败");
            }
        } catch (Exception e) {
            log.error("发送系统通知失败", e);
            return Result.failure("系统通知发送失败：" + e.getMessage());
        }
    }

    /**
     * 测试邮件配置
     *
     * @return 测试结果
     */
    @GetMapping("/test")
    public Result<Map<String, Object>> testEmailConfiguration() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 发送测试邮件
            String testSubject = "邮件配置测试";
            String testContent = "这是一封测试邮件，用于验证邮件配置是否正确。";
            String htmlContent = emailService.buildHtmlContent(testSubject, testContent);

            boolean success = emailService.sendSystemNotification(testSubject, htmlContent);

            result.put("success", success);
            result.put("message", success ? "邮件配置测试成功" : "邮件配置测试失败");
            result.put("timestamp", new java.util.Date());

            return Result.success(result);
        } catch (Exception e) {
            log.error("邮件配置测试失败", e);
            result.put("success", false);
            result.put("message", "邮件配置测试失败：" + e.getMessage());
            result.put("timestamp", new java.util.Date());
            return Result.failure(JSONObject.toJSONString(result));
        }
    }

    /**
     * 诊断邮件配置
     *
     * @return 诊断结果
     */
    @GetMapping("/diagnose")
    public Result<String> diagnoseEmailConfig() {
        try {
            String diagnostic = emailConfigDiagnostic.diagnoseEmailConfig();
            return Result.success(diagnostic);
        } catch (Exception e) {
            log.error("邮件配置诊断失败", e);
            return Result.failure("邮件配置诊断失败：" + e.getMessage());
        }
    }

    /**
     * 发送测试邮件（使用原生方法）
     *
     * @param toEmail 收件人邮箱
     * @return 测试结果
     */
    @PostMapping("/test-native")
    public Result<String> testNativeEmail(@RequestParam String toEmail) {
        try {
            if (!emailService.isValidEmail(toEmail)) {
                return Result.failure("邮箱地址格式不正确");
            }

            boolean success = emailConfigDiagnostic.sendTestEmail(toEmail);
            if (success) {
                return Result.success("测试邮件发送成功");
            } else {
                return Result.failure("测试邮件发送失败");
            }
        } catch (Exception e) {
            log.error("测试邮件发送失败", e);
            return Result.failure("测试邮件发送失败：" + e.getMessage());
        }
    }

    /**
     * 获取邮箱服务器配置信息
     *
     * @param emailType 邮箱类型（qq, 163, gmail, outlook）
     * @return 配置信息
     */
    @GetMapping("/config/{emailType}")
    public Result<String> getEmailServerConfig(@PathVariable String emailType) {
        try {
            String config = emailConfigDiagnostic.getEmailServerConfig(emailType);
            return Result.success(config);
        } catch (Exception e) {
            log.error("获取邮箱配置信息失败", e);
            return Result.failure("获取邮箱配置信息失败：" + e.getMessage());
        }
    }
}
