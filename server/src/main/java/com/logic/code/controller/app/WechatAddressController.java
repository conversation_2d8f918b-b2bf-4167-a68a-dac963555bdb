package com.logic.code.controller.app;

import com.logic.code.common.response.Result;
import com.logic.code.common.utils.JwtHelper;
import com.logic.code.entity.Address;
import com.logic.code.entity.User;
import com.logic.code.model.vo.AddressVO;
import com.logic.code.service.AddressService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/wechat/address")
@Validated
public class WechatAddressController  {

    @Autowired
    private AddressService addressService;


    @GetMapping("/list")
    public Result<List<AddressVO>> queryList() {
        return Result.success(addressService.queryDetailList());
    }

    @GetMapping("/detail")
    public Result<AddressVO> queryDetail(Integer id) {
        return Result.success(addressService.queryDetail(id));
    }

    @PostMapping("/save")
    public Result save(@Validated @RequestBody Address entity) {
        User userInfo = JwtHelper.getUserInfo();
        entity.setUserId(userInfo.getId());
        if(entity.getId()!=null){
            Result<Integer> integerResult = Result.success(addressService.updateNotNull(entity));
            return integerResult;
        }
        Result<Address> integerResult = Result.success(addressService.createEntity(entity));
        return integerResult;
    }

    @PostMapping("/update")
    public Result update(@Validated @RequestBody Address entity) {
        User userInfo = JwtHelper.getUserInfo();
        entity.setUserId(userInfo.getId());
        addressService.updateNotNull(entity);
        return Result.success();
    }
    @RequestMapping("/setDefault")
    public Result setDefault(Integer id) {
        return Result.success(addressService.serDefault(id));
    }

    @PostMapping("/delete")
    public Result delete(@Validated @RequestBody Address entity) {
        addressService.deleteById(entity.getId());
        return Result.success();
    }
}
