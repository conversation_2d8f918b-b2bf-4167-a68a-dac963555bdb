package com.logic.code.controller.app;

import com.logic.code.common.response.Result;
import com.logic.code.common.utils.JwtHelper;
import com.logic.code.entity.User;
import com.logic.code.entity.UserCoupon;
import com.logic.code.service.UserCouponService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 优惠券控制器
 * <AUTHOR>
 * @date 2025/7/25
 */
@RestController
@RequestMapping("/wechat/coupon")
@Slf4j
public class CouponController {

    @Autowired
    private UserCouponService userCouponService;

    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

    /**
     * 获取用户优惠券统计
     */
    @GetMapping("/stats")
    public Result<Map<String, Object>> getCouponStats() {
        try {
            User userInfo = JwtHelper.getUserInfo();
            Map<String, Object> stats = userCouponService.getCouponStats(userInfo.getId());
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取优惠券统计失败", e);
            return Result.failure("获取统计信息失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户优惠券列表
     */
    @GetMapping("/list")
    public Result<List<UserCoupon>> getCouponList(
            @RequestParam(defaultValue = "available") String status,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "100") Integer size) {
        try {
            User userInfo = JwtHelper.getUserInfo();
            List<UserCoupon> coupons = userCouponService.getUserCoupons(userInfo.getId(), status, page, size);

            // UserCouponService已经格式化了数据，这里只需要设置name字段
            for (UserCoupon coupon : coupons) {
                // 设置优惠券名称
                if (coupon.getTitle() != null) {
                    coupon.setName(coupon.getTitle());
                } else {
                    coupon.setName("优惠券");
                }
            }

            return Result.success(coupons);
        } catch (Exception e) {
            log.error("获取优惠券列表失败", e);
            return Result.failure("获取优惠券列表失败：" + e.getMessage());
        }
    }

    /**
     * 使用优惠券
     */
    @PostMapping("/use")
    public Result<Boolean> useCoupon(@RequestBody Map<String, Object> params) {
        try {
            User userInfo = JwtHelper.getUserInfo();
            Integer couponId = Integer.valueOf(params.get("couponId").toString());
            Integer orderId = params.get("orderId") != null ? 
                Integer.valueOf(params.get("orderId").toString()) : null;
            
            boolean success = userCouponService.useCoupon(userInfo.getId(), couponId, orderId);
            if (success) {
                return Result.success(true);
            } else {
                return Result.failure("使用优惠券失败");
            }
        } catch (Exception e) {
            log.error("使用优惠券失败", e);
            return Result.failure("使用优惠券失败：" + e.getMessage());
        }
    }

    /**
     * 格式化优惠券数据用于前端显示
     */
    private void formatCouponForResponse(UserCoupon coupon) {
        // 检查并更新过期状态
        Date now = new Date();
        if (coupon.getStatus() == 0 && coupon.getEndTime() != null && now.after(coupon.getEndTime())) {
            coupon.setStatus(2); // 设置为过期状态
        }

        // 设置优惠券名称
        if (coupon.getTitle() != null) {
            coupon.setName(coupon.getTitle());
        } else {
            coupon.setName("优惠券");
        }

        // 格式化开始时间和结束时间字符串，用于前端显示
        String startTimeStr = "";
        String endTimeStr = "";

        if (coupon.getStartTime() != null) {
            startTimeStr = dateFormat.format(coupon.getStartTime());
        }

        if (coupon.getEndTime() != null) {
            endTimeStr = dateFormat.format(coupon.getEndTime());
        }

        // 将格式化的时间存储在remark字段中，前端可以直接使用
        // 格式：startTime,endTime
        if (!startTimeStr.isEmpty() && !endTimeStr.isEmpty()) {
            coupon.setRemark(startTimeStr + "," + endTimeStr);
        } else if (!endTimeStr.isEmpty()) {
            // 如果只有结束时间，也要保持逗号格式以便前端解析
            coupon.setRemark("," + endTimeStr);
        }
    }
}