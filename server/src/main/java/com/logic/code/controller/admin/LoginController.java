package com.logic.code.controller.admin;

import com.logic.code.common.response.Result;
import com.logic.code.service.SystemMenuService;
import com.logic.code.service.AdminService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/8 11:45
 * @desc
 */

@Slf4j
@RestController
@RequestMapping("/adminapi")
public class LoginController {

    @Resource
    private SystemMenuService systemMenuService;
    
    @Resource
    private AdminService adminService;


    @RequestMapping("/login")
    public Result<Map<String, Object>> login() {
        try {
            // 使用默认管理员登录（开发环境）
            Map<String, Object> loginResponse = adminService.defaultAdminLogin();
            return Result.success(loginResponse);
        } catch (Exception e) {
            log.error("管理员登录失败", e);
            return Result.failure("登录失败：" + e.getMessage());
        }
    }
    
    /**
     * 管理员登录接口（带用户名密码验证）
     * 
     * @param username 用户名
     * @param password 密码
     * @return 登录结果
     */
    @RequestMapping("/login/auth")
    public Result<Map<String, Object>> loginWithAuth(
            @RequestParam String username, 
            @RequestParam String password) {
        try {
            Map<String, Object> loginResult = adminService.adminLogin(username, password);
            
            if (!(Boolean) loginResult.get("success")) {
                return Result.failure((String) loginResult.get("message"));
            }
            
            String token = (String) loginResult.get("token");
            com.logic.code.entity.Admin admin = (com.logic.code.entity.Admin) loginResult.get("admin");
            
            Map<String, Object> response = adminService.buildLoginResponse(admin, token);
            return Result.success(response);
            
        } catch (Exception e) {
            log.error("管理员登录失败", e);
            return Result.failure("登录失败：" + e.getMessage());
        }
    }

    /**
     * 管理员退出登录接口
     * 
     * @param token JWT token (从请求头获取)
     * @return 退出结果
     */
    @RequestMapping("/setting/admin/logout")
    public Result<String> logout(@RequestHeader(value = "Authorization", required = false) String token) {
        try {
            // 验证token是否为管理员token
            if (token != null && token.startsWith("Bearer ")) {
                String actualToken = token.substring(7); // 移除"Bearer "前缀
                
                // 验证是否为有效的管理员token
                if (adminService.isAdmin(actualToken)) {
                    log.info("管理员退出登录成功，token: {}", actualToken.substring(0, Math.min(20, actualToken.length())) + "...");
                    
                    // 将token加入黑名单
                    adminService.addTokenToBlacklist(actualToken);
                    
                    return Result.success("退出登录成功");
                } else {
                    return Result.failure("无效的管理员token");
                }
            } else {
                return Result.failure("未提供有效的token");
            }
            
        } catch (Exception e) {
            log.error("管理员退出登录失败", e);
            return Result.failure("退出登录失败：" + e.getMessage());
        }
    }

    /**
     * 管理员退出登录接口（兼容POST方式）
     * 
     * @param token JWT token (从请求参数获取)
     * @return 退出结果
     */
    @RequestMapping(value = "/setting/admin/logout", method = org.springframework.web.bind.annotation.RequestMethod.POST)
    public Result<String> logoutPost(
            @RequestParam(value = "token", required = false) String token,
            @RequestHeader(value = "Authorization", required = false) String authHeader) {
        try {
            String actualToken = null;
            
            // 优先从Authorization header获取token
            if (authHeader != null && authHeader.startsWith("Bearer ")) {
                actualToken = authHeader.substring(7);
            } else if (token != null && !token.isEmpty()) {
                // 从请求参数获取token
                actualToken = token;
            }
            
            if (actualToken == null || actualToken.isEmpty()) {
                return Result.failure("未提供有效的token");
            }
            
            // 验证是否为有效的管理员token
            if (adminService.isAdmin(actualToken)) {
                log.info("管理员退出登录成功，token: {}", actualToken.substring(0, Math.min(20, actualToken.length())) + "...");
                
                // 将token加入黑名单
                adminService.addTokenToBlacklist(actualToken);
                
                return Result.success("退出登录成功");
            } else {
                return Result.failure("无效的管理员token");
            }
            
        } catch (Exception e) {
            log.error("管理员退出登录失败", e);
            return Result.failure("退出登录失败：" + e.getMessage());
        }
    }

}
