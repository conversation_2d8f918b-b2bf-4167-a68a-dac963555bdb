package com.logic.code.controller.app;

import com.github.binarywang.wxpay.bean.notify.SignatureHeader;
import com.github.binarywang.wxpay.bean.notify.WxPayNotifyResponse;
import com.github.binarywang.wxpay.bean.notify.WxPayNotifyV3Result;
import com.github.binarywang.wxpay.bean.result.WxPayUnifiedOrderV3Result;
import com.github.binarywang.wxpay.constant.WxPayConstants;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.logic.code.common.response.Result;
import com.logic.code.service.PayService;
import com.logic.code.service.RechargeService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 微信支付
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/wechat/pay")
@Validated
@Slf4j
public class WechatPayController {

    @Autowired
    private PayService payService;

    @Autowired
    private RechargeService rechargeService;

    @Resource
    private WxPayService wxService;

    @GetMapping("/prepay")
    public Result<WxPayUnifiedOrderV3Result.JsapiResult> prepay(@NotNull @RequestParam("orderId") Integer orderId) {
        return Result.success(payService.prepay(orderId));
    }

    /**
     * 当支付成功后微信会回调这个地址，在这里你可以做一些事，比如修改订单状态什么的。
     *
     * @return
     */
    @RequestMapping("/notify")
    public String notify(@RequestBody String notifyData, HttpServletRequest request) throws WxPayException {
        log.info("【支付回调通知处理-notifyData】:{}", notifyData);
        SignatureHeader requestHeader = this.getRequestHeader(request);
        log.info("【支付回调通知处理-SignatureHeader】:{}", requestHeader);
        return payService.notify(notifyData, requestHeader);
    }

    @RequestMapping("/notifyRefund")
    public String notifyRefund(@RequestBody String notifyData, HttpServletRequest request) throws WxPayException {
        log.info("【支付回调通知处理-notifyData】:{}", notifyData);
        SignatureHeader requestHeader = this.getRequestHeader(request);
        log.info("【支付回调通知处理-SignatureHeader】:{}", requestHeader);
        return "ok";
    }

    @RequestMapping("/notifySuc")
    public Result<Boolean> notifySuc(@NotNull @RequestParam("orderId") Integer orderId) {
        return Result.success(payService.notify(orderId));
    }

    /**
     * 充值支付回调通知
     */
    @RequestMapping("/notifyRecharge")
    public String notifyRecharge(@RequestBody String notifyData, HttpServletRequest request) throws WxPayException {
        log.info("【充值支付回调通知处理-notifyData】:{}", notifyData);
        SignatureHeader requestHeader = this.getRequestHeader(request);
        log.info("【充值支付回调通知处理-SignatureHeader】:{}", requestHeader);
        
        // 临时返回成功，避免编译错误
        // 实际的支付回调处理逻辑需要在解决SDK兼容性问题后实现
        log.warn("充值支付回调通知暂时未实现，请检查SDK版本兼容性");

        try {
            // 直接使用wxService解析通知数据，与PayService中的方法保持一致
            WxPayNotifyV3Result result = this.wxService.parseOrderNotifyV3Result(notifyData, requestHeader);
            // 解密后的数据
            WxPayNotifyV3Result.DecryptNotifyResult notifyResult = result.getResult();
            
            if (WxPayConstants.WxpayTradeStatus.SUCCESS.equals(notifyResult.getTradeState())) {
                String outTradeNo = notifyResult.getOutTradeNo();
                String transactionId = notifyResult.getTransactionId();
                
                // 从订单号中提取充值记录ID
                if (outTradeNo.startsWith("RECHARGE_")) {
                    String[] parts = outTradeNo.split("_");
                    if (parts.length >= 2) {
                        Integer rechargeId = Integer.valueOf(parts[1]);
                        rechargeService.handleRechargePaySuccess(rechargeId, transactionId);
                    }
                }
                
                log.info("【充值支付回调通知处理成功】");
                return WxPayNotifyResponse.success("成功");
            } else {
                log.error("【充值支付回调通知失败】:{}", result);
                return WxPayNotifyResponse.fail("支付失败");
            }
        } catch (Exception e) {
            log.error("处理充值支付回调失败", e);
            return WxPayNotifyResponse.fail("处理失败");
        }
    }


    /**
     * 获取回调请求头：签名相关
     *
     * @param request HttpServletRequest
     * @return SignatureHeader
     */
    public SignatureHeader getRequestHeader(HttpServletRequest request) {
        // 获取通知签名
        String signature = request.getHeader("Wechatpay-Signature");
        String nonce = request.getHeader("Wechatpay-Nonce");
        String serial = request.getHeader("Wechatpay-Serial");
        String timestamp = request.getHeader("Wechatpay-Timestamp");

        SignatureHeader signatureHeader = new SignatureHeader();
        signatureHeader.setSignature(signature);
        signatureHeader.setNonce(nonce);
        signatureHeader.setSerial(serial);
        signatureHeader.setTimeStamp(timestamp);
        return signatureHeader;
    }

}
