package com.logic.code.controller.admin;

import com.logic.code.common.response.Result;
import com.logic.code.entity.file.FileCategory;
import com.logic.code.service.FileCategoryService;
import com.logic.code.util.CategoryHierarchyUpdater;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * File category controller
 */
@RestController
@RequestMapping("/adminapi/file/category")
@Validated
public class FileCategoryController {

    @Autowired
    private FileCategoryService fileCategoryService;

    @Autowired
    private CategoryHierarchyUpdater categoryHierarchyUpdater;

    /**
     * Get category list
     * @param pid Parent category ID
     * @return List of categories
     */
    @GetMapping
    public Result<List<FileCategory>> list(@RequestParam(value = "pid", defaultValue = "0") Integer pid) {
        List<FileCategory> categories = fileCategoryService.getCategoryList(pid);
        return Result.success(categories);
    }

    /**
     * Get category tree
     * @return Category tree
     */
    @GetMapping("/tree")
    public Result<List<FileCategory>> tree() {
        List<FileCategory> categoryTree = fileCategoryService.getCategoryTree();
        return Result.success(categoryTree);
    }

    /**
     * Get category by ID
     * @param id Category ID
     * @return Category
     */
    @GetMapping("/{id}")
    public Result<FileCategory> getById(@PathVariable Integer id) {
        FileCategory category = fileCategoryService.getById(id);
        if (category == null) {
            return Result.failure("Category not found");
        }
        return Result.success(category);
    }

    /**
     * Create form for new category
     * @param parentId Parent category ID (optional)
     * @return Form configuration for creating category
     */
    @GetMapping("/create")
    public Result<Map<String, Object>> create(@RequestParam(value = "id", required = false, defaultValue = "0") Integer parentId) {
        Map<String, Object> formConfig = new HashMap<>();

        // Form title
        formConfig.put("title", "新增分类");

        // Form action and method
        formConfig.put("action", "/file/category");
        formConfig.put("method", "POST");

        // Form rules (field definitions)
        List<Map<String, Object>> rules = new ArrayList<>();

        // Parent ID field (hidden)
        Map<String, Object> parentIdRule = new HashMap<>();
        parentIdRule.put("type", "hidden");
        parentIdRule.put("field", "parentId");
        parentIdRule.put("value", parentId);
        rules.add(parentIdRule);

        // Category name field
        Map<String, Object> titleRule = new HashMap<>();
        titleRule.put("type", "input");
        titleRule.put("field", "title");
        titleRule.put("title", "分类名称");
        titleRule.put("value", "");

        Map<String, Object> titleProps = new HashMap<>();
        titleProps.put("placeholder", "请输入分类名称");
        titleRule.put("props", titleProps);

        List<Map<String, Object>> titleValidate = new ArrayList<>();
        Map<String, Object> titleRequired = new HashMap<>();
        titleRequired.put("required", true);
        titleRequired.put("message", "请输入分类名称");
        titleRequired.put("trigger", "blur");
        titleValidate.add(titleRequired);
        titleRule.put("validate", titleValidate);
        rules.add(titleRule);

        // Description field
        Map<String, Object> descRule = new HashMap<>();
        descRule.put("type", "input");
        descRule.put("field", "description");
        descRule.put("title", "分类描述");
        descRule.put("value", "");

        Map<String, Object> descProps = new HashMap<>();
        descProps.put("type", "textarea");
        descProps.put("placeholder", "请输入分类描述（可选）");
        descProps.put("rows", 3);
        descRule.put("props", descProps);
        rules.add(descRule);

        // Sort order field
        Map<String, Object> sortRule = new HashMap<>();
        sortRule.put("type", "inputNumber");
        sortRule.put("field", "sortOrder");
        sortRule.put("title", "排序");
        sortRule.put("value", 0);

        Map<String, Object> sortProps = new HashMap<>();
        sortProps.put("min", 0);
        sortProps.put("placeholder", "排序值，数字越小越靠前");
        sortRule.put("props", sortProps);
        rules.add(sortRule);

        formConfig.put("rules", rules);

        // Form configuration
        Map<String, Object> config = new HashMap<>();
        Map<String, Object> form = new HashMap<>();
        form.put("labelWidth", "100px");
        config.put("form", form);
        formConfig.put("config", config);

        return Result.success(formConfig);
    }

    /**
     * Edit form for category
     * @param id Category ID
     * @return Category
     */
    @GetMapping("/{id}/edit")
    public Result<FileCategory> edit(@PathVariable Integer id) {
        FileCategory category = fileCategoryService.getById(id);
        if (category == null) {
            return Result.failure("Category not found");
        }
        return Result.success(category);
    }

    /**
     * Create category
     * @param category Category to create
     * @return Created category
     */
    @PostMapping
    public Result<FileCategory> save(@Valid @RequestBody FileCategory category) {
        try {
            FileCategory createdCategory = fileCategoryService.createCategory(category);
            return Result.success(createdCategory);
        } catch (Exception e) {
            return Result.failure(e.getMessage());
        }
    }

    /**
     * Update category
     * @param category Category to update
     * @return True if updated successfully
     */
    @PutMapping
    public Result<Boolean> update(@Valid @RequestBody FileCategory category) {
        try {
            boolean success = fileCategoryService.updateCategory(category);
            if (success) {
                return Result.success(true);
            } else {
                return Result.failure("Failed to update category");
            }
        } catch (Exception e) {
            return Result.failure(e.getMessage());
        }
    }

    /**
     * Delete category
     * @param id Category ID
     * @return True if deleted successfully
     */
    @DeleteMapping("/{id}")
    public Result<Boolean> delete(@PathVariable @NotNull Integer id) {
        try {
            boolean success = fileCategoryService.deleteCategory(id);
            if (success) {
                return Result.success(true);
            } else {
                return Result.failure("Failed to delete category");
            }
        } catch (Exception e) {
            return Result.failure(e.getMessage());
        }
    }

    /**
     * Batch delete categories
     * @param ids Category IDs
     * @return True if deleted successfully
     */
    @DeleteMapping("/batch")
    public Result<Boolean> batchDelete(@RequestBody @NotEmpty List<Integer> ids) {
        try {
            boolean success = fileCategoryService.batchDeleteCategories(ids);
            if (success) {
                return Result.success(true);
            } else {
                return Result.failure("Failed to delete categories");
            }
        } catch (Exception e) {
            return Result.failure(e.getMessage());
        }
    }

    /**
     * Move category
     * @param id Category ID
     * @param newParentId New parent category ID
     * @return True if moved successfully
     */
    @PutMapping("/{id}/move")
    public Result<Boolean> move(@PathVariable @NotNull Integer id,
                               @RequestParam @NotNull Integer newParentId) {
        try {
            boolean success = fileCategoryService.moveCategory(id, newParentId);
            if (success) {
                return Result.success(true);
            } else {
                return Result.failure("Failed to move category");
            }
        } catch (Exception e) {
            return Result.failure(e.getMessage());
        }
    }

    /**
     * Update category sort order
     * @param id Category ID
     * @param sortOrder New sort order
     * @return True if updated successfully
     */
    @PutMapping("/{id}/sort")
    public Result<Boolean> updateSort(@PathVariable @NotNull Integer id,
                                     @RequestParam @NotNull Integer sortOrder) {
        try {
            boolean success = fileCategoryService.updateSortOrder(id, sortOrder);
            if (success) {
                return Result.success(true);
            } else {
                return Result.failure("Failed to update sort order");
            }
        } catch (Exception e) {
            return Result.failure(e.getMessage());
        }
    }

    /**
     * Get category path
     * @param id Category ID
     * @return Category path
     */
    @GetMapping("/{id}/path")
    public Result<List<FileCategory>> getPath(@PathVariable @NotNull Integer id) {
        List<FileCategory> path = fileCategoryService.getCategoryPath(id);
        return Result.success(path);
    }

    /**
     * Search categories
     * @param title Search keyword
     * @return List of matching categories
     */
    @GetMapping("/search")
    public Result<List<FileCategory>> search(@RequestParam String title) {
        List<FileCategory> categories = fileCategoryService.searchCategories(title);
        return Result.success(categories);
    }

    /**
     * Fix category hierarchy
     * @return Success message
     */
    @PostMapping("/fix-hierarchy")
    public Result<String> fixHierarchy() {
        try {
            categoryHierarchyUpdater.fixCategoryHierarchy();
            return Result.success("分类层级结构修复完成");
        } catch (Exception e) {
            return Result.failure("修复分类层级结构失败：" + e.getMessage());
        }
    }

    /**
     * Validate category hierarchy
     * @return Validation result
     */
    @GetMapping("/validate-hierarchy")
    public Result<Boolean> validateHierarchy() {
        try {
            boolean isValid = categoryHierarchyUpdater.validateCategoryHierarchy();
            return Result.success(isValid);
        } catch (Exception e) {
            return Result.failure("验证分类层级结构失败：" + e.getMessage());
        }
    }

    /**
     * Get category hierarchy statistics
     * @return Statistics data
     */
    @GetMapping("/hierarchy-stats")
    public Result<Map<String, Object>> getHierarchyStats() {
        try {
            Map<String, Object> stats = categoryHierarchyUpdater.getCategoryHierarchyStats();
            return Result.success(stats);
        } catch (Exception e) {
            return Result.failure("获取分类统计信息失败：" + e.getMessage());
        }
    }
}
