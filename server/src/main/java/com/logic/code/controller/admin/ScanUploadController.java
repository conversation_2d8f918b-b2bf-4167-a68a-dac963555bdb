package com.logic.code.controller.admin;

import com.logic.code.common.response.Result;
import com.logic.code.entity.file.FileAttachment;
import com.logic.code.service.ScanUploadService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * Scan upload controller
 */
@RestController
@RequestMapping("/adminapi/file/scan_upload")
public class ScanUploadController {
    
    private static final Logger logger = LoggerFactory.getLogger(ScanUploadController.class);
    
    @Autowired
    private ScanUploadService scanUploadService;
    
    /**
     * Generate QR code for scan upload
     * @param pid Category ID
     * @return QR code info
     */
    @GetMapping("/qrcode")
    public Result<Map<String, String>> generateQrCode(
            @RequestParam(value = "pid", required = false, defaultValue = "0") Integer pid) {
        try {
            // For now, use hardcoded user ID
            // In a real application, get this from the authenticated user
            Integer userId = 1;
            
            Map<String, String> qrCodeInfo = scanUploadService.generateQrCode(pid, userId);
            return Result.success(qrCodeInfo);
        } catch (Exception e) {
            logger.error("Failed to generate QR code", e);
            return Result.failure("生成二维码失败：" + e.getMessage());
        }
    }
    
    /**
     * Get uploaded files by token
     * @param token Scan token
     * @return List of uploaded files
     */
    @GetMapping("/image/{token}")
    public Result<List<FileAttachment>> getUploadedFiles(@PathVariable String token) {
        try {
            List<FileAttachment> files = scanUploadService.getUploadedFiles(token);
            return Result.success(files);
        } catch (Exception e) {
            logger.error("Failed to get uploaded files", e);
            return Result.failure("获取上传文件失败：" + e.getMessage());
        }
    }
    
    /**
     * Upload file via scan
     * @param file File to upload
     * @param token Scan token
     * @return Uploaded file info
     */
    @PostMapping("/image/{token}")
    public Result<Map<String, String>> uploadViaScan(
            @RequestParam("file") MultipartFile file,
            @PathVariable String token) {
        if (file == null || file.isEmpty()) {
            logger.warn("Upload failed: Empty file received");
            return Result.failure("上传文件不能为空");
        }
        
        try {
            Map<String, String> result = scanUploadService.uploadViaScan(file, token);
            return Result.success(result);
        } catch (Exception e) {
            logger.error("Failed to upload file via scan", e);
            return Result.failure("通过扫描上传文件失败：" + e.getMessage());
        }
    }
    
    /**
     * Delete scan upload token
     * @return Success message
     */
    @DeleteMapping("/qrcode")
    public Result<String> deleteToken() {
        try {
            // In a real application, we would delete the token associated with the current user
            // For now, we'll just return success
            return Result.success("清除成功");
        } catch (Exception e) {
            logger.error("Failed to delete token", e);
            return Result.failure("清除令牌失败：" + e.getMessage());
        }
    }
} 