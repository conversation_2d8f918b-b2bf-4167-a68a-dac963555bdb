package com.logic.code.controller.app;

import com.logic.code.common.response.Result;
import com.logic.code.entity.goods.Brand;
import com.logic.code.service.BrandService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/wechat/brand")
public class WechatBrandController {

    @Autowired
    private BrandService brandService;

    @GetMapping
    public Result<Brand> query(Integer id) {
        return Result.success(brandService.queryById(id));
    }

}
