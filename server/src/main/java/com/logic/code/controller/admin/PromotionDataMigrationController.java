package com.logic.code.controller.admin;

import com.logic.code.common.response.Result;
import com.logic.code.service.PromotionDataMigrationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 推广数据迁移管理控制器
 * 用于管理员执行推广数据迁移操作
 */
@Slf4j
@RestController
@RequestMapping("/admin/promotion/migration")
public class PromotionDataMigrationController {

    @Autowired
    private PromotionDataMigrationService migrationService;

    /**
     * 执行推广数据迁移
     * 将历史订单数据迁移到推广收益表中
     */
    @PostMapping("/execute")
    public Result<Map<String, Object>> executeDataMigration() {
        try {
            log.info("管理员请求执行推广数据迁移");
            Map<String, Object> result = migrationService.migratePromotionData();
            
            if ((Boolean) result.get("success")) {
                return Result.success(result);
            } else {
                return Result.failure((String) result.get("message"));
            }

        } catch (Exception e) {
            log.error("执行推广数据迁移失败: {}", e.getMessage(), e);
            return Result.failure("执行推广数据迁移失败: " + e.getMessage());
        }
    }

    /**
     * 获取迁移统计信息
     */
    @GetMapping("/stats")
    public Result<Map<String, Object>> getMigrationStats() {
        try {
            Map<String, Object> stats = migrationService.getMigrationStats();
            return Result.success(stats);

        } catch (Exception e) {
            log.error("获取迁移统计信息失败: {}", e.getMessage(), e);
            return Result.failure("获取迁移统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 清空推广收益数据
     * 谨慎使用，会删除所有推广收益记录
     */
    @DeleteMapping("/clear")
    public Result<Map<String, Object>> clearPromotionEarnings(
            @RequestParam(value = "confirm", defaultValue = "false") Boolean confirm) {

        if (!confirm) {
            return Result.failure("请确认清空操作，设置 confirm=true 参数");
        }

        try {
            log.warn("管理员请求清空推广收益数据");
            Map<String, Object> result = migrationService.clearPromotionEarnings();

            if ((Boolean) result.get("success")) {
                return Result.success(result);
            } else {
                return Result.failure((String) result.get("message"));
            }

        } catch (Exception e) {
            log.error("清空推广收益数据失败: {}", e.getMessage(), e);
            return Result.failure("清空推广收益数据失败: " + e.getMessage());
        }
    }

    /**
     * 预览迁移数据
     * 不实际执行迁移，只返回将要迁移的数据统计
     */
    @GetMapping("/preview")
    public Result<Map<String, Object>> previewMigration() {
        try {
            Map<String, Object> stats = migrationService.getMigrationStats();

            // 添加预览说明
            stats.put("preview", true);
            stats.put("description", "这是预览数据，实际迁移请调用 /execute 接口");

            return Result.success(stats);

        } catch (Exception e) {
            log.error("预览迁移数据失败: {}", e.getMessage(), e);
            return Result.failure("预览迁移数据失败: " + e.getMessage());
        }
    }
}
