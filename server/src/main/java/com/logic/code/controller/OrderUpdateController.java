package com.logic.code.controller;

import com.logic.code.service.OrderUpdateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;

/**
 * 订单信息更新控制器
 */
@Slf4j
@RestController
@RequestMapping("/admin/order")
public class OrderUpdateController {

    @Resource
    private OrderUpdateService orderUpdateService;

    /**
     * 根据Excel文件更新订单信息
     * @param filePath Excel文件路径
     * @return 操作结果
     */
    @PostMapping("/update-from-excel")
    public String updateOrdersFromExcel(@RequestParam String filePath) {
        try {
            orderUpdateService.updateOrdersFromExcel(filePath);
            return "订单信息更新完成";
        } catch (Exception e) {
            log.error("更新订单信息失败: {}", e.getMessage(), e);
            return "更新订单信息失败: " + e.getMessage();
        }
    }
}