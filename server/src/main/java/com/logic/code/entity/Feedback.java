package com.logic.code.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;

@TableName( "weshop_feedback")
public class Feedback {
    @TableId(type = IdType.AUTO)
    private Integer msgId;

    private Integer parentId;

    private Integer userId;

    private String userName;

    private String userEmail;

    private String msgTitle;

    private Boolean msgType;

    private Boolean msgStatus;

    private Date msgTime;

    private String messageImg;

    private Integer orderId;

    private Boolean msgArea;

    private String msgContent;

    public Integer getMsgId() {
        return msgId;
    }

    public Feedback setMsgId(Integer msgId) {
        this.msgId = msgId;
        return this;
    }

    public Integer getParentId() {
        return parentId;
    }

    public Feedback setParentId(Integer parentId) {
        this.parentId = parentId;
        return this;
    }

    public Integer getUserId() {
        return userId;
    }

    public Feedback setUserId(Integer userId) {
        this.userId = userId;
        return this;
    }

    public String getUserName() {
        return userName;
    }

    public Feedback setUserName(String userName) {
        this.userName = userName;
        return this;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public Feedback setUserEmail(String userEmail) {
        this.userEmail = userEmail;
        return this;
    }

    public String getMsgTitle() {
        return msgTitle;
    }

    public Feedback setMsgTitle(String msgTitle) {
        this.msgTitle = msgTitle;
        return this;
    }

    public Boolean getMsgType() {
        return msgType;
    }

    public Feedback setMsgType(Boolean msgType) {
        this.msgType = msgType;
        return this;
    }

    public Boolean getMsgStatus() {
        return msgStatus;
    }

    public Feedback setMsgStatus(Boolean msgStatus) {
        this.msgStatus = msgStatus;
        return this;
    }

    public Date getMsgTime() {
        return msgTime;
    }

    public Feedback setMsgTime(Date msgTime) {
        this.msgTime = msgTime;
        return this;
    }

    public String getMessageImg() {
        return messageImg;
    }

    public Feedback setMessageImg(String messageImg) {
        this.messageImg = messageImg;
        return this;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public Feedback setOrderId(Integer orderId) {
        this.orderId = orderId;
        return this;
    }

    public Boolean getMsgArea() {
        return msgArea;
    }

    public Feedback setMsgArea(Boolean msgArea) {
        this.msgArea = msgArea;
        return this;
    }

    public String getMsgContent() {
        return msgContent;
    }

    public Feedback setMsgContent(String msgContent) {
        this.msgContent = msgContent;
        return this;
    }
}
