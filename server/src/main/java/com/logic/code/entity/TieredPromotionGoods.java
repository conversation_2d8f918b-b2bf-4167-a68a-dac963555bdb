package com.logic.code.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 阶梯推广商品配置实体
 */
@Data
@TableName("weshop_tiered_promotion_goods")
public class TieredPromotionGoods {
    
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 商品ID
     */
    private Integer goodsId;
    
    /**
     * 商品名称
     */
    private String goodsName;
    
    /**
     * 活动名称
     */
    private String name;
    
    /**
     * 是否启用：0-禁用，1-启用
     */
    private Boolean isActive;
    
    /**
     * 第1笔返现比例（%）
     */
    private BigDecimal tier1Rate;
    
    /**
     * 第2笔返现比例（%）
     */
    private BigDecimal tier2Rate;
    
    /**
     * 第3笔返现比例（%）
     */
    private BigDecimal tier3Rate;
    
    /**
     * 第4笔及以后返现比例（%）
     */
    private BigDecimal tier4PlusRate;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 创建者ID
     */
    private Integer creatorId;
    
    /**
     * 商品推广描述
     */
    private String description;
}