package com.logic.code.entity.file;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * File attachment entity
 */
@Data
@TableName("weshop_file_attachment")
public class FileAttachment {
    
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * File name
     */
    private String name;
    
    /**
     * File path
     */
    private String path;
    
    /**
     * File URL
     */
    private String url;
    
    /**
     * File size in bytes
     */
    private Long size;
    
    /**
     * File type (extension)
     */
    private String type;
    
    /**
     * Category ID
     */
    private Integer categoryId;
    
    /**
     * Upload user ID
     */
    private Integer userId;
    
    /**
     * Upload user name
     */
    private String userName;
    
    /**
     * Upload source (0: local, 1: online, 2: scan)
     */
    private Integer uploadSource;
    
    /**
     * Is the file deleted
     */
    private Boolean isDelete;
    
    /**
     * Creation time
     */
    private Date createTime;
    
    /**
     * Update time
     */
    private Date updateTime;
} 