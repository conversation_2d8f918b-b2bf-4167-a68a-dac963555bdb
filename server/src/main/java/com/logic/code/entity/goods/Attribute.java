package com.logic.code.entity.goods;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("weshop_attribute")
public class Attribute {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer attributeCategoryId;

    private String name;

    private Boolean inputType;

    private Byte sortOrder;

    @TableField("`values`")
    private String values;

    public Integer getId() {
        return id;
    }

    public Attribute setId(Integer id) {
        this.id = id;
        return this;
    }

    public Integer getAttributeCategoryId() {
        return attributeCategoryId;
    }

    public Attribute setAttributeCategoryId(Integer attributeCategoryId) {
        this.attributeCategoryId = attributeCategoryId;
        return this;
    }

    public String getName() {
        return name;
    }

    public Attribute setName(String name) {
        this.name = name;
        return this;
    }

    public Boolean getInputType() {
        return inputType;
    }

    public Attribute setInputType(Boolean inputType) {
        this.inputType = inputType;
        return this;
    }

    public Byte getSortOrder() {
        return sortOrder;
    }

    public Attribute setSortOrder(Byte sortOrder) {
        this.sortOrder = sortOrder;
        return this;
    }

    public String getValues() {
        return values;
    }

    public Attribute setValues(String values) {
        this.values = values;
        return this;
    }
}
