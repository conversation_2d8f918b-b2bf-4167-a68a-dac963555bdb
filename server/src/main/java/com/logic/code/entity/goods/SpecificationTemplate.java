package com.logic.code.entity.goods;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 规格模板实体类
 * 用于存储商品规格模板信息
 */
@TableName("weshop_specification_template")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SpecificationTemplate implements Serializable {

    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 规格模板名称
     */
    private String ruleName;

    /**
     * 规格名称
     */
    private String attrName;

    /**
     * 规格值，JSON格式存储
     */
    private String attrValue;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 状态：1启用，0禁用
     */
    private Integer status;

}
