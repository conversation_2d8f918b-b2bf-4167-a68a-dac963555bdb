package com.logic.code.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("weshop_ad")
public class Ad implements Serializable {
    @TableId(type = IdType.AUTO)
    private Short id;

    private Short adPositionId;

    private Byte mediaType;

    private String name;

    private String link;

    private String content;

    private Date endTime;

    private Byte enabled;

    private String imageUrl;

    public Short getId() {
        return id;
    }

    public Ad setId(Short id) {
        this.id = id;
        return this;
    }

    public Short getAdPositionId() {
        return adPositionId;
    }

    public Ad setAdPositionId(Short adPositionId) {
        this.adPositionId = adPositionId;
        return this;
    }

    public Byte getMediaType() {
        return mediaType;
    }

    public Ad setMediaType(Byte mediaType) {
        this.mediaType = mediaType;
        return this;
    }

    public String getName() {
        return name;
    }

    public Ad setName(String name) {
        this.name = name;
        return this;
    }

    public String getLink() {
        return link;
    }

    public Ad setLink(String link) {
        this.link = link;
        return this;
    }

    public String getContent() {
        return content;
    }

    public Ad setContent(String content) {
        this.content = content;
        return this;
    }

    public Date getEndTime() {
        return endTime;
    }

    public Ad setEndTime(Date endTime) {
        this.endTime = endTime;
        return this;
    }

    public Byte getEnabled() {
        return enabled;
    }

    public Ad setEnabled(Byte enabled) {
        this.enabled = enabled;
        return this;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public Ad setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
        return this;
    }
}
