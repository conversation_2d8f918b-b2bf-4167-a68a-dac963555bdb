package com.logic.code.entity.goods;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 商品标签关联实体类
 */
@TableName("weshop_goods_label")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GoodsLabel implements Serializable {

    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 商品ID
     */
    private Integer goodsId;

    /**
     * 标签ID
     */
    private Integer labelId;

    /**
     * 创建时间
     */
    private Date createTime;
}
