package com.logic.code.entity.goods;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@TableName("weshop_brand")
@Data
public class Brand implements Serializable {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private String name;

    private String listPicUrl;

    private String simpleDesc;

    private String picUrl;

    private Byte sortOrder;

    private Boolean isShow;

    private BigDecimal floorPrice;

    private String appListPicUrl;

    private Boolean isNewly;

    private String newPicUrl;

    private Byte newSortOrder;


}
