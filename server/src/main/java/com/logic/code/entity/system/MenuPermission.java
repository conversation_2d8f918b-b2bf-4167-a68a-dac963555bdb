package com.logic.code.entity.system;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 菜单权限实体类
 * 
 * <AUTHOR>
 * @date 2025/7/7
 */
@TableName("weshop_menu_permission")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MenuPermission implements Serializable {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 菜单ID
     */
    private Integer menuId;
    
    /**
     * 权限标识
     */
    private String permission;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
