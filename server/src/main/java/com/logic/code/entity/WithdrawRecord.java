package com.logic.code.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 提现记录实体
 */
@Data
@TableName("weshop_withdraw_record")
public class WithdrawRecord {
    
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 用户ID
     */
    private Integer userId;
    
    /**
     * 提现金额
     */
    private BigDecimal amount;
    
    /**
     * 手续费
     */
    private BigDecimal fee;
    
    /**
     * 实际到账金额
     */
    private BigDecimal actualAmount;
    
    /**
     * 收款方式：wechat-微信，alipay-支付宝，bank-银行卡
     */
    private String paymentMethod;
    
    /**
     * 收款账户信息
     */
    private String accountInfo;
    
    /**
     * 银行卡信息ID（关联银行卡表）
     */
    private Integer bankCardId;
    
    /**
     * 银行卡信息快照（JSON格式存储提现时的银行卡信息）
     */
    private String bankCardSnapshot;
    
    /**
     * 提现状态：pending-待处理，processing-处理中，success-成功，failed-失败
     */
    private String status;
    
    /**
     * 申请时间
     */
    private Date applyTime;
    
    /**
     * 处理时间
     */
    private Date processTime;
    
    /**
     * 完成时间
     */
    private Date completeTime;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 获取银行卡快照信息（解析JSON）
     * @return 银行卡快照信息对象
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public BankCardSnapshot getBankCardSnapshotObject() {
        if (bankCardSnapshot == null || bankCardSnapshot.trim().isEmpty()) {
            return null;
        }
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(bankCardSnapshot, BankCardSnapshot.class);
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 银行卡快照信息内部类
     */
    @Data
    public static class BankCardSnapshot {
        private String bankName;
        private String cardNumber;
        private String cardHolder;
        private String bankAddress;
        private String cardImageUrl;
    }
}
