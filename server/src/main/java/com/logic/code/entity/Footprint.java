package com.logic.code.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@TableName( "weshop_footprint")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Footprint implements Serializable {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer userId;

    private Integer goodsId;

    private Date createTime;

    public Integer getId() {
        return id;
    }

    public Footprint setId(Integer id) {
        this.id = id;
        return this;
    }

    public Integer getUserId() {
        return userId;
    }

    public Footprint setUserId(Integer userId) {
        this.userId = userId;
        return this;
    }

    public Integer getGoodsId() {
        return goodsId;
    }

    public Footprint setGoodsId(Integer goodsId) {
        this.goodsId = goodsId;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public Footprint setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    @Override
    public String toString() {
        return "Footprint{" +
                "id=" + id +
                ", userId=" + userId +
                ", goodsId=" + goodsId +
                ", createTime=" + createTime +
                '}';
    }
}
