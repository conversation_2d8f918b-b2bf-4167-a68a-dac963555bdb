package com.logic.code.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 充值记录实体类
 * <AUTHOR>
 * @date 2025/7/24
 */
@TableName("weshop_recharge_record")
@Data
public class RechargeRecord {
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 充值金额
     */
    private BigDecimal amount;

    /**
     * 赠送优惠券数量
     */
    private Integer couponCount;

    /**
     * 赠送优惠券面额
     */
    private BigDecimal couponAmount;

    /**
     * 充值状态：0-待支付，1-已支付，2-已取消
     */
    private Integer status;

    /**
     * 支付方式：1-微信支付
     */
    private Integer paymentMethod;

    /**
     * 微信支付订单号
     */
    private String wxOrderId;

    /**
     * 微信支付交易号
     */
    private String wxTransactionId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 备注
     */
    private String remark;
}