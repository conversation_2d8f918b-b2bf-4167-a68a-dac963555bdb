package com.logic.code.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;


@TableName( "weshop_topic_category")
public class TopicCategory {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private String title;

    private String picUrl;

    public Integer getId() {
        return id;
    }

    public TopicCategory setId(Integer id) {
        this.id = id;
        return this;
    }

    public String getTitle() {
        return title;
    }

    public TopicCategory setTitle(String title) {
        this.title = title;
        return this;
    }

    public String getPicUrl() {
        return picUrl;
    }

    public TopicCategory setPicUrl(String picUrl) {
        this.picUrl = picUrl;
        return this;
    }
}
