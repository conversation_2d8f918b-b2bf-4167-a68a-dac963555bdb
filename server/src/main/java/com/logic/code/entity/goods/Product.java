package com.logic.code.entity.goods;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@TableName("weshop_product")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Product implements Serializable {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer goodsId;

    private String goodsSpecificationIds;

    private String goodsSn;

    private Integer goodsNumber;

    private BigDecimal retailPrice;

    /**
     * 成本价
     */
    private BigDecimal costPrice;

    /**
     * 划线价/原价
     */
    private BigDecimal otPrice;

    /**
     * 商品编码
     */
    private String barCode;

    /**
     * 条形码
     */
    private String barCodeNumber;

    /**
     * 商品重量(KG)
     */
    private BigDecimal goodsWeight;

    /**
     * 商品体积(m³)
     */
    private BigDecimal goodsVolume;

    /**
     * 规格图片URL
     */
    private String picUrl;

    /**
     * 一级分销佣金
     */
    private BigDecimal brokerage;

    /**
     * 二级分销佣金
     */
    private BigDecimal brokerageTwo;

    /**
     * 限购数量，0为不限购
     */
    private Integer quota;

    /**
     * 是否显示限购数量：1显示，0不显示
     */
    private Boolean quotaShow;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}
