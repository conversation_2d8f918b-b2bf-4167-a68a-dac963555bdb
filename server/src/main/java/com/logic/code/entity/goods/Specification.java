package com.logic.code.entity.goods;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 规格实体类
 * 用于存储商品规格类型信息
 */
@TableName("weshop_specification")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Specification implements Serializable {

    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 规格名称
     */
    private String name;

    /**
     * 排序
     */
    private Byte sortOrder;

}
