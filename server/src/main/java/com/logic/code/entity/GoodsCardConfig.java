package com.logic.code.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/5/4 10:10
 * @desc
 */
@Data
@TableName("weshop_goods_card_config")
public class GoodsCardConfig {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private Integer goodsId;
    private String cardNo;
}
