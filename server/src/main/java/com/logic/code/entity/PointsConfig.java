package com.logic.code.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 积分配置实体类
 */
@Data
@Accessors(chain = true)
@TableName("weshop_points_config")
public class PointsConfig {
    
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 积分获得比例（每消费多少元获得1积分）
     */
    private BigDecimal earnRate;
    
    /**
     * 积分使用比例（多少积分抵扣1元）
     */
    private BigDecimal useRate;
    
    /**
     * 最少使用积分数量
     */
    private Integer minUsePoints;
    
    /**
     * 最大使用比例（订单金额的百分比）
     */
    private BigDecimal maxUseRatio;
    
    /**
     * 是否启用积分系统
     */
    private Boolean isEnabled;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
}