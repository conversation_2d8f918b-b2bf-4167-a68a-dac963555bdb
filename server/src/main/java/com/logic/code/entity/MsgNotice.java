package com.logic.code.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/5/30 15:13
 * @desc
 */
@Data
@TableName("weshop_msg_notice")
public class MsgNotice {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private Integer userId;
    private String templId;
    private Integer num;
}
