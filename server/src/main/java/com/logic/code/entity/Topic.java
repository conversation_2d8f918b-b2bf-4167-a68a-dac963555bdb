package com.logic.code.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@TableName( "weshop_topic")
@Data
public class Topic implements Serializable {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private String title;

    private String avatar;

    private String itemPicUrl;

    private String subtitle;

    private Integer topicCategoryId;

    private BigDecimal priceInfo;

    private String readCount;

    private String scenePicUrl;

    private Integer topicTemplateId;

    private Integer topicTagId;

    private Integer sortOrder;

    private Boolean isShow;

    private String content;

}
