package com.logic.code.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 银行卡信息实体类
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@Accessors(chain = true)
@TableName("weshop_bank_card_info")
public class BankCardInfo {
    
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 用户ID
     */
    private Integer userId;
    
    /**
     * 开户行名称
     */
    private String bankName;
    
    /**
     * 银行卡号（加密存储）
     */
    private String cardNumber;
    
    /**
     * 持卡人姓名
     */
    private String cardHolder;
    
    /**
     * 开户行地址
     */
    private String bankAddress;
    
    /**
     * 银行卡照片URL
     */
    private String cardImageUrl;
    
    /**
     * 是否默认银行卡：0-否，1-是
     */
    private Boolean isDefault;
    
    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 获取脱敏的银行卡号（用于显示）
     * @return 脱敏后的银行卡号，如：**** **** **** 1234
     */
    public String getMaskedCardNumber() {
        if (cardNumber == null || cardNumber.length() < 4) {
            return "****";
        }
        // 只显示最后4位数字
        String lastFour = cardNumber.substring(cardNumber.length() - 4);
        return "**** **** **** " + lastFour;
    }
    
    /**
     * 创建银行卡快照信息（用于提现记录）
     * @return 包含银行卡信息的JSON字符串格式
     */
    public String createSnapshot() {
        return String.format("{\"bankName\":\"%s\",\"cardNumber\":\"%s\",\"cardHolder\":\"%s\",\"bankAddress\":\"%s\",\"cardImageUrl\":\"%s\"}", 
                            bankName, getMaskedCardNumber(), cardHolder, bankAddress, cardImageUrl);
    }
}