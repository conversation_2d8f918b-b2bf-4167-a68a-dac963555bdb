package com.logic.code.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;

@TableName("weshop_coupon")
public class Coupon {
    @TableId(type = IdType.AUTO)
    private Short id;

    private String name;

    private BigDecimal typeMoney;

    private Byte sendType;

    private BigDecimal minAmount;

    private BigDecimal maxAmount;

    private Integer sendStartDate;

    private Integer sendEndDate;

    private Integer useStartDate;

    private Integer useEndDate;

    private BigDecimal minGoodsAmount;

    public Short getId() {
        return id;
    }

    public Coupon setId(Short id) {
        this.id = id;
        return this;
    }

    public String getName() {
        return name;
    }

    public Coupon setName(String name) {
        this.name = name;
        return this;
    }

    public BigDecimal getTypeMoney() {
        return typeMoney;
    }

    public Coupon setTypeMoney(BigDecimal typeMoney) {
        this.typeMoney = typeMoney;
        return this;
    }

    public Byte getSendType() {
        return sendType;
    }

    public Coupon setSendType(Byte sendType) {
        this.sendType = sendType;
        return this;
    }

    public BigDecimal getMinAmount() {
        return minAmount;
    }

    public Coupon setMinAmount(BigDecimal minAmount) {
        this.minAmount = minAmount;
        return this;
    }

    public BigDecimal getMaxAmount() {
        return maxAmount;
    }

    public Coupon setMaxAmount(BigDecimal maxAmount) {
        this.maxAmount = maxAmount;
        return this;
    }

    public Integer getSendStartDate() {
        return sendStartDate;
    }

    public Coupon setSendStartDate(Integer sendStartDate) {
        this.sendStartDate = sendStartDate;
        return this;
    }

    public Integer getSendEndDate() {
        return sendEndDate;
    }

    public Coupon setSendEndDate(Integer sendEndDate) {
        this.sendEndDate = sendEndDate;
        return this;
    }

    public Integer getUseStartDate() {
        return useStartDate;
    }

    public Coupon setUseStartDate(Integer useStartDate) {
        this.useStartDate = useStartDate;
        return this;
    }

    public Integer getUseEndDate() {
        return useEndDate;
    }

    public Coupon setUseEndDate(Integer useEndDate) {
        this.useEndDate = useEndDate;
        return this;
    }

    public BigDecimal getMinGoodsAmount() {
        return minGoodsAmount;
    }

    public Coupon setMinGoodsAmount(BigDecimal minGoodsAmount) {
        this.minGoodsAmount = minGoodsAmount;
        return this;
    }
}
