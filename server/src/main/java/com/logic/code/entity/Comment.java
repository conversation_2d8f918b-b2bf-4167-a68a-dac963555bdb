package com.logic.code.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDateTime;

@TableName("weshop_comment")
public class Comment {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer typeId;

    private Integer valueId;

    /**
     * 储存为base64编码
     */
    private String content;

    private LocalDateTime createTime;

    private Byte status;

    private Integer userId;

    private String newContent;

    /**
     * 星级评分(1-5星)
     */
    private Integer rating;

    public Integer getId() {
        return id;
    }

    public Comment setId(Integer id) {
        this.id = id;
        return this;
    }

    public Integer getTypeId() {
        return typeId;
    }

    public Comment setTypeId(Integer typeId) {
        this.typeId = typeId;
        return this;
    }

    public Integer getValueId() {
        return valueId;
    }

    public Comment setValueId(Integer valueId) {
        this.valueId = valueId;
        return this;
    }

    public String getContent() {
        return content;
    }

    public Comment setContent(String content) {
        this.content = content;
        return this;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public Comment setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
        return this;
    }

    public Byte getStatus() {
        return status;
    }

    public Comment setStatus(Byte status) {
        this.status = status;
        return this;
    }

    public Integer getUserId() {
        return userId;
    }

    public Comment setUserId(Integer userId) {
        this.userId = userId;
        return this;
    }

    public String getNewContent() {
        return newContent;
    }

    public Comment setNewContent(String newContent) {
        this.newContent = newContent;
        return this;
    }

    public Integer getRating() {
        return rating;
    }

    public Comment setRating(Integer rating) {
        this.rating = rating;
        return this;
    }
}
