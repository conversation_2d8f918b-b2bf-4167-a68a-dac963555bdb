package com.logic.code.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/3 11:16
 * @desc
 */
@Data
@TableName("weshop_card")
public class Card {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private String phone;
    private Date startDate;
    private Date endDate;
    private String no;
    private String code;
    private Integer createUser;
    private Date createDate;
    private Integer status;
    private Integer type;
    private String name;
    private String goodsId;

}
