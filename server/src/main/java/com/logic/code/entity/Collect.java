package com.logic.code.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@TableName("weshop_collect")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Collect {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer userId;

    private Integer valueId;

    private Date createTime;

    /**
     * 是否是关注
     */
    private Boolean isAttention;

    private Integer typeId;


}
