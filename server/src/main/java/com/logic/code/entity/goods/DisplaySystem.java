package com.logic.code.entity.goods;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 商品展示系统配置实体类
 * 
 * <AUTHOR>
 * @date 2025/7/6
 */
@TableName("weshop_display_system")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DisplaySystem implements Serializable {
    
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 系统名称
     */
    private String name;
    
    /**
     * 系统编码
     */
    private String code;
    
    /**
     * 系统描述
     */
    private String description;
    
    /**
     * 系统图标
     */
    private String icon;
    
    /**
     * 系统颜色
     */
    private String color;
    
    /**
     * 排序
     */
    private Integer sortOrder;
    
    /**
     * 是否启用：1启用，0禁用
     */
    private Boolean isEnabled;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
}
