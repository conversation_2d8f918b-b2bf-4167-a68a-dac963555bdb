package com.logic.code.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 推广海报配置实体类
 * 
 * <AUTHOR>
 * @date 2025/09/03
 */
@Data
@TableName("weshop_promotion_poster_config")
public class PromotionPosterConfig implements Serializable {
    
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 海报模板名称
     */
    private String name;
    
    /**
     * 海报标题
     */
    private String title;
    
    /**
     * 海报描述文字
     */
    private String description;
    
    /**
     * 背景图片URL
     */
    private String backgroundImage;
    
    /**
     * Logo图片URL
     */
    private String logoImage;
    
    /**
     * 模板缩略图URL
     */
    private String thumbnailUrl;
    
    /**
     * 模板类型
     */
    private String templateType;
    
    /**
     * 是否为默认模板
     */
    private Boolean isDefault;
    
    /**
     * 是否启用
     */
    private Boolean isActive;
    
    /**
     * 排序
     */
    private Integer sortOrder;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
}
