package com.logic.code.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户商品推广统计实体
 */
@Data
@TableName("weshop_user_goods_promotion_stats")
public class UserGoodsPromotionStats {
    
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 推广者用户ID
     */
    private Integer promoterId;
    
    /**
     * 商品ID
     */
    private Integer goodsId;
    
    /**
     * 该商品总推广次数
     */
    private Integer totalPromotionCount;
    
    /**
     * 第1阶梯推广次数
     */
    private Integer tier1Count;
    
    /**
     * 第2阶梯推广次数
     */
    private Integer tier2Count;
    
    /**
     * 第3阶梯推广次数
     */
    private Integer tier3Count;
    
    /**
     * 第4阶梯及以后推广次数
     */
    private Integer tier4PlusCount;
    
    /**
     * 该商品总佣金
     */
    private BigDecimal totalCommission;
    
    /**
     * 首次推广时间
     */
    private Date firstPromotionTime;
    
    /**
     * 最后推广时间
     */
    private Date lastPromotionTime;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
}