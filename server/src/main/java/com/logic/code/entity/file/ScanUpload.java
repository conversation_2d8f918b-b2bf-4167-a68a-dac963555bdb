package com.logic.code.entity.file;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * Scan upload entity for QR code uploads
 */
@Data
@TableName("weshop_scan_upload")
public class ScanUpload {
    
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * Scan token
     */
    private String scanToken;
    
    /**
     * Category ID
     */
    private Integer categoryId;
    
    /**
     * User ID
     */
    private Integer userId;
    
    /**
     * Status (0: pending, 1: completed)
     */
    private Integer status;
    
    /**
     * Expiration time
     */
    private Date expireTime;
    
    /**
     * Creation time
     */
    private Date createTime;
    
    /**
     * Update time
     */
    private Date updateTime;
} 