package com.logic.code.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 会员日提示信息实体类
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("member_day_tip")
public class MemberDayTip {
    
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 提示文本内容
     */
    private String tipText;
    
    /**
     * 是否启用 (0-禁用, 1-启用)
     */
    private Integer isEnabled;
    
    /**
     * 显示位置 (goods-商品页, index-首页, all-全部)
     */
    private String displayPosition;
    
    /**
     * 优先级 (数字越大优先级越高)
     */
    private Integer priority;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建者
     */
    private String createBy;
    
    /**
     * 更新者
     */
    private String updateBy;
}