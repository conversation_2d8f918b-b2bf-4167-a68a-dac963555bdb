package com.logic.code.entity.file;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * File category entity
 */
@Data
@TableName("weshop_file_category")
public class FileCategory {

    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * Category name
     */
    @NotBlank(message = "分类名称不能为空")
    @Size(max = 50, message = "分类名称长度不能超过50个字符")
    private String title;

    /**
     * Parent category ID
     */
    private Integer parentId;

    /**
     * Sort order
     */
    private Integer sortOrder;

    /**
     * Category level (0-root, 1-first level, 2-second level, etc.)
     */
    private Integer level;

    /**
     * Category path (e.g., "0,1,2")
     */
    private String path;

    /**
     * Category description
     */
    @Size(max = 200, message = "分类描述长度不能超过200个字符")
    private String description;

    /**
     * Category icon
     */
    private String icon;

    /**
     * Is the category deleted
     */
    private Boolean isDelete;

    /**
     * Creation time
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * Update time
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * Children categories (not stored in database)
     */
    @TableField(exist = false)
    private List<FileCategory> children;

    /**
     * File count in this category (not stored in database)
     */
    @TableField(exist = false)
    private Integer fileCount;

    /**
     * Parent category name (not stored in database)
     */
    @TableField(exist = false)
    private String parentName;
}
