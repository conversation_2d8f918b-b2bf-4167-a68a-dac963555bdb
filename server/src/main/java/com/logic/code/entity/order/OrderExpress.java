package com.logic.code.entity.order;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@TableName("weshop_order_express")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrderExpress {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer orderId;

    private Integer shipperId;

    /**
     * 物流公司名称
     */
    private String shipperName;

    /**
     * 物流公司代码
     */
    private String shipperCode;

    /**
     * 快递单号
     */
    private String logisticCode;

    /**
     * 物流跟踪信息
     */
    private String traces;

    private Boolean isFinish;

    /**
     * 总查询次数
     */
    private Integer requestCount;

    /**
     * 最近一次向第三方查询物流信息时间
     */
    private Date requestTime;

    /**
     * 添加时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private String logisticsStatus;

}
