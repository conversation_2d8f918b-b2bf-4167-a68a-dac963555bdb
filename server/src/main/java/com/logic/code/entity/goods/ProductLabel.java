package com.logic.code.entity.goods;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 商品标签实体类
 */
@TableName("weshop_product_label")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProductLabel implements Serializable {

    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 分类ID
     */
    private Integer cateId;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 样式类型：1自定义，2图片
     */
    private Integer type;

    /**
     * 字体颜色
     */
    private String fontColor;

    /**
     * 背景颜色
     */
    private String bgColor;

    /**
     * 边框颜色
     */
    private String borderColor;

    /**
     * 标签图片URL
     */
    private String image;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 是否显示：1显示，0隐藏
     */
    private Integer isShow;

    /**
     * 状态：1启用，0禁用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 非数据库字段，分类名称
     */
    @TableField(exist = false)
    private String categoryName;
}
