package com.logic.code.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


@TableName( "weshop_keywords")
@Data
public class Keywords {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private String keyword;

    private Boolean hot;

    private Boolean isDefault;

    private Boolean isShow;

    private Integer sortOrder;

    /**
     * 关键词的跳转链接
     */
    private String schemeUrl;

    private Integer type;


}
