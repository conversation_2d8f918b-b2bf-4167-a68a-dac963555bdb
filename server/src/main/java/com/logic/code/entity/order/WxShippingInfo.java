package com.logic.code.entity.order;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 微信发货信息管理实体类
 * 用于存储向微信平台上传的发货信息
 */
@TableName("weshop_wx_shipping_info")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WxShippingInfo {
    
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 订单ID
     */
    private Integer orderId;

    /**
     * 微信支付交易单号
     */
    private String transactionId;

    /**
     * 商户号
     */
    private String mchid;

    /**
     * 商户订单号
     */
    private String outTradeNo;

    /**
     * 用户openid
     */
    private String openid;

    /**
     * 物流模式：1-实体物流配送，2-同城配送，3-虚拟商品，4-用户自提
     */
    private Integer logisticsType;

    /**
     * 发货模式：1-统一发货，2-分拆发货
     */
    private Integer deliveryMode;

    /**
     * 是否全部发货完成（分拆发货时使用）
     */
    private Boolean isAllDelivered;

    /**
     * 物流信息JSON，包含物流单号、快递公司等
     */
    private String shippingList;

    /**
     * 上传到微信的时间
     */
    private Date uploadTime;

    /**
     * 微信返回状态：0-待上传，1-上传成功，2-上传失败
     */
    private Integer wxStatus;

    /**
     * 微信返回错误信息
     */
    private String wxErrorMsg;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
