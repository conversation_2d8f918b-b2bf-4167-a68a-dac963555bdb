package com.logic.code.entity.goods;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("weshop_attribute_category")
public class AttributeCategory {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private String name;

    private Boolean enabled;

    // 非数据库字段，用于统计该分类下的参数数量
    @TableField(exist = false)
    private Integer attributeCount;

    public Integer getId() {
        return id;
    }

    public AttributeCategory setId(Integer id) {
        this.id = id;
        return this;
    }

    public String getName() {
        return name;
    }

    public AttributeCategory setName(String name) {
        this.name = name;
        return this;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public AttributeCategory setEnabled(Boolean enabled) {
        this.enabled = enabled;
        return this;
    }

    public Integer getAttributeCount() {
        return attributeCount;
    }

    public AttributeCategory setAttributeCount(Integer attributeCount) {
        this.attributeCount = attributeCount;
        return this;
    }
}
