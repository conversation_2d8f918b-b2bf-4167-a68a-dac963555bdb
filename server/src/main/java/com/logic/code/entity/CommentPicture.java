package com.logic.code.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("weshop_comment_picture")
public class CommentPicture {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer commentId;

    private String picUrl;

    private Boolean sortOrder;

    public Integer getId() {
        return id;
    }

    public CommentPicture setId(Integer id) {
        this.id = id;
        return this;
    }

    public Integer getCommentId() {
        return commentId;
    }

    public CommentPicture setCommentId(Integer commentId) {
        this.commentId = commentId;
        return this;
    }

    public String getPicUrl() {
        return picUrl;
    }

    public CommentPicture setPicUrl(String picUrl) {
        this.picUrl = picUrl;
        return this;
    }

    public Boolean getSortOrder() {
        return sortOrder;
    }

    public CommentPicture setSortOrder(Boolean sortOrder) {
        this.sortOrder = sortOrder;
        return this;
    }
}
