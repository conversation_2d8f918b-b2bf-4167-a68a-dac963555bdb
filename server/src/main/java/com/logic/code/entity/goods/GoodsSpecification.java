package com.logic.code.entity.goods;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 商品规格实体类
 * 用于存储商品的规格信息
 */
@TableName("weshop_goods_specification")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GoodsSpecification implements Serializable {

    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 商品ID
     */
    private Integer goodsId;

    /**
     * 规格ID
     */
    private Integer specificationId;

    /**
     * 规格值
     */
    private String value;

    /**
     * 规格图片URL
     */
    private String picUrl;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 逻辑删除
     */
    private Boolean isDeleted;

}
