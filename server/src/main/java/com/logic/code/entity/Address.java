package com.logic.code.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("weshop_address")
public class Address {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private String name;

    private Integer userId;

    private Short countryId;

    private Short provinceId;

    private Short cityId;

    private Short districtId;

    private String address;

    private String mobile;

    private Boolean isDefault;

    public Integer getId() {
        return id;
    }

    public Address setId(Integer id) {
        this.id = id;
        return this;
    }

    public String getName() {
        return name;
    }

    public Address setName(String name) {
        this.name = name;
        return this;
    }

    public Integer getUserId() {
        return userId;
    }

    public Address setUserId(Integer userId) {
        this.userId = userId;
        return this;
    }

    public Short getCountryId() {
        return countryId;
    }

    public Address setCountryId(Short countryId) {
        this.countryId = countryId;
        return this;
    }

    public Short getProvinceId() {
        return provinceId;
    }

    public Address setProvinceId(Short provinceId) {
        this.provinceId = provinceId;
        return this;
    }

    public Short getCityId() {
        return cityId;
    }

    public Address setCityId(Short cityId) {
        this.cityId = cityId;
        return this;
    }

    public Short getDistrictId() {
        return districtId;
    }

    public Address setDistrictId(Short districtId) {
        this.districtId = districtId;
        return this;
    }

    public String getAddress() {
        return address;
    }

    public Address setAddress(String address) {
        this.address = address;
        return this;
    }

    public String getMobile() {
        return mobile;
    }

    public Address setMobile(String mobile) {
        this.mobile = mobile;
        return this;
    }

    public Boolean getIsDefault() {
        return isDefault;
    }

    public Address setIsDefault(Boolean isDefault) {
        this.isDefault = isDefault;
        return this;
    }
}
