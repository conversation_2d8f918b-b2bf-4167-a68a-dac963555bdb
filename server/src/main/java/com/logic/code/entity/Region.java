package com.logic.code.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.logic.code.common.enmus.RegionTypeEnum;

@TableName("weshop_region")
public class Region {
    @TableId(type = IdType.AUTO)
    private Short id;

    private Short parentId;

    private String name;

    private RegionTypeEnum type;

    private Short agencyId;

    public Short getId() {
        return id;
    }

    public Region setId(Short id) {
        this.id = id;
        return this;
    }

    public Short getParentId() {
        return parentId;
    }

    public Region setParentId(Short parentId) {
        this.parentId = parentId;
        return this;
    }

    public String getName() {
        return name;
    }

    public Region setName(String name) {
        this.name = name;
        return this;
    }

    public RegionTypeEnum getType() {
        return type;
    }

    public Region setType(RegionTypeEnum type) {
        this.type = type;
        return this;
    }

    public Short getAgencyId() {
        return agencyId;
    }

    public Region setAgencyId(Short agencyId) {
        this.agencyId = agencyId;
        return this;
    }
}
