package com.logic.code.entity.system;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 系统菜单实体类
 *
 * <AUTHOR>
 * @date 2025/7/7
 */
@TableName("weshop_system_menu")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SystemMenu implements Serializable {

    /**
     * 菜单ID
     */
    @TableId(type = IdType.INPUT)
    private Integer id;

    /**
     * 父级菜单ID
     */
    private Integer pid;

    /**
     * 菜单路径
     */
    private String path;

    /**
     * 菜单标题
     */
    private String title;

    /**
     * 菜单图标
     */
    private String icon;

    /**
     * 头部标识
     */
    private String header;

    /**
     * 是否为头部菜单
     */
    @JsonProperty("is_header")
    private Integer isHeader;

    /**
     * 是否显示
     */
    @JsonProperty("is_show")
    private Integer isShow;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 子菜单列表（不存储在数据库中）
     */
    @TableField(exist = false)
    private List<SystemMenu> children;

    /**
     * 权限列表（不存储在数据库中）
     */
    @TableField(exist = false)
    private List<String> auth;
}
