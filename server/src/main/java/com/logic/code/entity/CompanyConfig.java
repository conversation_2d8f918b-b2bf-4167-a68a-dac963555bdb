package com.logic.code.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 公司信息配置实体类
 * 
 * <AUTHOR>
 * @date 2025/09/03
 */
@Data
@TableName("weshop_company_config")
public class CompanyConfig implements Serializable {
    
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 公司名称
     */
    private String companyName;
    
    /**
     * 公司地址
     */
    private String companyAddress;
    
    /**
     * 联系电话
     */
    private String contactPhone;
    
    /**
     * 微信号
     */
    private String contactWechat;
    
    /**
     * 公司Logo
     */
    private String companyLogo;
    
    /**
     * 公司二维码
     */
    private String qrCodeUrl;
    
    /**
     * 工作时间
     */
    private String workTime;
    
    /**
     * 公司描述
     */
    private String description;
    
    /**
     * 是否启用
     */
    private Boolean isActive;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
}
