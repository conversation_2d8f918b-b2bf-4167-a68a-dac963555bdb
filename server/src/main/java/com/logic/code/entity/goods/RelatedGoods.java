package com.logic.code.entity.goods;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;


@TableName("weshop_related_goods")
public class RelatedGoods {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer goodsId;

    private Integer relatedGoodsId;

    public Integer getId() {
        return id;
    }

    public RelatedGoods setId(Integer id) {
        this.id = id;
        return this;
    }

    public Integer getGoodsId() {
        return goodsId;
    }

    public RelatedGoods setGoodsId(Integer goodsId) {
        this.goodsId = goodsId;
        return this;
    }

    public Integer getRelatedGoodsId() {
        return relatedGoodsId;
    }

    public RelatedGoods setRelatedGoodsId(Integer relatedGoodsId) {
        this.relatedGoodsId = relatedGoodsId;
        return this;
    }
}
