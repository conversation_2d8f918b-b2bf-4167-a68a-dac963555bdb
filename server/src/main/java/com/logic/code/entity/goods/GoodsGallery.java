package com.logic.code.entity.goods;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 商品轮播图实体类
 * 用于存储商品的轮播图片信息
 */
@TableName("weshop_goods_gallery")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GoodsGallery implements Serializable {

    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 商品ID
     */
    private Integer goodsId;

    /**
     * 图片URL
     */
    private String imgUrl;

    /**
     * 图片描述
     */
    private String imgDesc;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}
