package com.logic.code.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 推广等级配置实体类
 */
@Data
@Accessors(chain = true)
@TableName("weshop_promotion_level_config")
public class PromotionLevelConfig {
    
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 推广等级（1-5级）
     */
    private Integer level;
    
    /**
     * 等级名称
     */
    private String levelName;
    
    /**
     * 最小推广人数
     */
    private Integer minPromotionCount;
    
    /**
     * 最大推广人数（NULL表示无上限）
     */
    private Integer maxPromotionCount;
    
    /**
     * 每推广一人获得的积分
     */
    private Integer rewardPoints;
    
    /**
     * 等级描述
     */
    private String levelDescription;
    
    /**
     * 是否启用
     */
    private Boolean isEnabled;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
}