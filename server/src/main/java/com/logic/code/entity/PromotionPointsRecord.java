package com.logic.code.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 推广积分记录实体类
 */
@Data
@Accessors(chain = true)
@TableName("weshop_promotion_points_record")
public class PromotionPointsRecord {
    
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 推广者ID
     */
    private Integer promoterId;
    
    /**
     * 被推广用户ID
     */
    private Integer promotedUserId;
    
    /**
     * 推广时的等级
     */
    private Integer promotionLevel;
    
    /**
     * 获得的积分
     */
    private Integer rewardPoints;
    
    /**
     * 推广时间
     */
    private Date promotionTime;
    
    /**
     * 创建时间
     */
    private Date createTime;
}