package com.logic.code.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户优惠券实体类
 * <AUTHOR>
 * @date 2025/7/25
 */
@TableName("weshop_user_coupon")
@Data
public class UserCoupon {
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 优惠券模板ID
     */
    private Integer couponId;

    /**
     * 优惠券编号
     */
    private String couponNumber;

    /**
     * 优惠券标题
     */
    private String title;

    /**
     * 优惠券描述
     */
    private String description;

    /**
     * 优惠券类型：1-满减券，2-折扣券，3-免邮券
     */
    private Integer type;

    /**
     * 优惠券类型名称
     */
    private String typeName;

    /**
     * 优惠金额/折扣
     */
    private BigDecimal amount;

    /**
     * 最低消费金额
     */
    private BigDecimal minAmount;

    /**
     * 优惠券状态：0-可用，1-已使用，2-已过期
     */
    private Integer status;

    /**
     * 获得时间
     */
    private Date createTime;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 使用时间
     */
    private Date useTime;

    /**
     * 使用订单ID
     */
    private Integer orderId;

    /**
     * 最后使用的订单ID（历史记录）
     */
    private Integer lastUsedOrderId;

    /**
     * 最后使用时间（历史记录）
     */
    private Date lastUseTime;

    /**
     * 是否已退回：0-未退回，1-已退回
     */
    private Boolean isRefunded;

    /**
     * 退回时间
     */
    private Date refundTime;

    /**
     * 备注
     */
    private String remark;

    // 以下字段用于前端显示，不存储到数据库
    /**
     * 优惠券名称（用于前端显示）
     */
    @TableField(exist = false)
    private String name;

    public String getName() {
        return this.title != null ? this.title : "优惠券";
    }

    public void setName(String name) {
        this.name = name;
    }
}