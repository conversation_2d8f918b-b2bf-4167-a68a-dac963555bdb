package com.logic.code.entity.goods;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

@TableName("weshop_channel")
public class Channel implements Serializable {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private String name;

    private String url;

    private String iconUrl;

    private Integer sortOrder;

    public Integer getId() {
        return id;
    }

    public Channel setId(Integer id) {
        this.id = id;
        return this;
    }

    public String getName() {
        return name;
    }

    public Channel setName(String name) {
        this.name = name;
        return this;
    }

    public String getUrl() {
        return url;
    }

    public Channel setUrl(String url) {
        this.url = url;
        return this;
    }

    public String getIconUrl() {
        return iconUrl;
    }

    public Channel setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
        return this;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public Channel setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
        return this;
    }
}
