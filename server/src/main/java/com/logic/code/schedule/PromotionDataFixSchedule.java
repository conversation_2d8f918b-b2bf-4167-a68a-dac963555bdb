package com.logic.code.schedule;

import com.logic.code.service.PromotionCompensationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 推广数据修复定时任务
 * 定期检查并修复缺失的推广数据问题
 */
@Slf4j
@Component
public class PromotionDataFixSchedule {

    @Autowired
    private PromotionCompensationService promotionCompensationService;

    /**
     * 每天凌晨2点执行推广数据修复任务
     * 修复缺失的推广收益数据（批量处理，每批100条）
     */
    //@Scheduled(cron = "0 0 2 * * ?")
    public void fixMissingPromotionData() {
        try {
            log.info("开始执行推广数据修复定时任务...");
            
            // 执行批量推广收益数据补偿（每批100条）
            Map<String, Object> compensationResult = promotionCompensationService.compensateMissingEarningsBatch(100);
            
            log.info("推广数据修复任务执行完成，结果: {}", compensationResult);
            
            // 记录统计信息
            Map<String, Object> stats = promotionCompensationService.getMigrationStats();
            log.info("修复后统计数据: {}", stats);
            
        } catch (Exception e) {
            log.error("推广数据修复定时任务执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 每天凌晨3点执行推广收益状态更新任务
     * 更新已确认收货订单的推广收益状态
     */
    //@Scheduled(cron = "0 0 3 * * ?")
    public void updatePromotionEarningsStatus() {
        try {
            log.info("开始执行推广收益状态更新定时任务...");
            
            // 更新已确认收货订单的推广收益状态
            Map<String, Object> updateResult = promotionCompensationService.updateConfirmedEarningsStatus();
            
            log.info("推广收益状态更新任务执行完成，结果: {}", updateResult);
            
        } catch (Exception e) {
            log.error("推广收益状态更新定时任务执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 每周一凌晨4点执行全面数据检查
     * 检查并修复所有推广数据问题
     */
   // @Scheduled(cron = "0 0 4 * * MON")
    public void comprehensiveDataCheck() {
        try {
            log.info("开始执行全面推广数据检查定时任务...");
            
            // 获取统计数据
            Map<String, Object> statsBefore = promotionCompensationService.getMigrationStats();
            log.info("修复前统计数据: {}", statsBefore);
            
            // 执行批量推广收益数据补偿
            Map<String, Object> compensationResult = promotionCompensationService.compensateMissingEarningsBatch(100);
            log.info("数据补偿结果: {}", compensationResult);
            
            // 更新已确认收货订单的推广收益状态
            Map<String, Object> updateResult = promotionCompensationService.updateConfirmedEarningsStatus();
            log.info("状态更新结果: {}", updateResult);
            
            // 获取修复后统计数据
            Map<String, Object> statsAfter = promotionCompensationService.getMigrationStats();
            log.info("修复后统计数据: {}", statsAfter);
            
            log.info("全面推广数据检查定时任务执行完成");
            
        } catch (Exception e) {
            log.error("全面推广数据检查定时任务执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 每小时执行一次快速检查（仅在开发/测试环境启用）
     * 检查是否有新的缺失数据需要处理
     */
    //@Scheduled(cron = "0 0 * * * ?")
    public void quickDataCheck() {
        try {
            // 仅在非生产环境启用快速检查
            String env = System.getProperty("spring.profiles.active", "dev");
            if (!"prod".equals(env)) {
                log.debug("开始执行快速推广数据检查...");
                
                // 获取统计数据
                Map<String, Object> stats = promotionCompensationService.getMigrationStats();
                Long ordersWithoutEarningsCount = (Long) stats.get("ordersWithoutEarningsCount");
                
                if (ordersWithoutEarningsCount != null && ordersWithoutEarningsCount > 0) {
                    log.info("发现 {} 个订单缺少推广收益数据，准备执行补偿...", ordersWithoutEarningsCount);
                    
                    // 执行快速补偿（小批量）
                    Map<String, Object> compensationResult = promotionCompensationService.compensateMissingEarningsBatch(10);
                    log.info("快速数据补偿完成: {}", compensationResult);
                }
                
                log.debug("快速推广数据检查执行完成");
            }
        } catch (Exception e) {
            log.error("快速推广数据检查定时任务执行失败: {}", e.getMessage(), e);
        }
    }
}