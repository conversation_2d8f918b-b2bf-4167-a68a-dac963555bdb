package com.logic.code.schedule;

import com.logic.code.service.PromotionCompensationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 推广收益补偿定时任务
 * 定期检查并补偿缺失的推广收益数据
 */
@Slf4j
@Component
public class PromotionCompensationSchedule {

    @Autowired
    private PromotionCompensationService promotionCompensationService;

    /**
     * 每天凌晨2点执行推广收益数据补偿
     */
    //@Scheduled(cron = "0 */2 * * * ?")
    public void scheduledPromotionCompensation() {
        try {
            log.info("开始执行定时推广收益数据补偿任务...");
            promotionCompensationService.compensateMissingEarnings();
            log.info("定时推广收益数据补偿任务执行完成");
        } catch (Exception e) {
            log.error("定时推广收益数据补偿任务执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 每天凌晨3点更新已确认收货订单的推广收益状态
     */
    //@Scheduled(cron = "0 0 3 * * ?")
    public void scheduledUpdateConfirmedEarningsStatus() {
        try {
            log.info("开始执行定时更新已确认收货订单的推广收益状态任务...");
            promotionCompensationService.updateConfirmedEarningsStatus();
            log.info("定时更新已确认收货订单的推广收益状态任务执行完成");
        } catch (Exception e) {
            log.error("定时更新已确认收货订单的推广收益状态任务执行失败: {}", e.getMessage(), e);
        }
    }
}