package com.logic.code.common.enmus;

/**
 * <AUTHOR>
 */
public enum RegionTypeEnum {

    COUNTRY("国家", 0), 
    PROVINCE("省份", 1), 
    CITY("城市", 2), 
    DISTRICT("区县", 3);

    private String name;
    private int value;

    RegionTypeEnum(String name) {
        this.name = name;
        this.value = this.ordinal();
    }
    
    RegionTypeEnum(String name, int value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    
    public int getValue() {
        return value;
    }
    
    public static RegionTypeEnum valueOf(int value) {
        for (RegionTypeEnum type : values()) {
            if (type.getValue() == value) {
                return type;
            }
        }
        throw new IllegalArgumentException("No enum constant " + RegionTypeEnum.class.getSimpleName() + "." + value);
    }
}
