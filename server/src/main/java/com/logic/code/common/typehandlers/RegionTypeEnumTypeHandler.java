package com.logic.code.common.typehandlers;

import com.logic.code.common.enmus.RegionTypeEnum;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * TypeHandler for RegionTypeEnum to convert between database values and enum values
 */
public class RegionTypeEnumTypeHandler extends BaseTypeHandler<RegionTypeEnum> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, RegionTypeEnum parameter, JdbcType jdbcType) throws SQLException {
        ps.setInt(i, parameter.getValue());
    }

    @Override
    public RegionTypeEnum getNullableResult(ResultSet rs, String columnName) throws SQLException {
        int value = rs.getInt(columnName);
        return rs.wasNull() ? null : RegionTypeEnum.valueOf(value);
    }

    @Override
    public RegionTypeEnum getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        int value = rs.getInt(columnIndex);
        return rs.wasNull() ? null : RegionTypeEnum.valueOf(value);
    }

    @Override
    public RegionTypeEnum getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        int value = cs.getInt(columnIndex);
        return cs.wasNull() ? null : RegionTypeEnum.valueOf(value);
    }
} 