package com.logic.code.common.constants;

public class WechatConstants {

  /**
   * 服务端的私钥，在任何场景都不应该流露出去。一旦客户端得知这个secret, 那就意味着客户端是可以自我签发jwt了。
   */
  public static final String JWT_SECRET = "asdqweasasdqweasasdqweasasdqweasasdqweasasdqweasasdqweasasdqweas";

  /**
   * 有效期7天
   */
  //public static final long JWT_TTL = 2L * 60L * 1000L;
  public static final long JWT_TTL = 7L * 24L * 60L * 60L * 1000L;

  /**
   * Token刷新阈值时间（2天），当token剩余有效期小于此时间时，可以刷新
   */
  public static final long JWT_REFRESH_THRESHOLD = 2L * 24L * 60L * 60L * 1000L;

  /**
   * Token的名字
   */
  public static final String JWT_KEY_NAME = "X-Weshop-Token";

  /**
   * 新Token响应头名字
   */
  public static final String JWT_NEW_TOKEN_HEADER = "X-New-Token";

  public static final String XML_PAY_ORDER_FAIL = "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[支付失败]]></return_msg></xml>";

  public static final String XML_PAY_ORDER_NOT_FOUND = "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[订单不存在]]></return_msg></xml>";

  public static final String XML_PAY_ORDER_OK = "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";

}
