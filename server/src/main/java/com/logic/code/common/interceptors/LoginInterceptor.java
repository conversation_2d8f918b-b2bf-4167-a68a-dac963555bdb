package com.logic.code.common.interceptors;

import com.logic.code.common.constants.WechatConstants;
import com.logic.code.common.enmus.WeshopWechatResultStatus;
import com.logic.code.common.exception.WeshopWechatException;
import com.logic.code.common.utils.JwtHelper;
import com.logic.code.common.utils.StringUtils;
import com.logic.code.common.utils.TokenUtils;
import io.jsonwebtoken.Claims;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * <AUTHOR>
 * @date 2025/5/12 18:04
 * @desc
 */
@Slf4j
public class LoginInterceptor implements HandlerInterceptor {
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        try {
            // 使用TokenUtils进行token验证和自动刷新
            Claims claims = TokenUtils.validateAndRefreshToken(request, response);
            JwtHelper.setCurrentClaims(claims);
            return true;
        } catch (WeshopWechatException e) {
            // 记录详细的错误信息
            String requestUri = request.getRequestURI();
            String userAgent = request.getHeader("User-Agent");
            log.warn("登录验证失败 - URI: {}, 错误: {}, UserAgent: {}",
                    requestUri, e.getStatus().getMsg(), userAgent);
            throw e;
        }
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        JwtHelper.setCurrentClaims(null);
    }
}
