package com.logic.code.common.exception;

import com.logic.code.common.enmus.WeshopWechatResultStatus;
import com.logic.code.common.response.Result;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

/**
 * <AUTHOR>
 */
@ControllerAdvice
@Slf4j
public class WechatExceptionHandler {


    /**
     * 微信服务调用失败的异常拦截
     *
     * @param request
     * @param e
     * @return
     */
    @ExceptionHandler(WxErrorException.class)
    public Result handleWxErrorException(HttpServletRequest request, WxErrorException e) {
        log.warn("execute method exception error.url is {}", request.getRequestURI(), e);
        return Result.failure(WeshopWechatResultStatus.WECHAT_SERVICE_ERROR.getCode(), e.getMessage());
    }

    /**
     * 微信相关异常处理（包括Token过期等）
     *
     * @param request
     * @param e
     * @return
     */
    @ExceptionHandler(WeshopWechatException.class)
    public Result handleWeshopWechatException(HttpServletRequest request, WeshopWechatException e) {
        // 对于token相关异常，记录警告日志
        if (WeshopWechatResultStatus.TOKEN_EXPIRED.equals(e.getStatus()) ||
            WeshopWechatResultStatus.TOKEN_INVALID.equals(e.getStatus()) ||
            WeshopWechatResultStatus.TOKEN_REFRESH_FAILED.equals(e.getStatus())) {
            log.warn("Token相关异常 - URL: {}, 错误: {}", request.getRequestURI(), e.getStatus().getMsg());
        } else {
            log.warn("微信业务异常 - URL: {}, 错误: {}", request.getRequestURI(), e.getStatus().getMsg());
        }

        return Result.failure(e.getStatus());
    }

}
