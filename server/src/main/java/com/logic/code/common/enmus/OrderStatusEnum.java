package com.logic.code.common.enmus;

import com.fasterxml.jackson.annotation.JsonValue;

/**
 * Enum for Order Status
 */
public enum OrderStatusEnum {

    WAIT_PAY("待付款", 0),
    WAIT_SEND("待发货", 1),
    WAIT_RECEIVE("待收货", 2),
    COMPLETED("已完成", 3),
    CANCELLED("已取消", 4);


    private String name;
    private int value;

    OrderStatusEnum(String name, int value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @JsonValue
    public int getValue() {
        return value;
    }

    public static OrderStatusEnum valueOf(int value) {
        for (OrderStatusEnum status : values()) {
            if (status.getValue() == value) {
                return status;
            }
        }
        throw new IllegalArgumentException("No enum constant " + OrderStatusEnum.class.getSimpleName() + "." + value);
    }
}
