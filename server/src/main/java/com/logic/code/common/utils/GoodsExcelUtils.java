package com.logic.code.common.utils;

import com.logic.code.model.dto.GoodsImportDTO;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 商品Excel导入导出工具类
 * <AUTHOR>
 * @date 2025/7/16
 */
public class GoodsExcelUtils {
    
    /**
     * Excel模板列定义
     */
    public static final String[] TEMPLATE_HEADERS = {
        "商品名称*", "商品分类ID*", "商品分类名称", "商品简介", "商品单位",
        "关键词", "商品描述", "是否上架(1-上架,0-下架)", "是否热门(1-是,0-否)", "是否新品(1-是,0-否)",
        "排序值", "虚拟销量", "商品主图URL", "商品轮播图URL(多个用逗号分隔)", "单价",
        "详情标签(多个用逗号分隔)", "展示系统ID(多个用逗号分隔)", "规格类型(0-单规格,1-多规格)", "商品价格*", "成本价",
        "划线价", "库存数量*", "商品编码", "商品重量(KG)", "商品体积(m³)",
        "规格图片URL", "一级分销佣金", "二级分销佣金", "限购数量", "是否显示限购(1-是,0-否)",
        "规格定义(JSON格式-多规格)", "SKU列表(JSON格式-多规格)", "商品属性(JSON格式)"
    };
    
    /**
     * 单规格商品示例数据
     */
    public static final String[] SINGLE_SPEC_EXAMPLE = {
        "苹果手机", "1001", "电子产品", "高品质智能手机", "台",
        "手机,智能,苹果", "这是一款高品质的智能手机", "1", "1", "0",
        "100", "999", "http://example.com/image.jpg", "http://example.com/img1.jpg,http://example.com/img2.jpg", "5999.00",
        "热销,推荐", "1,2", "0", "5999.00", "4500.00",
        "6999.00", "100", "APPLE001", "0.2", "0.001",
        "http://example.com/spec.jpg", "100.00", "50.00", "2", "1",
        "", "", "{\"颜色\":\"黑色\",\"内存\":\"128GB\"}"
    };

    /**
     * 多规格商品示例数据
     */
    public static final String[] MULTI_SPEC_EXAMPLE = {
        "时尚T恤", "1002", "服装", "舒适时尚T恤", "件",
        "T恤,服装,时尚", "这是一款舒适的时尚T恤", "1", "0", "1",
        "200", "500", "http://example.com/tshirt.jpg", "http://example.com/t1.jpg,http://example.com/t2.jpg", "0",
        "新品,时尚", "1", "1", "0", "0",
        "0", "0", "", "0.3", "0.002",
        "", "0", "0", "0", "0",
        "[{\"value\":\"颜色\",\"detail\":[{\"value\":\"红色\",\"pic\":\"\"},{\"value\":\"蓝色\",\"pic\":\"\"}]},{\"value\":\"尺寸\",\"detail\":[{\"value\":\"S\",\"pic\":\"\"},{\"value\":\"M\",\"pic\":\"\"}]}]",
        "[{\"detail\":{\"颜色\":\"红色\",\"尺寸\":\"S\"},\"price\":99,\"cost\":60,\"ot_price\":120,\"stock\":50,\"bar_code\":\"TS001\"},{\"detail\":{\"颜色\":\"红色\",\"尺寸\":\"M\"},\"price\":99,\"cost\":60,\"ot_price\":120,\"stock\":30,\"bar_code\":\"TS002\"},{\"detail\":{\"颜色\":\"蓝色\",\"尺寸\":\"S\"},\"price\":109,\"cost\":65,\"ot_price\":130,\"stock\":40,\"bar_code\":\"TS003\"},{\"detail\":{\"颜色\":\"蓝色\",\"尺寸\":\"M\"},\"price\":109,\"cost\":65,\"ot_price\":130,\"stock\":25,\"bar_code\":\"TS004\"}]",
        "{\"材质\":\"纯棉\",\"产地\":\"中国\"}"
    };
    
    /**
     * 生成Excel模板文件
     */
    public static void generateTemplate(String filePath) throws IOException {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("商品导入模板");
            
            // 创建标题样式
            CellStyle headerStyle = ExcelUtils.createHeaderStyle(workbook);
            CellStyle dataStyle = ExcelUtils.createDataStyle(workbook);
            
            // 创建表头
            Row headerRow = sheet.createRow(0);
            for (int i = 0; i < TEMPLATE_HEADERS.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(TEMPLATE_HEADERS[i]);
                cell.setCellStyle(headerStyle);
            }
            
            // 创建单规格商品示例数据行
            Row singleSpecRow = sheet.createRow(1);
            for (int i = 0; i < SINGLE_SPEC_EXAMPLE.length; i++) {
                Cell cell = singleSpecRow.createCell(i);
                cell.setCellValue(SINGLE_SPEC_EXAMPLE[i]);
                cell.setCellStyle(dataStyle);
            }

            // 创建多规格商品示例数据行
            Row multiSpecRow = sheet.createRow(2);
            for (int i = 0; i < MULTI_SPEC_EXAMPLE.length; i++) {
                Cell cell = multiSpecRow.createCell(i);
                cell.setCellValue(MULTI_SPEC_EXAMPLE[i]);
                cell.setCellStyle(dataStyle);
            }
            
            // 自动调整列宽
            ExcelUtils.autoSizeColumns(sheet, TEMPLATE_HEADERS.length);
            
            // 保存文件
            try (FileOutputStream fileOut = new FileOutputStream(filePath)) {
                workbook.write(fileOut);
            }
        }
    }
    
    /**
     * 从Excel文件读取商品数据
     */
    public static List<GoodsImportDTO> readGoodsFromExcel(String filePath) throws IOException {
        List<GoodsImportDTO> goodsList = new ArrayList<>();
        
        try (Workbook workbook = ExcelUtils.readWorkbook(filePath)) {
            Sheet sheet = workbook.getSheetAt(0);

            // 跳过表头行，从第二行开始读取数据
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null || isEmptyRow(row)) {
                    continue;
                }

                GoodsImportDTO goods = new GoodsImportDTO();
                goods.setRowNumber(i + 1); // Excel行号从1开始

                try {
                    // 读取基本信息
                    goods.setName(getCellValueAsString(row, 0));
                    goods.setCategoryId(getCellValueAsInteger(row, 1));
                    goods.setCategoryName(getCellValueAsString(row, 2));
                    goods.setBrief(getCellValueAsString(row, 3));
                    goods.setUnit(getCellValueAsString(row, 4));
                    goods.setKeywords(getCellValueAsString(row, 5));
                    goods.setDescription(getCellValueAsString(row, 6));
                    goods.setIsOnSale(getCellValueAsInteger(row, 7));
                    goods.setIsHot(getCellValueAsInteger(row, 8));
                    goods.setIsNew(getCellValueAsInteger(row, 9));
                    goods.setSortOrder(getCellValueAsInteger(row, 10));
                    goods.setVirtualSales(getCellValueAsInteger(row, 11));
                    goods.setImageUrl(getCellValueAsString(row, 12));
                    goods.setSliderImages(getCellValueAsString(row, 13));
                    goods.setUnitPrice(getCellValueAsBigDecimal(row, 14));
                    goods.setDetailTag(getCellValueAsString(row, 15));
                    goods.setDisplaySystems(getCellValueAsString(row, 16));

                    // 读取规格信息
                    goods.setSpecType(getCellValueAsInteger(row, 17));
                    goods.setPrice(getCellValueAsBigDecimal(row, 18));
                    goods.setCostPrice(getCellValueAsBigDecimal(row, 19));
                    goods.setOtPrice(getCellValueAsBigDecimal(row, 20));
                    goods.setStock(getCellValueAsInteger(row, 21));
                    goods.setBarCode(getCellValueAsString(row, 22));
                    goods.setWeight(getCellValueAsBigDecimal(row, 23));
                    goods.setVolume(getCellValueAsBigDecimal(row, 24));
                    goods.setSpecImageUrl(getCellValueAsString(row, 25));
                    goods.setBrokerage(getCellValueAsBigDecimal(row, 26));
                    goods.setBrokerageTwo(getCellValueAsBigDecimal(row, 27));
                    goods.setQuota(getCellValueAsInteger(row, 28));
                    goods.setQuotaShow(getCellValueAsInteger(row, 29));

                    // 读取多规格相关字段
                    goods.setSpecItems(getCellValueAsString(row, 30));
                    goods.setSkuAttrs(getCellValueAsString(row, 31));
                    goods.setAttributes(getCellValueAsString(row, 32));

                    goods.setIsValid(true);
                    goods.setProcessStatus(0); // 待处理

                } catch (Exception e) {
                    goods.setIsValid(false);
                    goods.setErrorMessage("第" + goods.getRowNumber() + "行数据解析失败: " + e.getMessage());
                    goods.setProcessStatus(2); // 失败
                }

                goodsList.add(goods);
            }
        }
        
        return goodsList;
    }
    
    /**
     * 检查是否为空行
     */
    private static boolean isEmptyRow(Row row) {
        if (row == null) return true;
        
        for (int i = 0; i < 3; i++) { // 检查前3个关键列
            Cell cell = row.getCell(i);
            if (cell != null && cell.getCellType() != CellType.BLANK) {
                String value = ExcelUtils.getCellValueAsString(cell);
                if (value != null && !value.trim().isEmpty()) {
                    return false;
                }
            }
        }
        return true;
    }
    
    /**
     * 获取单元格字符串值
     */
    private static String getCellValueAsString(Row row, int columnIndex) {
        Cell cell = row.getCell(columnIndex);
        return cell != null ? ExcelUtils.getCellValueAsString(cell) : "";
    }
    
    /**
     * 获取单元格整数值
     */
    private static Integer getCellValueAsInteger(Row row, int columnIndex) {
        Cell cell = row.getCell(columnIndex);
        return cell != null ? ExcelUtils.getCellValueAsInteger(cell) : null;
    }
    
    /**
     * 获取单元格BigDecimal值
     */
    private static BigDecimal getCellValueAsBigDecimal(Row row, int columnIndex) {
        Cell cell = row.getCell(columnIndex);
        if (cell == null) return null;
        
        String value = ExcelUtils.getCellValueAsString(cell);
        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        
        try {
            return new BigDecimal(value.trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }
}
