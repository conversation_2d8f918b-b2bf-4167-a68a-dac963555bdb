package com.logic.code.common.utils;

import com.logic.code.common.constants.WechatConstants;
import com.logic.code.common.enmus.WeshopWechatResultStatus;
import com.logic.code.common.exception.WeshopWechatException;
import io.jsonwebtoken.Claims;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * Token工具类
 * 提供token相关的辅助功能
 * 
 * <AUTHOR>
 * @date 2025/7/29
 */
@Slf4j
public class TokenUtils {

    /**
     * 从请求中获取token
     * 
     * @param request HTTP请求
     * @return token字符串，如果不存在则返回null
     */
    public static String getTokenFromRequest(HttpServletRequest request) {
        return request.getHeader(WechatConstants.JWT_KEY_NAME);
    }

    /**
     * 在响应中设置新的token
     * 
     * @param response HTTP响应
     * @param newToken 新的token
     */
    public static void setNewTokenInResponse(HttpServletResponse response, String newToken) {
        response.setHeader(WechatConstants.JWT_NEW_TOKEN_HEADER, newToken);
    }

    /**
     * 检查token是否即将过期（1小时内）
     * 
     * @param claims JWT claims
     * @return 是否即将过期
     */
    public static boolean isTokenExpiringSoon(Claims claims) {
        if (claims == null || claims.getExpiration() == null) {
            return false;
        }
        
        long currentTime = System.currentTimeMillis();
        long expirationTime = claims.getExpiration().getTime();
        long remainingTime = expirationTime - currentTime;
        
        // 1小时 = 60 * 60 * 1000 毫秒
        return remainingTime > 0 && remainingTime < TimeUnit.HOURS.toMillis(1);
    }

    /**
     * 获取token剩余有效时间（毫秒）
     * 
     * @param claims JWT claims
     * @return 剩余时间，如果已过期返回负数
     */
    public static long getRemainingTime(Claims claims) {
        if (claims == null || claims.getExpiration() == null) {
            return 0;
        }
        
        long currentTime = System.currentTimeMillis();
        long expirationTime = claims.getExpiration().getTime();
        
        return expirationTime - currentTime;
    }

    /**
     * 获取token剩余有效时间（友好格式）
     * 
     * @param claims JWT claims
     * @return 剩余时间描述
     */
    public static String getRemainingTimeDescription(Claims claims) {
        long remainingTime = getRemainingTime(claims);
        
        if (remainingTime <= 0) {
            return "已过期";
        }
        
        long days = TimeUnit.MILLISECONDS.toDays(remainingTime);
        long hours = TimeUnit.MILLISECONDS.toHours(remainingTime) % 24;
        long minutes = TimeUnit.MILLISECONDS.toMinutes(remainingTime) % 60;
        
        if (days > 0) {
            return String.format("%d天%d小时", days, hours);
        } else if (hours > 0) {
            return String.format("%d小时%d分钟", hours, minutes);
        } else {
            return String.format("%d分钟", minutes);
        }
    }

    /**
     * 验证token是否有效（不抛出异常）
     * 
     * @param token token字符串
     * @return 是否有效
     */
    public static boolean isTokenValid(String token) {
        if (StringUtils.isBlank(token)) {
            return false;
        }
        
        try {
            Claims claims = JwtHelper.parseJWT(token);
            return claims != null && getRemainingTime(claims) > 0;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 安全地解析token（不抛出异常）
     * 
     * @param token token字符串
     * @return Claims对象，解析失败返回null
     */
    public static Claims safeParseToken(String token) {
        if (StringUtils.isBlank(token)) {
            return null;
        }
        
        try {
            return JwtHelper.parseJWT(token);
        } catch (Exception e) {
            log.debug("Token解析失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从token中安全地获取用户ID
     * 
     * @param token token字符串
     * @return 用户ID，获取失败返回null
     */
    public static String getUserIdFromToken(String token) {
        Claims claims = safeParseToken(token);
        if (claims != null) {
            return claims.get("uid", String.class);
        }
        return null;
    }

    /**
     * 检查token是否可以刷新
     * 包括检查token格式、过期时间等
     * 
     * @param token token字符串
     * @return 是否可以刷新
     */
    public static boolean canRefreshToken(String token) {
        if (StringUtils.isBlank(token)) {
            return false;
        }
        
        // 首先尝试正常解析
        Claims claims = safeParseToken(token);
        if (claims != null) {
            return JwtHelper.shouldRefreshToken(claims);
        }
        
        // 如果正常解析失败，尝试解析过期token
        Claims expiredClaims = JwtHelper.parseExpiredJWT(token);
        return expiredClaims != null;
    }

    /**
     * 创建token状态信息
     * 
     * @param claims JWT claims
     * @return token状态描述
     */
    public static String getTokenStatusDescription(Claims claims) {
        if (claims == null) {
            return "无效token";
        }
        
        long remainingTime = getRemainingTime(claims);
        
        if (remainingTime <= 0) {
            return "token已过期";
        } else if (JwtHelper.shouldRefreshToken(claims)) {
            return "token即将过期，建议刷新";
        } else if (isTokenExpiringSoon(claims)) {
            return "token即将过期";
        } else {
            return "token有效";
        }
    }

    /**
     * 记录token操作日志
     * 
     * @param operation 操作类型
     * @param userId 用户ID
     * @param success 是否成功
     * @param message 附加信息
     */
    public static void logTokenOperation(String operation, String userId, boolean success, String message) {
        if (success) {
            log.info("Token操作成功 - 操作: {}, 用户ID: {}, 信息: {}", operation, userId, message);
        } else {
            log.warn("Token操作失败 - 操作: {}, 用户ID: {}, 信息: {}", operation, userId, message);
        }
    }

    /**
     * 验证token并自动刷新（如果需要）
     * 
     * @param request HTTP请求
     * @param response HTTP响应
     * @return 有效的Claims
     * @throws WeshopWechatException 如果token无效或刷新失败
     */
    public static Claims validateAndRefreshToken(HttpServletRequest request, HttpServletResponse response) {
        String token = getTokenFromRequest(request);
        if (StringUtils.isBlank(token)) {
            throw new WeshopWechatException(WeshopWechatResultStatus.WECHAT_LOGIN_ERROR);
        }
        
        try {
            Claims claims = JwtHelper.parseJWT(token);
            
            // 检查是否需要刷新
            if (JwtHelper.shouldRefreshToken(claims)) {
                try {
                    String newToken = JwtHelper.refreshToken(claims);
                    setNewTokenInResponse(response, newToken);
                    logTokenOperation("自动刷新", claims.get("uid", String.class), true, "token即将过期");
                } catch (Exception e) {
                    logTokenOperation("自动刷新", claims.get("uid", String.class), false, e.getMessage());
                }
            }
            
            return claims;
        } catch (WeshopWechatException e) {
            if (WeshopWechatResultStatus.TOKEN_EXPIRED.equals(e.getStatus())) {
                // 尝试刷新过期token
                Claims expiredClaims = JwtHelper.parseExpiredJWT(token);
                if (expiredClaims != null) {
                    try {
                        String newToken = JwtHelper.refreshToken(expiredClaims);
                        setNewTokenInResponse(response, newToken);
                        logTokenOperation("过期刷新", expiredClaims.get("uid", String.class), true, "token已过期但成功刷新");
                        return expiredClaims;
                    } catch (Exception refreshException) {
                        logTokenOperation("过期刷新", expiredClaims.get("uid", String.class), false, refreshException.getMessage());
                    }
                }
            }
            throw e;
        }
    }
}
