package com.logic.code.common;

import com.logic.code.entity.Region;
import com.logic.code.service.RegionService;
import jakarta.annotation.Resource;
import logic.orm.utils.ListUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/22 11:02
 * @desc
 */
@Component
public class Cache {

    @Resource
    RegionService regionService;

    public static final Map<Short, String> region = new HashMap<>();


    public void init() {
        List<Region> regions = regionService.queryAll();
        if (ListUtils.isNotBlank(regions)) {
            regions.forEach(region -> {
                Cache.region.put(region.getId(),region.getName());
            });
        }
    }

}
