package com.logic.code.common.utils;

import com.logic.code.common.constants.WechatConstants;
import com.logic.code.common.enmus.WeshopWechatResultStatus;
import com.logic.code.common.exception.WeshopException;
import com.logic.code.common.exception.WeshopWechatException;
import com.logic.code.entity.User;
import io.jsonwebtoken.*;
import io.jsonwebtoken.security.SignatureException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static java.util.Optional.ofNullable;

/**
 * <AUTHOR>
 */
@Slf4j
public class JwtHelper {


    /**
     * 登陆信息
     */
    private static final ThreadLocal<Claims> currentClaims = new ThreadLocal<>();

    /**
     * 获得传输的信息
     *
     * @return
     */
    public static Claims getCurrentClaims() {
        return currentClaims.get();
    }

    public static void setCurrentClaims(Claims claims) {
        currentClaims.set(claims);
    }

    public static User getUserInfo() {
        String subject = ofNullable(currentClaims.get()).map(Claims::getSubject).orElse(null);
        if (subject == null) {
            throw new WeshopException(WeshopWechatResultStatus.WECHAT_LOGIN_ERROR);
        }
        return JsonUtils.toObject(subject, User.class);
    }


    public static User getUser() {
        String subject = ofNullable(currentClaims.get()).map(Claims::getSubject).orElse(null);
        if (StringUtils.isBlank(subject)) return null;
        return JsonUtils.toObject(subject, User.class);
    }

    /**
     * 由字符串生成加密key
     *
     * @return
     */
    public static SecretKey generalKey() {
        //本地配置文件中加密的密文7786df7fc3a34e26a61c034d5ec8245d
        String stringKey = WechatConstants.JWT_SECRET;
        //本地的密码解码[B@152f6e2
        byte[] encodedKey = Base64.decodeBase64(stringKey);
        // 根据给定的字节数组使用AES加密算法构造一个密钥，使用 encodedKey中的始于且包含 0 到前 leng 个字节这是当然是所有。
        SecretKey key = new SecretKeySpec(encodedKey, 0, encodedKey.length, "HmacSHA256");
        return key;
    }

    /**
     * 创建jwt
     *
     * @param id
     * @param subject
     * @param ttlMillis 过期的时间长度
     * @return
     * @throws Exception
     */
    public static String createJWT(String id, String subject, long ttlMillis) {
        //指定签名的时候使用的签名算法，也就是header那部分，jjwt已经将这部分内容封装好了。
        SignatureAlgorithm signatureAlgorithm = SignatureAlgorithm.HS256;
        //生成JWT的时间
        long nowMillis = System.currentTimeMillis();
        Date now = new Date(nowMillis);
        //创建payload的私有声明（根据特定的业务需要添加，如果要拿这个做验证，一般是需要和jwt的接收方提前沟通好验证方式的）
        Map<String, Object> claims = new HashMap<>(16);
        claims.put("uid", id);
        //生成签名的时候使用的秘钥secret,这个方法本地封装了的，一般可以从本地配置文件中读取，切记这个秘钥不能外露哦。它就是你服务端的私钥，在任何场景都不应该流露出去。一旦客户端得知这个secret, 那就意味着客户端是可以自我签发jwt了。
        SecretKey key = generalKey();
        //下面就是在为payload添加各种标准声明和私有声明了
        //这里其实就是new一个JwtBuilder，设置jwt的body
        JwtBuilder builder = Jwts.builder()
                //如果有私有声明，一定要先设置这个自己创建的私有的声明，这个是给builder的claim赋值，一旦写在标准的声明赋值之后，就是覆盖了那些标准的声明的
                .setClaims(claims)
                //设置jti(JWT ID)：是JWT的唯一标识，根据业务需要，这个可以设置为一个不重复的值，主要用来作为一次性token,从而回避重放攻击。
                .setId(id)
                //iat: jwt的签发时间
                .setIssuedAt(now)
                //sub(Subject)：代表这个JWT的主体，即它的所有人，这个是一个json格式的字符串，可以存放什么userid，roldid之类的，作为什么用户的唯一标志。
                .setSubject(subject)
                //设置签名使用的签名算法和签名使用的秘钥
                .signWith(signatureAlgorithm, key);
        if (ttlMillis >= 0) {
            long expMillis = nowMillis + ttlMillis;
            Date exp = new Date(expMillis);
            //设置过期时间
            builder.setExpiration(exp);
        }
        log.info(builder.compact());
        //就开始压缩为xxxxxxxxxxxxxx.xxxxxxxxxxxxxxx.xxxxxxxxxxxxx这样的jwt
        return builder.compact();
        //打印了一哈哈确实是下面的这个样子
        //eyJhbGciOiJIUzI1NiJ9.eyJ1aWQiOiJEU1NGQVdEV0FEQVMuLi4iLCJzdWIiOiIiLCJ1c2VyX25hbWUiOiJhZG1pbiIsIm5pY2tfbmFtZSI6IkRBU0RBMTIxIiwiZXhwIjoxNTE3ODI4MDE4LCJpYXQiOjE1MTc4Mjc5NTgsImp0aSI6Imp3dCJ9.xjIvBbdPbEMBMurmwW6IzBkS3MPwicbqQa2Y5hjHSyo
    }

    /**
     * 解密jwt
     *
     * @param jwt
     * @return
     * @throws Exception
     */
    public static Claims parseJWT(String jwt) {
        //签名秘钥，和生成的签名的秘钥一模一样
        SecretKey key = generalKey();
        //得到DefaultJwtParser
        Claims claims = null;
        try {
            claims = Jwts.parser()
                    //设置签名的秘钥
                    .setSigningKey(key)
                    //设置需要解析的jwt
                    .parseClaimsJws(jwt).getBody();
        } catch (ExpiredJwtException e) {
            log.warn("JWT token已过期: {}", e.getMessage());
            throw new WeshopWechatException(WeshopWechatResultStatus.TOKEN_EXPIRED);
        } catch (SignatureException e) {
            log.warn("JWT签名验证失败: {}", e.getMessage());
            throw new WeshopWechatException(WeshopWechatResultStatus.TOKEN_INVALID);
        } catch (MalformedJwtException e) {
            log.warn("JWT格式错误: {}", e.getMessage());
            throw new WeshopWechatException(WeshopWechatResultStatus.TOKEN_INVALID);
        } catch (UnsupportedJwtException e) {
            log.warn("不支持的JWT: {}", e.getMessage());
            throw new WeshopWechatException(WeshopWechatResultStatus.TOKEN_INVALID);
        } catch (IllegalArgumentException e) {
            log.warn("JWT参数错误: {}", e.getMessage());
            throw new WeshopWechatException(WeshopWechatResultStatus.TOKEN_INVALID);
        } catch (Exception e) {
            log.error("JWT解析异常: {}", e.getMessage(), e);
            throw new WeshopWechatException(WeshopWechatResultStatus.WECHAT_LOGIN_ERROR);
        }
        return claims;
    }


    /**
     * 检查token是否需要刷新
     * 当token剩余有效期小于刷新阈值时，返回true
     *
     * @param claims JWT claims
     * @return 是否需要刷新
     */
    public static boolean shouldRefreshToken(Claims claims) {
        if (claims == null || claims.getExpiration() == null) {
            return false;
        }

        long currentTime = System.currentTimeMillis();
        long expirationTime = claims.getExpiration().getTime();
        long remainingTime = expirationTime - currentTime;

        return remainingTime > 0 && remainingTime < WechatConstants.JWT_REFRESH_THRESHOLD;
    }

    /**
     * 刷新token
     * 基于现有的claims生成新的token
     *
     * @param oldClaims 旧的claims
     * @return 新的token
     */
    public static String refreshToken(Claims oldClaims) {
        if (oldClaims == null) {
            throw new WeshopWechatException(WeshopWechatResultStatus.TOKEN_REFRESH_FAILED);
        }

        try {
            String id = oldClaims.getId();
            String subject = oldClaims.getSubject();

            if (StringUtils.isBlank(id) || StringUtils.isBlank(subject)) {
                throw new WeshopWechatException(WeshopWechatResultStatus.TOKEN_REFRESH_FAILED);
            }

            return createJWT(id, subject, WechatConstants.JWT_TTL);
        } catch (Exception e) {
            log.error("刷新token失败: {}", e.getMessage(), e);
            throw new WeshopWechatException(WeshopWechatResultStatus.TOKEN_REFRESH_FAILED);
        }
    }

    /**
     * 尝试解析过期的token（用于刷新场景）
     * 即使token过期也会返回claims，但不会抛出过期异常
     *
     * @param jwt token字符串
     * @return claims，如果token格式错误则返回null
     */
    public static Claims parseExpiredJWT(String jwt) {
        SecretKey key = generalKey();
        try {
            return Jwts.parser()
                    .setSigningKey(key)
                    .parseClaimsJws(jwt)
                    .getBody();
        } catch (ExpiredJwtException e) {
            // 对于过期的token，我们仍然返回claims用于刷新
            return e.getClaims();
        } catch (Exception e) {
            log.warn("解析过期token失败: {}", e.getMessage());
            return null;
        }
    }

    public static String createJWTTest() {
        String ab = JwtHelper.createJWT("999999999", "{ \"id\":999999999,\"userId\": 999999999,\"username\": \"开发人员测试账号\",\"source\":\"dr_admin\"}", -1);
        return ab;
    }


    public static void main(String[] args) throws Exception {
        String ab = JwtHelper.createJWT("999999999", "{ \"id\":999999999,\"userId\": 999999999,\"username\": \"开发人员测试账号\",\"source\":\"dr_admin\"}", -1);
        System.out.println(ab);
        //注意：如果jwExpiredJwtException。
        Claims c = JwtHelper.parseJWT(ab);
        //jwt
        System.out.println(c.getId());
        //Mon Feb 05 20:50:49 CST 2018
        System.out.println(c.getIssuedAt());
        //{ "id":999999999,"userId": 999999999,"username": "开发人员测试账号","source":"dr_admin"}
        System.out.println(c.getSubject());
        //null
        System.out.println(c.getIssuer());
        //DSSFAWDWADAS...
        System.out.println(c.get("uid", String.class));

    }

}
