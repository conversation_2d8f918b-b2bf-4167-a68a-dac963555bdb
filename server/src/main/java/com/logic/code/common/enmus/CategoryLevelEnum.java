package com.logic.code.common.enmus;

/**
 * <AUTHOR>
 */
public enum CategoryLevelEnum {

    L1("一级类目"), L2("二级类目");

    CategoryLevelEnum(String name) {
        this.name = name;
    }

    private String name;

    public String getName() {
        return name;
    }
    
    /**
     * Convert from integer value to enum
     * 
     * @param value The integer value (0-based index)
     * @return The corresponding enum value, or L1 if value is invalid
     */
    public static CategoryLevelEnum fromValue(int value) {
        try {
            return CategoryLevelEnum.values()[value];
        } catch (ArrayIndexOutOfBoundsException e) {
            // Default to L1 if value is out of bounds
            return L1;
        }
    }
}
