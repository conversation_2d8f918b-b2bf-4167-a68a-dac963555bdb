package com.logic.code.common;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.Data;

/**
 * 分页数据封装类
 */
@Data
public class CommonPage {
    private Integer pageNum;
    private Integer pageSize;
    private Integer totalPage;
    private Long total;

    /**
     * 将MyBatis Plus分页结果转化为通用结果
     */
    public static CommonPage restPage(IPage pageResult) {
        CommonPage result = new CommonPage();
        result.setPageNum((int) pageResult.getCurrent());
        result.setPageSize((int) pageResult.getSize());
        result.setTotal(pageResult.getTotal());
        result.setTotalPage((int) pageResult.getPages());
        return result;
    }
}
