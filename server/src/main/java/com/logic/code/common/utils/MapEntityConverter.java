package com.logic.code.common.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.util.*;

/**
 * Utility class for converting between Maps and entity objects.
 * Handles mapping between database column names and Java field names
 * with support for camelCase, snake_case, and case insensitivity.
 */
public class MapEntityConverter {
    private static final Logger log = LoggerFactory.getLogger(MapEntityConverter.class);

    /**
     * Converts a list of maps to a list of entities of the specified type
     *
     * @param mapList List of maps with column data
     * @param entityClass Class of the entity to create
     * @param <T> Entity type
     * @return List of entity objects
     */
    public static <T> List<T> convertToEntityList(List<Map<String, Object>> mapList, Class<T> entityClass) {
        if (mapList == null || mapList.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<T> entityList = new ArrayList<>(mapList.size());
        for (Map<String, Object> map : mapList) {
            T entity = convertToEntity(map, entityClass);
            if (entity != null) {
                entityList.add(entity);
            }
        }
        
        return entityList;
    }
    
    /**
     * Converts a map to an entity of the specified type
     *
     * @param mapResult Map with column data
     * @param entityClass Class of the entity to create
     * @param <T> Entity type
     * @return Entity object
     */
    public static <T> T convertToEntity(Map<String, Object> mapResult, Class<T> entityClass) {
        if (mapResult == null || mapResult.isEmpty()) {
            return null;
        }
        
        log.debug("Converting map to entity of type {}: {}", entityClass.getName(), mapResult);
        
        try {
            // Create a new instance of the entity class
            T entity = entityClass.getDeclaredConstructor().newInstance();
            
            // Create a case-insensitive map for easier lookups
            Map<String, Object> caseInsensitiveMap = new HashMap<>();
            for (Map.Entry<String, Object> entry : mapResult.entrySet()) {
                if (entry.getKey() != null) {
                    caseInsensitiveMap.put(entry.getKey().toLowerCase(), entry.getValue());
                    log.trace("Map entry: {}={} ({})", entry.getKey().toLowerCase(), 
                            entry.getValue(), entry.getValue() != null ? entry.getValue().getClass().getName() : "null");
                }
            }
            
            // Get all fields of the entity class including inherited fields
            List<Field> fields = getAllFields(entityClass);
            
            // Populate entity fields from the map
            for (Field field : fields) {
                field.setAccessible(true);
                String fieldName = field.getName();
                
                // Try different naming conventions to find the value
                Object value = findValueForField(fieldName, caseInsensitiveMap);
                
                if (value != null) {
                    log.trace("Found value for field {}: {} ({})", fieldName, value, value.getClass().getName());
                    setFieldValue(entity, field, value);
                } else {
                    log.trace("No value found for field: {}", fieldName);
                }
            }
            
            return entity;
        } catch (Exception e) {
            log.error("Error converting Map to entity of type {}: {}", entityClass.getName(), e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * Converts an entity to a Map
     *
     * @param entity Entity object
     * @param <T> Entity type
     * @return Map with entity data
     */
    public static <T> Map<String, Object> convertToMap(T entity) {
        if (entity == null) {
            return Collections.emptyMap();
        }
        
        try {
            Map<String, Object> resultMap = new HashMap<>();
            List<Field> fields = getAllFields(entity.getClass());
            
            for (Field field : fields) {
                field.setAccessible(true);
                String fieldName = field.getName();
                Object value = field.get(entity);
                
                if (value != null) {
                    // Put both camelCase and snake_case versions
                    resultMap.put(fieldName, value);
                    resultMap.put(camelToSnake(fieldName), value);
                }
            }
            
            return resultMap;
        } catch (Exception e) {
            log.error("Error converting entity to Map: {}", e.getMessage(), e);
            return Collections.emptyMap();
        }
    }
    
    /**
     * Finds a value for a field by trying different naming conventions
     */
    private static Object findValueForField(String fieldName, Map<String, Object> caseInsensitiveMap) {
        // Try direct lookup
        Object value = caseInsensitiveMap.get(fieldName.toLowerCase());
        if (value != null) return value;
        
        // Try snake_case
        String snakeCaseName = camelToSnake(fieldName);
        value = caseInsensitiveMap.get(snakeCaseName.toLowerCase());
        if (value != null) return value;
        
        // Try with database-style naming (e.g., user_id vs userId)
        for (String key : caseInsensitiveMap.keySet()) {
            // Replace all underscores and compare
            if (key.replace("_", "").equalsIgnoreCase(fieldName.replace("_", ""))) {
                return caseInsensitiveMap.get(key);
            }
        }
        
        return null;
    }
    
    /**
     * Converts a camelCase string to snake_case
     */
    private static String camelToSnake(String str) {
        String regex = "([a-z])([A-Z])";
        String replacement = "$1_$2";
        return str.replaceAll(regex, replacement).toLowerCase();
    }
    
    /**
     * Gets all fields of a class including inherited fields
     */
    private static List<Field> getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        while (clazz != null) {
            fields.addAll(Arrays.asList(clazz.getDeclaredFields()));
            clazz = clazz.getSuperclass();
        }
        return fields;
    }
    
    /**
     * Sets a field value with appropriate type conversion
     */
    private static void setFieldValue(Object entity, Field field, Object value) {
        try {
            Class<?> fieldType = field.getType();
            
            // Handle primitive types and common objects
            if (fieldType == String.class) {
                field.set(entity, value.toString());
            } else if (fieldType == Integer.class || fieldType == int.class) {
                field.set(entity, value instanceof Integer ? value : Integer.valueOf(value.toString()));
            } else if (fieldType == Long.class || fieldType == long.class) {
                field.set(entity, value instanceof Long ? value : Long.valueOf(value.toString()));
            } else if (fieldType == Double.class || fieldType == double.class) {
                field.set(entity, value instanceof Double ? value : Double.valueOf(value.toString()));
            } else if (fieldType == Float.class || fieldType == float.class) {
                field.set(entity, value instanceof Float ? value : Float.valueOf(value.toString()));
            } else if (fieldType == Boolean.class || fieldType == boolean.class) {
                field.set(entity, value instanceof Boolean ? value : Boolean.valueOf(value.toString()));
            } else if (fieldType == Byte.class || fieldType == byte.class) {
                field.set(entity, value instanceof Byte ? value : Byte.valueOf(value.toString()));
            } else if (fieldType == Date.class) {
                if (value instanceof Date) {
                    field.set(entity, value);
                } else if (value instanceof Long) {
                    field.set(entity, new Date((Long) value));
                } else if (value instanceof java.sql.Date) {
                    field.set(entity, new Date(((java.sql.Date) value).getTime()));
                } else if (value instanceof java.sql.Timestamp) {
                    field.set(entity, new Date(((java.sql.Timestamp) value).getTime()));
                } else if (value instanceof String) {
                    // Try to parse the date string - this is a simplified version
                    try {
                        // For ISO format dates
                        java.time.Instant instant = java.time.Instant.parse((String) value);
                        field.set(entity, Date.from(instant));
                    } catch (Exception e) {
                        // Log and continue - could add other date formats as needed
                        log.warn("Could not parse date string: {}", value);
                    }
                }
            } else if (fieldType.isEnum() && value instanceof String) {
                // Handle enum conversion
                try {
                    @SuppressWarnings({"unchecked", "rawtypes"})
                    Object enumValue = Enum.valueOf((Class<Enum>) fieldType, value.toString());
                    field.set(entity, enumValue);
                } catch (Exception e) {
                    // Log and continue
                    log.warn("Could not convert to enum: {}", value);
                }
            } else if (value.getClass().equals(fieldType)) {
                // If the types already match
                field.set(entity, value);
            }
        } catch (Exception e) {
            log.warn("Failed to set field {}: {}", field.getName(), e.getMessage());
        }
    }
} 