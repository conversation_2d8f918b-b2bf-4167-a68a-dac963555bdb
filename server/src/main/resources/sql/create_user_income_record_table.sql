-- 创建用户收益记录表
CREATE TABLE IF NOT EXISTS `eb_user_income_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `income_type` tinyint(2) NOT NULL DEFAULT '1' COMMENT '收益类型：1-分销佣金，2-推广奖励，3-团队奖励，4-其他',
  `amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '收益金额',
  `order_id` int(11) DEFAULT NULL COMMENT '关联订单ID',
  `order_no` varchar(32) DEFAULT NULL COMMENT '关联订单号',
  `product_id` int(11) DEFAULT NULL COMMENT '商品ID',
  `product_name` varchar(255) DEFAULT NULL COMMENT '商品名称',
  `product_image` varchar(500) DEFAULT NULL COMMENT '商品图片',
  `source_user_id` int(11) DEFAULT NULL COMMENT '来源用户ID',
  `source_user_nickname` varchar(50) DEFAULT NULL COMMENT '来源用户昵称',
  `status` tinyint(2) NOT NULL DEFAULT '0' COMMENT '状态：0-待结算，1-已结算，2-已冻结',
  `settle_time` datetime DEFAULT NULL COMMENT '结算时间',
  `freeze_reason` varchar(255) DEFAULT NULL COMMENT '冻结原因',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_income_type` (`income_type`),
  KEY `idx_status` (`status`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_source_user_id` (`source_user_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_is_del` (`is_del`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户收益记录表';

-- 插入一些测试数据
INSERT INTO `eb_user_income_record` (`user_id`, `income_type`, `amount`, `order_no`, `product_name`, `product_image`, `source_user_id`, `source_user_nickname`, `status`, `remark`) VALUES
(1, 1, 15.50, 'ORD202501010001', '测试商品A', '/uploads/product/test1.jpg', 2, '测试用户B', 1, '分销佣金'),
(1, 1, 25.80, 'ORD202501010002', '测试商品B', '/uploads/product/test2.jpg', 3, '测试用户C', 0, '分销佣金'),
(1, 2, 10.00, NULL, NULL, NULL, 4, '测试用户D', 1, '推广奖励'),
(1, 3, 50.00, 'ORD202501010003', '测试商品C', '/uploads/product/test3.jpg', 5, '测试用户E', 2, '团队奖励'),
(1, 1, 8.90, 'ORD202501010004', '测试商品D', '/uploads/product/test4.jpg', 6, '测试用户F', 1, '分销佣金');
