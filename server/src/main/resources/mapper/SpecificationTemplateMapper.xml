<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logic.code.mapper.SpecificationTemplateMapper">

    <resultMap id="BaseResultMap" type="com.logic.code.entity.goods.SpecificationTemplate">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="rule_name" property="ruleName" jdbcType="VARCHAR"/>
        <result column="attr_name" property="attrName" jdbcType="VARCHAR"/>
        <result column="attr_value" property="attrValue" jdbcType="LONGVARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, rule_name, attr_name, attr_value, create_time, update_time, status
    </sql>

    <!-- 分页查询规格模板 -->
    <select id="selectPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM weshop_specification_template
        WHERE status = 1
        <if test="ruleName != null and ruleName != ''">
            AND rule_name LIKE CONCAT('%', #{ruleName}, '%')
        </if>
        ORDER BY create_time DESC
    </select>

</mapper>
