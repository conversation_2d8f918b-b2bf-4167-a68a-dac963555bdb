# MyBatis XML解析错误修复

## 错误信息
```
org.xml.sax.SAXParseException; lineNumber: 64; columnNumber: 37; 
元素内容必须由格式正确的字符数据或标记组成。
```

## 问题原因

在MyBatis的XML映射文件中，SQL语句包含了XML的特殊字符，导致XML解析器无法正确解析。

### 问题字符
- `<` (小于号)
- `>` (大于号) 
- `<=` (小于等于)
- `>=` (大于等于)
- `&` (和号)

这些字符在XML中有特殊含义，需要进行转义处理。

## 修复方案

### 方案1: XML实体转义（推荐）

将特殊字符替换为XML实体：

```xml
<!-- 修复前 -->
COUNT(CASE WHEN rating >= 4 THEN 1 END) as goodCount,
COUNT(CASE WHEN rating <= 2 THEN 1 END) as badCount,
WHEN COUNT(*) > 0 THEN

<!-- 修复后 -->
COUNT(CASE WHEN rating &gt;= 4 THEN 1 END) as goodCount,
COUNT(CASE WHEN rating &lt;= 2 THEN 1 END) as badCount,
WHEN COUNT(*) &gt; 0 THEN
```

### XML实体对照表
| 字符 | XML实体 | 说明 |
|------|---------|------|
| `<`  | `&lt;`  | 小于号 |
| `>`  | `&gt;`  | 大于号 |
| `&`  | `&amp;` | 和号 |
| `"`  | `&quot;`| 双引号 |
| `'`  | `&apos;`| 单引号 |

### 方案2: CDATA区域（备选）

使用CDATA包裹整个SQL语句：

```xml
<select id="selectCommentStats" resultType="java.util.Map">
    <![CDATA[
    SELECT
        COUNT(CASE WHEN rating >= 4 THEN 1 END) as goodCount,
        COUNT(CASE WHEN rating <= 2 THEN 1 END) as badCount
    FROM weshop_comment wc
    WHERE type_id = #{commentQuery.typeId}
    ]]>
</select>
```

**注意**: CDATA中无法使用MyBatis的动态SQL标签（如`<if>`、`<where>`等）。

## 最终修复代码

```xml
<select id="selectCommentStats" resultType="java.util.Map">
    SELECT
        COUNT(*) as allCount,
        COUNT(CASE WHEN wc.id IN (
            SELECT DISTINCT comment_id FROM weshop_comment_picture WHERE comment_id = wc.id
        ) THEN 1 END) as hasPicCount,
        COUNT(CASE WHEN rating &gt;= 4 THEN 1 END) as goodCount,
        COUNT(CASE WHEN rating = 3 THEN 1 END) as normalCount,
        COUNT(CASE WHEN rating &lt;= 2 THEN 1 END) as badCount,
        ROUND(AVG(CASE WHEN rating IS NOT NULL THEN rating END), 2) as averageRating,
        ROUND(
            CASE
                WHEN COUNT(*) &gt; 0 THEN (COUNT(CASE WHEN rating &gt;= 4 THEN 1 END) * 100.0 / COUNT(*))
                ELSE 100.0
            END,
            2
        ) as satisfactionRate
    FROM
        weshop_comment wc
    WHERE
        type_id = #{commentQuery.typeId}
        AND value_id = #{commentQuery.valueId}
</select>
```

## 验证方法

### 1. XML语法验证
确保XML文件格式正确，可以被正常解析。

### 2. MyBatis映射测试
```java
@Test
public void testSelectCommentStats() {
    CommentQuery query = new CommentQuery();
    query.setTypeId((byte) 0);
    query.setValueId(1);
    
    Map<String, Object> stats = commentMapper.selectCommentStats(query);
    
    assertNotNull(stats);
    assertTrue(stats.containsKey("allCount"));
    assertTrue(stats.containsKey("averageRating"));
}
```

### 3. SQL执行验证
```sql
-- 直接在数据库中执行SQL验证逻辑
SELECT
    COUNT(*) as allCount,
    COUNT(CASE WHEN rating >= 4 THEN 1 END) as goodCount,
    COUNT(CASE WHEN rating <= 2 THEN 1 END) as badCount,
    ROUND(AVG(CASE WHEN rating IS NOT NULL THEN rating END), 2) as averageRating
FROM weshop_comment 
WHERE type_id = 0 AND value_id = 1;
```

## 预防措施

### 1. 开发规范
- 在XML中编写SQL时，始终注意特殊字符的转义
- 使用IDE的XML语法检查功能
- 定期进行XML格式验证

### 2. 测试覆盖
- 为每个新增的SQL映射编写单元测试
- 在CI/CD流程中包含XML语法验证
- 使用MyBatis Generator时注意生成的XML格式

### 3. 工具支持
- 使用支持MyBatis的IDE插件
- 配置XML Schema验证
- 使用代码格式化工具保持一致性

## 常见错误场景

1. **比较运算符**: `>=`, `<=`, `<>`, `!=`
2. **逻辑运算符**: `&&`, `||` (应使用 `AND`, `OR`)
3. **字符串包含**: `LIKE '%value%'` 中的 `%` 符号
4. **注释**: `<!-- -->` 与SQL注释 `--` 的混用

通过正确的XML转义处理，可以避免这类解析错误，确保MyBatis映射文件的正常运行。
