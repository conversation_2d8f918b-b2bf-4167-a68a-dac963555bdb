<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logic.code.mapper.GoodsAttributeMapper">
    <resultMap id="BaseResultMap" type="com.logic.code.entity.goods.GoodsAttribute">
        <!--
      WARNING - @mbg.generated
    -->
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="goods_id" property="goodsId" jdbcType="INTEGER"/>
        <result column="attribute_id" property="attributeId" jdbcType="INTEGER"/>
        <result column="value" property="value" jdbcType="LONGVARCHAR"/>
    </resultMap>
    <select id="selectGoodsDetailAttributeByGoodsId" resultType="com.logic.code.model.dto.GoodsAttributeDTO">
        SELECT
        a.name, ga.value
        FROM
        weshop_goods_attribute AS ga
        INNER JOIN
        weshop_attribute AS a ON a.id = ga.attribute_id
        WHERE
        ga.goods_id = #{goodsId}
    </select>
</mapper>
