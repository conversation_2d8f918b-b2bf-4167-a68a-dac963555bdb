<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logic.code.mapper.CollectMapper">
    <resultMap id="BaseResultMap" type="com.logic.code.entity.Collect">
        <!--
      WARNING - @mbg.generated
    -->
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="user_id" property="userId" jdbcType="INTEGER"/>
        <result column="value_id" property="valueId" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="is_attention" property="attention" jdbcType="BIT"/>
        <result column="type_id" property="typeId" jdbcType="INTEGER"/>
    </resultMap>
    <select id="selectGoodsCollectByUserId" resultType="com.logic.code.model.dto.GoodsCollectDTO">
        SELECT
        c.*, g.name, g.list_pic_url, g.goods_brief, g.retail_price
        FROM
        weshop_collect c
        LEFT JOIN
        weshop_goods g ON c.value_id = g.id
        WHERE
        c.user_id = #{userId} AND type_id = 0;
    </select>
</mapper>
