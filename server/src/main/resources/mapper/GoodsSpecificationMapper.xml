<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logic.code.mapper.GoodsSpecificationMapper">
    <resultMap id="BaseResultMap" type="com.logic.code.entity.goods.GoodsSpecification">
        <!--
      WARNING - @mbg.generated
    -->
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="goods_id" property="goodsId" jdbcType="INTEGER"/>
        <result column="specification_id" property="specificationId" jdbcType="INTEGER"/>
        <result column="value" property="value" jdbcType="VARCHAR"/>
        <result column="pic_url" property="picUrl" jdbcType="VARCHAR"/>
    </resultMap>
    <select id="selectGoodsDetailSpecificationByGoodsId" resultType="com.logic.code.model.dto.GoodsSpecificationDTO">
        SELECT
        gs.*, s.name
        FROM
        weshop_goods_specification AS gs
        INNER JOIN
        weshop_specification AS s ON s.id = gs.specification_id
        WHERE
        gs.goods_id = #{goodsId};
    </select>
    <select id="selectValueByGoodsIdAndIdIn" resultType="java.lang.String">
        select
        `value`
        from
        weshop_goods_specification
        where
        goods_id=#{goodsId}
        and specification_id in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
