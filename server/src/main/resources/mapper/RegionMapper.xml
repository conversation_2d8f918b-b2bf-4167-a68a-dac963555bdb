<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logic.code.mapper.RegionMapper">
    <resultMap id="BaseResultMap" type="com.logic.code.entity.Region">
        <id column="id" property="id" jdbcType="SMALLINT"/>
        <result column="parent_id" property="parentId" jdbcType="SMALLINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="INTEGER" typeHandler="com.logic.code.common.typehandlers.RegionTypeEnumTypeHandler"/>
        <result column="agency_id" property="agencyId" jdbcType="SMALLINT"/>
    </resultMap>
</mapper> 