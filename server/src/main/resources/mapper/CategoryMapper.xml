<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logic.code.mapper.CategoryMapper">
    <resultMap id="BaseResultMap" type="com.logic.code.entity.goods.Category">
        <!--
      WARNING - @mbg.generated
    -->
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="keywords" property="keywords" jdbcType="VARCHAR"/>
        <result column="front_desc" property="frontDesc" jdbcType="VARCHAR"/>
        <result column="parent_id" property="parentId" jdbcType="INTEGER"/>
        <result column="sort_order" property="sortOrder" jdbcType="BIT"/>
        <result column="show_index" property="showIndex" jdbcType="BIT"/>
        <result column="is_show" property="show" jdbcType="BIT"/>
        <result column="banner_url" property="bannerUrl" jdbcType="VARCHAR"/>
        <result column="icon_url" property="iconUrl" jdbcType="VARCHAR"/>
        <result column="img_url" property="imgUrl" jdbcType="VARCHAR"/>
        <result column="wap_banner_url" property="wapBannerUrl" jdbcType="VARCHAR"/>
        <result column="level" property="level" jdbcType="INTEGER" typeHandler="com.logic.code.config.typehandler.CategoryLevelEnumTypeHandler"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="front_name" property="frontName" jdbcType="VARCHAR"/>
    </resultMap>
    <select id="selectParentIdsByIdIn" resultType="java.lang.Integer">
        select distinct parent_id from weshop_category where id in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
