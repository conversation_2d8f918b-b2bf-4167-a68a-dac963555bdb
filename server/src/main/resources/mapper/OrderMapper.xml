<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logic.code.mapper.OrderMapper">
    <resultMap id="BaseResultMap" type="com.logic.code.entity.order.Order">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="order_sn" property="orderSn" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="INTEGER"/>
        <result column="order_status" property="orderStatus" jdbcType="INTEGER" typeHandler="com.logic.code.common.typehandlers.OrderStatusEnumTypeHandler"/>
        <result column="shipping_status" property="shippingStatus" jdbcType="SMALLINT"/>
        <result column="pay_status" property="payStatus" jdbcType="INTEGER" typeHandler="com.logic.code.common.typehandlers.PayStatusEnumTypeHandler"/>
        <result column="consignee" property="consignee" jdbcType="VARCHAR"/>
        <result column="country" property="country" jdbcType="SMALLINT"/>
        <result column="province" property="province" jdbcType="SMALLINT"/>
        <result column="city" property="city" jdbcType="SMALLINT"/>
        <result column="district" property="district" jdbcType="SMALLINT"/>
        <result column="address" property="address" jdbcType="VARCHAR"/>
        <result column="mobile" property="mobile" jdbcType="VARCHAR"/>
        <result column="postscript" property="postscript" jdbcType="VARCHAR"/>
        <result column="shipping_fee" property="shippingFee" jdbcType="DECIMAL"/>
        <result column="pay_name" property="payName" jdbcType="VARCHAR"/>
        <result column="pay_id" property="payId" jdbcType="TINYINT"/>
        <result column="actual_price" property="actualPrice" jdbcType="DECIMAL"/>
        <result column="integral" property="integral" jdbcType="INTEGER"/>
        <result column="integral_money" property="integralMoney" jdbcType="DECIMAL"/>
        <result column="order_price" property="orderPrice" jdbcType="DECIMAL"/>
        <result column="goods_price" property="goodsPrice" jdbcType="DECIMAL"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="confirm_time" property="confirmTime" jdbcType="TIMESTAMP"/>
        <result column="pay_time" property="payTime" jdbcType="TIMESTAMP"/>
        <result column="freight_price" property="freightPrice" jdbcType="DECIMAL"/>
        <result column="coupon_id" property="couponId" jdbcType="INTEGER"/>
        <result column="parent_id" property="parentId" jdbcType="INTEGER"/>
        <result column="coupon_price" property="couponPrice" jdbcType="DECIMAL"/>
        <result column="callback_status" property="callbackStatus" jdbcType="VARCHAR"/>
    </resultMap>
</mapper> 