logging.level.org.springframework.web=info
weshop.swagger.title=\u5FAE\u4FE1\u7AEFAPI
weshop.swagger.basePackage=tech.wetech.weshop.wechat.controller
weshop.swagger.description=\u5FAE\u4FE1\u7AEFAPI\u6587\u6863
wx.miniapp.configs[0].appid=wxca19c2771bc2ea41
wx.miniapp.configs[0].secret=cca2b3b03a56d89aece1643aa7f30e58
wx.miniapp.configs[0].token=wetech.tech
wx.miniapp.configs[0].aesKey=NDOLmGgEXiRzAioMwdGQIMZv29Pvh1PiKUEewc0r4is
wx.miniapp.configs[0].msgDataFormat=JSON
weshop.wx.miniapp.appid=wxca19c2771bc2ea41
weshop-wjhx.login-interceptor-exclude-path[0]=/
weshop-wjhx.login-interceptor-exclude-path[1]=/csrf
weshop-wjhx.login-interceptor-exclude-path[2]=/error
weshop-wjhx.login-interceptor-exclude-path[3]=/favicon.ico
weshop-wjhx.login-interceptor-exclude-path[4]=/swagger-resources/**
weshop-wjhx.login-interceptor-exclude-path[5]=/webjars/**
weshop-wjhx.login-interceptor-exclude-path[6]=/v2/**
weshop-wjhx.login-interceptor-exclude-path[7]=/swagger-ui.html/**
weshop-wjhx.login-interceptor-exclude-path[8]=/wechat/brand/**
weshop-wjhx.login-interceptor-exclude-path[9]=/wechat/catalog/**
weshop-wjhx.login-interceptor-exclude-path[10]=/wechat/goods/**
weshop-wjhx.login-interceptor-exclude-path[11]=/wechat/home/<USER>
weshop-wjhx.login-interceptor-exclude-path[12]=/wechat/search/**
weshop-wjhx.login-interceptor-exclude-path[13]=/wechat/topic/**
weshop-wjhx.login-interceptor-exclude-path[14]=/wechat/auth/login
weshop-wjhx.login-interceptor-exclude-path[15]=/wechat/dev/{userId}/token
