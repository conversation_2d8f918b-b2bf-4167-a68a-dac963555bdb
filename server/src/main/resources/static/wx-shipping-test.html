<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信发货信息管理功能测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .test-section h3 {
            color: #007cba;
            margin-top: 0;
        }
        button {
            background-color: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #005a8b;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        input, select {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        .loading {
            display: none;
            color: #007cba;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 微信发货信息管理功能测试</h1>
        
        <!-- 健康检查 -->
        <div class="test-section">
            <h3>1. 健康检查</h3>
            <p>检查测试接口是否正常运行</p>
            <button onclick="testHealth()">健康检查</button>
            <div class="loading" id="health-loading">检查中...</div>
            <div id="health-result" class="result" style="display:none;"></div>
        </div>

        <!-- 服务状态检查 -->
        <div class="test-section">
            <h3>2. 服务开通状态检查</h3>
            <p>检查微信小程序是否已开通发货信息管理服务</p>
            <button onclick="testServiceStatus()">检查服务状态</button>
            <div class="loading" id="service-loading">检查中...</div>
            <div id="service-result" class="result" style="display:none;"></div>
        </div>

        <!-- 设置跳转路径 -->
        <div class="test-section">
            <h3>3. 设置消息跳转路径</h3>
            <p>设置用户点击发货消息时跳转到的小程序页面路径</p>
            <div class="form-group">
                <label>跳转路径:</label>
                <input type="text" id="jumpPath" value="pages/order/detail" placeholder="例如: pages/order/detail">
            </div>
            <button onclick="testSetJumpPath()">设置跳转路径</button>
            <div class="loading" id="jump-loading">设置中...</div>
            <div id="jump-result" class="result" style="display:none;"></div>
        </div>

        <!-- 发货信息录入测试 -->
        <div class="test-section">
            <h3>4. 发货信息录入测试</h3>
            <p>使用模拟数据测试发货信息录入功能</p>
            <button onclick="getTestData()">获取测试数据</button>
            <button onclick="testUploadShipping()">测试发货信息录入</button>
            <div class="loading" id="upload-loading">处理中...</div>
            <div id="upload-result" class="result" style="display:none;"></div>
        </div>

        <!-- 查询订单状态 -->
        <div class="test-section">
            <h3>5. 查询订单发货状态</h3>
            <p>查询指定订单在微信平台的发货状态</p>
            <div class="form-group">
                <label>微信支付单号:</label>
                <input type="text" id="transactionId" value="test_transaction_id" placeholder="输入微信支付单号">
            </div>
            <button onclick="testOrderStatus()">查询订单状态</button>
            <div class="loading" id="status-loading">查询中...</div>
            <div id="status-result" class="result" style="display:none;"></div>
        </div>

        <!-- 使用说明 -->
        <div class="test-section">
            <h3>📖 使用说明</h3>
            <ul>
                <li><strong>健康检查</strong>: 验证测试接口是否正常运行</li>
                <li><strong>服务状态检查</strong>: 检查小程序是否已开通发货信息管理服务</li>
                <li><strong>设置跳转路径</strong>: 配置用户点击发货消息时的跳转页面</li>
                <li><strong>发货信息录入</strong>: 测试向微信平台上传发货信息</li>
                <li><strong>查询订单状态</strong>: 查询订单在微信平台的发货状态</li>
            </ul>
            <p><strong>注意</strong>: 此页面仅在开发环境下可用，生产环境请使用管理后台进行操作。</p>
        </div>
    </div>

    <script>
        const API_BASE = '';

        // 显示加载状态
        function showLoading(id) {
            document.getElementById(id + '-loading').style.display = 'block';
            document.getElementById(id + '-result').style.display = 'none';
        }

        // 隐藏加载状态
        function hideLoading(id) {
            document.getElementById(id + '-loading').style.display = 'none';
        }

        // 显示结果
        function showResult(id, data, isSuccess = true) {
            hideLoading(id);
            const resultDiv = document.getElementById(id + '-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result ' + (isSuccess ? 'success' : 'error');
            resultDiv.textContent = JSON.stringify(data, null, 2);
        }

        // 健康检查
        async function testHealth() {
            showLoading('health');
            try {
                const response = await fetch(API_BASE + '/test/wx/shipping/health');
                const data = await response.json();
                showResult('health', data, data.success !== false);
            } catch (error) {
                showResult('health', { error: error.message }, false);
            }
        }

        // 检查服务状态
        async function testServiceStatus() {
            showLoading('service');
            try {
                const response = await fetch(API_BASE + '/test/wx/shipping/test-service-status');
                const data = await response.json();
                showResult('service', data, data.success !== false);
            } catch (error) {
                showResult('service', { error: error.message }, false);
            }
        }

        // 设置跳转路径
        async function testSetJumpPath() {
            showLoading('jump');
            const path = document.getElementById('jumpPath').value;
            try {
                const response = await fetch(API_BASE + '/test/wx/shipping/test-jump-path?path=' + encodeURIComponent(path), {
                    method: 'POST'
                });
                const data = await response.json();
                showResult('jump', data, data.success !== false);
            } catch (error) {
                showResult('jump', { error: error.message }, false);
            }
        }

        // 获取测试数据
        async function getTestData() {
            showLoading('upload');
            try {
                const response = await fetch(API_BASE + '/test/wx/shipping/get-test-data');
                const data = await response.json();
                showResult('upload', data, data.success !== false);
            } catch (error) {
                showResult('upload', { error: error.message }, false);
            }
        }

        // 测试发货信息录入
        async function testUploadShipping() {
            showLoading('upload');
            try {
                const response = await fetch(API_BASE + '/test/wx/shipping/test-upload-shipping', {
                    method: 'POST'
                });
                const data = await response.json();
                showResult('upload', data, data.success !== false);
            } catch (error) {
                showResult('upload', { error: error.message }, false);
            }
        }

        // 查询订单状态
        async function testOrderStatus() {
            showLoading('status');
            const transactionId = document.getElementById('transactionId').value;
            try {
                const response = await fetch(API_BASE + '/test/wx/shipping/test-order-status?transactionId=' + encodeURIComponent(transactionId));
                const data = await response.json();
                showResult('status', data, data.success !== false);
            } catch (error) {
                showResult('status', { error: error.message }, false);
            }
        }

        // 页面加载完成后自动执行健康检查
        window.onload = function() {
            testHealth();
        };
    </script>
</body>
</html>
