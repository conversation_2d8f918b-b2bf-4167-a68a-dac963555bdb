{"token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJwd2QiOiJmMzdmNTAwNDE3NmEzN2JkNDk3YTNmODRmNzg4MDg2YyIsImlzcyI6InY1LmNybWViLm5ldCIsImF1ZCI6InY1LmNybWViLm5ldCIsImlhdCI6MTc1NTI3MzQxMSwibmJmIjoxNzU1MjczNDExLCJleHAiOjE3NTc4NjU0MTEsImp0aSI6eyJpZCI6NSwidHlwZSI6ImFkbWluIn19.uV2QjcmTcAKYXmLA7JVNNshXKbuPuo2mcCCkFK5tfRo", "expires_time": 1757865411, "menus": [{"id": 7, "pid": 0, "path": "/admin/index", "title": "主页", "icon": "s-home", "header": "home", "is_header": 1, "is_show": 1, "auth": ["hidden"]}, {"id": 9, "pid": 0, "path": "/admin/user", "title": "用户", "icon": "user-solid", "header": "user", "is_header": 1, "is_show": 1, "auth": ["hidden"], "children": [{"id": 718, "pid": 9, "path": "/admin/statistic/user", "title": "用户统计", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 10, "pid": 9, "path": "/admin/user/list", "title": "用户管理", "icon": "", "header": "user", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 227, "pid": 9, "path": "/admin/user/group", "title": "用户分组", "icon": "", "header": "user", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 589, "pid": 9, "path": "/admin/user/label", "title": "用户标签", "icon": "", "header": "user", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 11, "pid": 9, "path": "/admin/user/level", "title": "用户等级", "icon": "", "header": "user", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 3423, "pid": 9, "path": "/admin/setting/user_config/2/100", "title": "用户配置", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}]}, {"id": 4, "pid": 0, "path": "/admin/order", "title": "订单", "icon": "s-order", "header": "home", "is_header": 1, "is_show": 1, "auth": ["hidden"], "children": [{"id": 997, "pid": 4, "path": "/admin/statistic/order", "title": "订单统计", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 5, "pid": 4, "path": "/admin/order/list", "title": "订单管理", "icon": "", "header": "order", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 897, "pid": 4, "path": "/admin/order/refund", "title": "售后订单", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 760, "pid": 4, "path": "/admin/order/offline", "title": "收银订单", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 302, "pid": 4, "path": "/admin/setting/merchant/system_verify_order/index", "title": "核销记录", "icon": "", "header": "setting", "is_header": 1, "is_show": 1, "auth": ["hidden"]}, {"id": 3424, "pid": 4, "path": "/admin/setting/order_config/2/113", "title": "订单配置", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}]}, {"id": 1, "pid": 0, "path": "/admin/product", "title": "商品", "icon": "s-shop", "header": "0", "is_header": 1, "is_show": 1, "auth": ["hidden"], "children": [{"id": 717, "pid": 1, "path": "/admin/statistic/product", "title": "商品统计", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 2, "pid": 1, "path": "/admin/product/product_list", "title": "商品管理", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 3, "pid": 1, "path": "/admin/product/product_classify", "title": "商品分类", "icon": "", "header": "product", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 99, "pid": 1, "path": "/admin/product/product_attr", "title": "商品规格", "icon": "", "header": "product", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 3452, "pid": 1, "path": "/admin/product/param/list", "title": "商品参数", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 3454, "pid": 1, "path": "/admin/product/label/list", "title": "商品标签", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 3455, "pid": 1, "path": "/admin/product/protection/list", "title": "商品保障", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 6, "pid": 1, "path": "/admin/product/product_reply", "title": "商品评论", "icon": "", "header": "product", "is_header": 0, "is_show": 1, "auth": ["hidden"]}]}, {"id": 27, "pid": 0, "path": "/admin/marketing", "title": "营销", "icon": "s-marketing", "header": "home", "is_header": 1, "is_show": 1, "auth": ["hidden"], "children": [{"id": 30, "pid": 27, "path": "/admin/marketing/store_coupon", "title": "优惠券", "icon": "", "header": "marketing", "is_header": 0, "is_show": 1, "auth": ["hidden"], "children": [{"id": 71, "pid": 30, "path": "/admin/marketing/store_coupon_issue/index", "title": "优惠券列表", "icon": "", "header": "marketing", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 72, "pid": 30, "path": "/admin/marketing/store_coupon_user/index", "title": "用户领取记录", "icon": "", "header": "marketing", "is_header": 0, "is_show": 1, "auth": ["hidden"]}]}, {"id": 34, "pid": 27, "path": "/admin/marketing/user_point", "title": "积分管理", "icon": "", "header": "marketing", "is_header": 0, "is_show": 1, "auth": ["hidden"], "children": [{"id": 1002, "pid": 34, "path": "/admin/marketing/point_statistic", "title": "积分统计", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 905, "pid": 34, "path": "/admin/marketing/store_integral/index", "title": "积分商品", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 912, "pid": 34, "path": "/admin/marketing/store_integral/order_list", "title": "积分订单", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 1001, "pid": 34, "path": "/admin/marketing/point_record", "title": "积分记录", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 79, "pid": 34, "path": "/admin/marketing/integral/system_config/2/11", "title": "积分配置", "icon": "", "header": "marketing", "is_header": 1, "is_show": 1, "auth": ["hidden"]}]}, {"id": 909, "pid": 27, "path": "/admin/marketing/lottery/index", "title": "抽奖管理", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"], "children": [{"id": 3451, "pid": 909, "path": "/admin/marketing/lottery/list", "title": "抽奖列表", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 3457, "pid": 909, "path": "/admin/marketing/lottery/config", "title": "抽奖配置", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}]}, {"id": 31, "pid": 27, "path": "/admin/marketing/store_bargain", "title": "砍价管理", "icon": "", "header": "marketing", "is_header": 0, "is_show": 1, "auth": ["hidden"], "children": [{"id": 74, "pid": 31, "path": "/admin/marketing/store_bargain/index", "title": "砍价商品", "icon": "", "header": "marketing", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 755, "pid": 31, "path": "/admin/marketing/store_bargain/bargain_list", "title": "砍价列表", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}]}, {"id": 32, "pid": 27, "path": "/admin/marketing/store_combination", "title": "拼团管理", "icon": "", "header": "marketing", "is_header": 0, "is_show": 1, "auth": ["hidden"], "children": [{"id": 75, "pid": 32, "path": "/admin/marketing/store_combination/index", "title": "拼团商品", "icon": "", "header": "marketing", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 76, "pid": 32, "path": "/admin/marketing/store_combination/combina_list", "title": "拼团列表", "icon": "", "header": "marketing", "is_header": 0, "is_show": 1, "auth": ["hidden"]}]}, {"id": 33, "pid": 27, "path": "/admin/marketing/store_seckill", "title": "秒杀管理", "icon": "", "header": "marketing", "is_header": 0, "is_show": 1, "auth": ["hidden"], "children": [{"id": 3456, "pid": 33, "path": "/admin/marketing/store_seckill/list", "title": "秒杀列表", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 77, "pid": 33, "path": "/admin/marketing/store_seckill/index", "title": "秒杀商品", "icon": "", "header": "marketing", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 78, "pid": 33, "path": "/admin/marketing/store_seckill_data/index/49", "title": "秒杀配置", "icon": "", "header": "marketing", "is_header": 0, "is_show": 1, "auth": ["hidden"]}]}, {"id": 731, "pid": 27, "path": "/admin/user/grade", "title": "付费会员", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"], "children": [{"id": 751, "pid": 731, "path": "/admin/user/grade/type", "title": "会员类型", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 765, "pid": 731, "path": "/admin/user/grade/right", "title": "会员权益", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 762, "pid": 731, "path": "/admin/user/grade/card", "title": "卡密会员", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 763, "pid": 731, "path": "/admin/user/grade/record", "title": "会员记录", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 1075, "pid": 731, "path": "/admin/setting/member_config/2/67", "title": "会员配置", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}]}, {"id": 686, "pid": 27, "path": "/admin/marketing/live", "title": "直播管理", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"], "children": [{"id": 687, "pid": 686, "path": "/admin/marketing/live/live_room", "title": "直播间管理", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 688, "pid": 686, "path": "/admin/marketing/live/live_goods", "title": "直播商品管理", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 689, "pid": 686, "path": "/admin/marketing/live/anchor", "title": "主播管理", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}]}, {"id": 3420, "pid": 27, "path": "/admin/marketing/recharge", "title": "用户充值", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"], "children": [{"id": 1053, "pid": 3420, "path": "/admin/marketing/recharge", "title": "金额设置", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 3422, "pid": 3420, "path": "/admin/setting/recharge_config/2/28", "title": "充值配置", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}]}, {"id": 3425, "pid": 27, "path": "/admin/markering/sign", "title": "每日签到", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"], "children": [{"id": 3426, "pid": 3425, "path": "/admin/setting/sign_config/2/126", "title": "签到配置", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 3427, "pid": 3425, "path": "/admin/marketing/sign_rewards", "title": "签到奖励", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}]}, {"id": 1023, "pid": 27, "path": "/admin/marketing/channel_code/channelCodeIndex", "title": "渠道码", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 3446, "pid": 27, "path": "/admin/marketing/newuser/gift", "title": "新人礼", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}]}, {"id": 26, "pid": 0, "path": "/admin/agent", "title": "分销", "icon": "s-promotion", "header": "user", "is_header": 1, "is_show": 1, "auth": ["hidden"], "children": [{"id": 29, "pid": 26, "path": "/admin/agent/agent_manage/index", "title": "分销员管理", "icon": "", "header": "user", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 896, "pid": 26, "path": "/admin/setting/membership_level/index", "title": "分销等级", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 28, "pid": 26, "path": "/admin/setting/system_config_retail/2/9", "title": "分销设置", "icon": "", "header": "setting", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 1013, "pid": 26, "path": "/admin/agent/division", "title": "事业部", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"], "children": [{"id": 1014, "pid": 1013, "path": "/admin/division/index", "title": "事业部列表", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 1015, "pid": 1013, "path": "/admin/division/agent/index", "title": "代理商列表", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 1016, "pid": 1013, "path": "/admin/division/agent/applyList", "title": "代理商申请", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}]}]}, {"id": 165, "pid": 0, "path": "/admin/kefu", "title": "客服", "icon": "message-solid", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"], "children": [{"id": 678, "pid": 165, "path": "/admin/setting/store_service/index", "title": "客服列表", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 679, "pid": 165, "path": "/admin/setting/store_service/speechcraft", "title": "客服话术", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 738, "pid": 165, "path": "/admin/setting/store_service/feedback", "title": "用户留言", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 3429, "pid": 165, "path": "/admin/setting/store_service/auto_reply", "title": "自动回复", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 3421, "pid": 165, "path": "/admin/setting/kefu_config/2/69", "title": "客服配置", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}]}, {"id": 35, "pid": 0, "path": "/admin/finance", "title": "财务", "icon": "s-finance", "header": "home", "is_header": 1, "is_show": 1, "auth": ["hidden"], "children": [{"id": 766, "pid": 35, "path": "/admin/statistic/transaction", "title": "交易统计", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 36, "pid": 35, "path": "/admin/finance/user_extract", "title": "财务操作", "icon": "", "header": "finance", "is_header": 0, "is_show": 1, "auth": ["hidden"], "children": [{"id": 39, "pid": 36, "path": "/admin/finance/user_extract/index", "title": "提现申请", "icon": "", "header": "finance", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 767, "pid": 36, "path": "/admin/order/invoice/list", "title": "发票管理", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}]}, {"id": 37, "pid": 35, "path": "/admin/finance/user_recharge", "title": "财务记录", "icon": "", "header": "finance", "is_header": 0, "is_show": 1, "auth": ["hidden"], "children": [{"id": 40, "pid": 37, "path": "/admin/finance/user_recharge/index", "title": "充值记录", "icon": "", "header": "finance", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 998, "pid": 37, "path": "/admin/finance/capital_flow/index", "title": "资金流水", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 999, "pid": 37, "path": "/admin/finance/billing_records/index", "title": "账单记录", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}]}, {"id": 38, "pid": 35, "path": "/admin/finance/finance", "title": "佣金记录", "icon": "", "header": "finance", "is_header": 0, "is_show": 1, "auth": ["hidden"], "children": [{"id": 42, "pid": 38, "path": "/admin/finance/finance/commission", "title": "佣金记录", "icon": "", "header": "finance", "is_header": 0, "is_show": 1, "auth": ["hidden"]}]}, {"id": 1003, "pid": 35, "path": "/admin/finance/balance", "title": "余额记录", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"], "children": [{"id": 1005, "pid": 1003, "path": "/admin/statistic/balance", "title": "余额统计", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 1004, "pid": 1003, "path": "/admin/finance/balance/balance", "title": "余额记录", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}]}]}, {"id": 43, "pid": 0, "path": "/admin/cms", "title": "内容", "icon": "s-management", "header": "home", "is_header": 1, "is_show": 1, "auth": ["hidden"], "children": [{"id": 44, "pid": 43, "path": "/admin/cms/article/index", "title": "文章管理", "icon": "", "header": "cms", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 45, "pid": 43, "path": "/admin/cms/article_category/index", "title": "文章分类", "icon": "", "header": "cms", "is_header": 0, "is_show": 1, "auth": ["hidden"]}]}, {"id": 656, "pid": 0, "path": "/admin/setting/pages", "title": "装修", "icon": "s-open", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"], "children": [{"id": 657, "pid": 656, "path": "/admin/setting/pages/devise/0", "title": "首页装修", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 3440, "pid": 656, "path": "/admin/setting/pages/cate_page/1", "title": "商品分类", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 3441, "pid": 656, "path": "/admin/setting/pages/user_page/2", "title": "个人中心", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 128, "pid": 656, "path": "/admin/setting/system_visualization_data", "title": "数据配置", "icon": "", "header": "system", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 902, "pid": 656, "path": "/admin/setting/theme_style", "title": "主题风格", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 566, "pid": 656, "path": "/admin/system/file", "title": "素材管理", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 3453, "pid": 656, "path": "/admin/setting/pages/link", "title": "链接管理", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}]}, {"id": 135, "pid": 0, "path": "/admin/app", "title": "应用", "icon": "menu", "header": "app", "is_header": 1, "is_show": 1, "auth": ["hidden"], "children": [{"id": 69, "pid": 135, "path": "/admin/app/wechat", "title": "公众号", "icon": "", "header": "app", "is_header": 0, "is_show": 1, "auth": ["hidden"], "children": [{"id": 92, "pid": 69, "path": "/admin/app/wechat/setting/menus/index", "title": "微信菜单", "icon": "", "header": "app", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 109, "pid": 69, "path": "/admin/app/wechat/news_category/index", "title": "图文管理", "icon": "", "header": "app", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 114, "pid": 69, "path": "/admin/app/wechat/reply", "title": "自动回复", "icon": "", "header": "app", "is_header": 0, "is_show": 1, "auth": ["hidden"], "children": [{"id": 113, "pid": 114, "path": "/admin/app/wechat/reply/follow/subscribe", "title": "关注回复", "icon": "", "header": "app", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 115, "pid": 114, "path": "/admin/app/wechat/reply/keyword", "title": "关键字回复", "icon": "", "header": "app", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 116, "pid": 114, "path": "/admin/app/wechat/reply/index/default", "title": "无效词回复", "icon": "", "header": "app", "is_header": 0, "is_show": 1, "auth": ["hidden"]}]}, {"id": 1006, "pid": 69, "path": "/admin/setting/wechat_config/2/2", "title": "公众号配置", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}]}, {"id": 993, "pid": 135, "path": "/admin/app/routine", "title": "小程序", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"], "children": [{"id": 994, "pid": 993, "path": "/admin/app/routine/download", "title": "小程序下载", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 1007, "pid": 993, "path": "/admin/setting/routine_config/2/7", "title": "小程序配置", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}]}, {"id": 1009, "pid": 135, "path": "/admin/app/app", "title": "APP", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"], "children": [{"id": 1011, "pid": 1009, "path": "/admin/setting/app_config/2/77", "title": "APP配置", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 1055, "pid": 1009, "path": "/admin/app/app/version", "title": "版本管理", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}]}, {"id": 1008, "pid": 135, "path": "/admin/app/pc", "title": "PC端", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"], "children": [{"id": 903, "pid": 1008, "path": "/admin/setting/pc_group_data", "title": "PC端装修", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 1010, "pid": 1008, "path": "/admin/setting/pc_config/2/75", "title": "PC端配置", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}]}]}, {"id": 12, "pid": 0, "path": "/admin/setting", "title": "设置", "icon": "s-tools", "header": "setting", "is_header": 1, "is_show": 1, "auth": ["hidden"], "children": [{"id": 23, "pid": 12, "path": "/admin/setting/system_config/2/129", "title": "系统设置", "icon": "", "header": "setting", "is_header": 1, "is_show": 1, "auth": ["hidden"]}, {"id": 898, "pid": 12, "path": "/admin/setting/notification/index", "title": "消息管理", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 1061, "pid": 12, "path": "/admin/setting/agreement", "title": "协议设置", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 3448, "pid": 12, "path": "/admin/setting/ticket", "title": "小票配置", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 14, "pid": 12, "path": "/admin/setting/auth/list", "title": "管理权限", "icon": "", "header": "setting", "is_header": 0, "is_show": 1, "auth": ["hidden"], "children": [{"id": 19, "pid": 14, "path": "/admin/setting/system_role/index", "title": "角色管理", "icon": "", "header": "setting", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 20, "pid": 14, "path": "/admin/setting/system_admin/index", "title": "管理员列表", "icon": "", "header": "setting", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 21, "pid": 14, "path": "/admin/setting/system_menus/index", "title": "权限设置", "icon": "", "header": "setting", "is_header": 1, "is_show": 1, "auth": ["hidden"]}]}, {"id": 303, "pid": 12, "path": "/admin/setting/freight", "title": "发货设置", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"], "children": [{"id": 720, "pid": 303, "path": "/admin/setting/delivery_service/index", "title": "配送员管理", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 144, "pid": 303, "path": "/admin/setting/merchant/system_store/index", "title": "提货点设置", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"], "children": [{"id": 300, "pid": 144, "path": "/admin/setting/merchant/system_store/list", "title": "提货点", "icon": "", "header": "setting", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 301, "pid": 144, "path": "/admin/setting/merchant/system_store_staff/index", "title": "核销员", "icon": "", "header": "setting", "is_header": 0, "is_show": 1, "auth": ["hidden"]}]}, {"id": 230, "pid": 303, "path": "/admin/setting/freight/shipping_templates/list", "title": "运费模板", "icon": "", "header": "setting", "is_header": 0, "is_show": 1, "auth": ["hidden"]}]}, {"id": 1056, "pid": 12, "path": "/admin/setting/other_config", "title": "接口配置", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"], "children": [{"id": 3417, "pid": 1056, "path": "/admin/yihaotong", "title": "一号通", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"], "children": [{"id": 94, "pid": 3417, "path": "/admin/setting/sms/sms_config/index", "title": "一号通页面", "icon": "", "header": "setting", "is_header": 1, "is_show": 1, "auth": ["hidden"]}, {"id": 3418, "pid": 3417, "path": "/admin/setting/yihaotong_config/3/18", "title": "一号通配置", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}]}, {"id": 1012, "pid": 1056, "path": "/admin/setting/storage", "title": "系统存储配置", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 1058, "pid": 1056, "path": "/admin/setting/other_config/copy/2/41", "title": "商品采集配置", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 1059, "pid": 1056, "path": "/admin/setting/other_config/logistics/2/64", "title": "物流查询配置", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 1060, "pid": 1056, "path": "/admin/setting/other_config/electronic/2/66", "title": "电子面单配置", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 1062, "pid": 1056, "path": "/admin/setting/other_config/sms/2/96", "title": "短信接口配置", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 1063, "pid": 1056, "path": "/admin/setting/other_config/pay/2/23", "title": "商城支付配置", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}]}]}, {"id": 25, "pid": 0, "path": "/admin/system", "title": "维护", "icon": "cpu", "header": "setting", "is_header": 1, "is_show": 1, "auth": ["hidden"], "children": [{"id": 56, "pid": 25, "path": "/admin/system/config", "title": "开发配置", "icon": "", "header": "system", "is_header": 0, "is_show": 1, "auth": ["hidden"], "children": [{"id": 111, "pid": 56, "path": "/admin/system/config/system_config_tab/index", "title": "配置分类", "icon": "", "header": "system", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 112, "pid": 56, "path": "/admin/system/config/system_group/index", "title": "组合数据", "icon": "", "header": "system", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 1076, "pid": 56, "path": "/admin/system/crontab", "title": "定时任务", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 2472, "pid": 56, "path": "/admin/system/system_menus/index", "title": "权限维护", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 3443, "pid": 56, "path": "/admin/marketing/integral/system_config/2/134", "title": "模块配置", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 3445, "pid": 56, "path": "/admin/system/event", "title": "自定事件", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}]}, {"id": 65, "pid": 25, "path": "/admin/system/maintain", "title": "安全维护", "icon": "", "header": "system", "is_header": 0, "is_show": 1, "auth": ["hidden"], "children": [{"id": 57, "pid": 65, "path": "/admin/system/maintain/clear/index", "title": "刷新缓存", "icon": "", "header": "system", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 47, "pid": 65, "path": "/admin/system/maintain/system_log/index", "title": "系统日志", "icon": "", "header": "system", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 655, "pid": 65, "path": "/admin/system/onlineUpgrade/index", "title": "在线升级", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}]}, {"id": 1073, "pid": 25, "path": "/adminsystem/database/index", "title": "数据维护", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"], "children": [{"id": 145, "pid": 1073, "path": "/admin/setting/freight/express/index", "title": "物流公司", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 229, "pid": 1073, "path": "/admin/setting/freight/city/list", "title": "城市数据", "icon": "", "header": "setting", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 66, "pid": 1073, "path": "/admin/system/maintain/system_cleardata/index", "title": "清除数据", "icon": "", "header": "system", "is_header": 0, "is_show": 1, "auth": ["hidden"]}]}, {"id": 1064, "pid": 25, "path": "/admin/setting/other_out_config", "title": "对外接口", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"], "children": [{"id": 1066, "pid": 1064, "path": "/admin/setting/system_out_account/index", "title": "账号管理", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}]}, {"id": 1067, "pid": 25, "path": "/admin/setting/lang", "title": "语言设置", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"], "children": [{"id": 1068, "pid": 1067, "path": "/admin/setting/lang/list", "title": "语言列表", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 1069, "pid": 1067, "path": "/admin/setting/lang/info", "title": "语言详情", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 1070, "pid": 1067, "path": "/admin/setting/lang/country", "title": "地区列表", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 3419, "pid": 1067, "path": "/admin/setting/lang_config/3/106", "title": "翻译配置", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}]}, {"id": 1695, "pid": 25, "path": "/admin/tool", "title": "开发工具", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"], "children": [{"id": 67, "pid": 1695, "path": "/admin/system/maintain/system_databackup/index", "title": "数据库管理", "icon": "", "header": "system", "is_header": 1, "is_show": 1, "auth": ["hidden"]}, {"id": 1071, "pid": 1695, "path": "/admin/system/maintain/system_file/opendir", "title": "文件管理", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 1078, "pid": 1695, "path": "/admin/system/backend_routing", "title": "接口管理", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 1101, "pid": 1695, "path": "/admin/system/code_generation_list", "title": "代码生成", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}, {"id": 3430, "pid": 1695, "path": "/admin/system/code_data_dictionary", "title": "数据字典", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}]}, {"id": 605, "pid": 25, "path": "/admin/system/maintain/auth", "title": "系统信息", "icon": "", "header": "", "is_header": 0, "is_show": 1, "auth": ["hidden"]}]}], "unique_auth": ["admin-product", "admin-store-storeProuduct-index", "admin-store-storeCategory-index", "admin-order", "admin-order-storeOrder-index", "product-product-reply", "admin-home", "admin-user", "admin-user-user-index", "user-user-level", "admin-setting", "setting-system-admin", "setting-system-role", "setting-system-list", "setting-system-menus", "setting-system-config", "admin-system", "admin-agent", "admin-marketing", "setting-system-config", "agent-agent-manage", "marketing-store_coupon-index", "marketing-store_bargain-index", "marketing-store_combination-index", "marketing-store_seckill-index", "marketing-user_point-index", "admin-finance", "finance-user_extract-index", "finance-user-recharge-index", "finance-finance-index", "finance-user_extract", "finance-user-recharge", "finance-finance-commission", "admin-cms", "cms-article-index", "cms-article-category", "system-maintain-system-log", "system-config-index", "system-clear", "system-maintain-index", "system-maintain-system-cleardata", "system-maintain-system-databackup", "admin-wechat", "marketing-store_coupon_issue", "marketing-store_coupon_user", "marketing-store_bargain", "marketing-store_combination", "marketing-store_combination-combina_list", "marketing-store_seckill", "marketing-store_seckill-data", "marketing-integral-system_config", "application-wechat-menus", "setting-sms", "product-product-attr", "wechat-wechat-news-category-index", "system-config-system_config-tab", "system-config-system_config-group", "wechat-wechat-reply-subscribe", "wechat-wechat-reply-index", "wechat-wechat-reply-keyword", "wechat-wechat-reply-default", "admin-setting-system_visualization_data", "admin-app", "setting-system-config-merchant", "setting-freight-express", "setting-store-service", "user-user-group", "setting-system-city", "setting-shipping-templates", "setting-merchant-system-store", "setting-merchant-system-store-staff", "setting-merchant-system-verify-order", "admin-setting-freight", "system-file", "user-user-label", "system-maintain-auth", "system-onlineUpgrade-index", "admin-setting-pages", "admin-setting-pages-devise", "admin-setting-store_service-index", "admin-setting-store_service-speechcraft", "admin-marketing-live", "admin-marketing-live-live_room", "admin-marketing-live-live_goods", "admin-marketing-live-anchor", "admin-statistic", "admin-statistic", "setting-delivery-service", "user-user-grade", "admin-setting-store_service-feedback", "admin-user-member-type", "marketing-store_bargain-bargain_list", "admin-order-offline", "admin-user-grade-card", "admin-user-grade-record", "admin-user-grade-right", "admin-statistic", "admin-order-startOrderInvoice-index", "admin-setting-membership_level-index", "admin-order-refund", "setting-notification", "admin-setting-theme_style", "setting-system-pc_data", "marketing-store_integral", "marketing-lottery-index", "marketing-store_integral-order", "admin-routine", "routine-download", "admin-statistic", "finance-capital_flow-index", "finance-billing_records-index", "marketing-point_record-index", "marketing-point_statistic-index", "finance-balance-index", "finance-user-balance", "admin-statistic", "setting-system-config", "setting-system-config", "admin-pc", "admin-app", "setting-system-config", "setting-system-config", "setting-storage", "agent-division", "agent-division-index", "agent-division-agent-index", "agent-division-agent-applyList", "marketing-channel_code-index", "marketing-recharge-index", "admin-app-version", "setting-other", "setting-other-copy", "setting-other-logistics", "setting-other-electronic", "setting-agreement", "setting-other-sms", "setting-other-pay", "setting-other-out", "setting-system-out-account-index", "admin-lang", "admin-lang-list", "admin-lang-info", "admin-lang-country", "system-maintain-system-file", "system-database-index", "setting-member-config", "system-crontab-index", "system-config-backend-routing", "system-config-code-generation-list", "admin-tool", "wechat-wechat-news-category-save", "cms-article-creat", "marketing-store_combination-create", "marketing-store_coupon_issue-create", "admin-store-storeProuduct-index", "marketing-store_bargain-create", "marketing-store_seckill-create", "marketing-store_integral-create", "admin-marketing-live-add_live_room", "admin-marketing-live-add_live_goods", "marketing-channel_code-create", "admin-setting-pages-diy", "system-config-code-generation", "system-maintain-system-file", "system_menus-index", "user-create", "user-user-user_save_info", "user-user_label-add", "user-user_label-save", "user-user", "user-user-edit", "user-user", "user-user-save", "user-send-coupon", "marketing-coupon-grant", "marketing-coupon-user-grant", "user-batch-set-group", "user-set_group", "user-save_set_group", "user-batch-set-label", "user-label", "user-save_set_label", "user-export", "export-user_list", "user-info", "user-user-*************", "user-user-646580181056f", "user-user-user_save_info-6465801810573", "user-one_info", "user-user_label-add-6465802de8e2c", "user-user_label-save-6465802de8e30", "user-set-balance", "user-update_other", "user-set-level-time", "user-give_level_time", "user-save_give_level_time", "user-set-group", "user-set_group-646585b911c0b", "user-save_set_group-646585b911c11", "user-set-label", "user-label-646585fd46ff0", "user-label-646585fd46ff6", "user-set-spread", "app-wechat-kefu-create", "agent-spread", "user-group-add", "user-group-update", "user-group-delete", "user-user_group-add", "user-user_group-save", "user-user_group-save-646586e1d4557", "user-user_group-add-646586e1d455e", "user-user_group-del", "user-label-cate", "user-label-add", "user-label-update", "user-label-delete", "user-user_label_cate-create", "user-user_label_cate", "user-user_label_cate-edit", "user-user_label_cate", "user-user_label_cate", "user-user_label-save-6465897098557", "user-user_label-add-646589709855d", "user-user_label-save-6465897903b20", "user-user_label-add-6465897903b2c", "user-user_label-del", "user-level-add", "user-level-update", "user-level-delete", "user-level-status", "user-user_level", "user-user_level-create", "user-user_level-create-64658a1262574", "user-user_level-64658a126257b", "user-user_level-delete", "user-user_level-set_show", "order-delete", "order-write-off", "order-export", "order-del", "order-dels", "order-write", "order-write_update", "export-order_list", "order-edit", "order-send", "order-offline-confirm", "order-info", "order-record", "order-electron", "order-tips", "order-mark", "order-take", "order-delete", "order-express", "order-edit-6465a3050171f", "order-update", "order-delivery", "order-split_cart_info", "order-split_delivery", "order-split_order", "order-distribution", "order-express_list", "order-express-6465a3438b950", "order-sheet_info", "order-delivery-list", "order-expr-temp", "order-express-temp", "order-order_dump", "order-pay_offline", "order-info-6465a369a2c3b", "order-status", "order-sheet_info-6465a3eb77bb4", "order-express-temp-6465a3eb77bbf", "order-order_dump-6465a3eb77bc6", "order-expr-temp-6465a3eb77bcd", "order-print", "order-remark", "order-take-6465a61b2ebdb", "order-del-6465a66347928", "order-dels-6465a66347931", "order-order_dump-6465a69038c44", "order-express-temp-6465a69038c4c", "order-expr-temp-6465a69038c51", "refund-info", "refund-mark", "refund-yes", "refund-no", "refund-info-6465c125e62a0", "refund-remark", "refund-agree", "refund-refund", "refund-refund", "refund-no_refund", "refund-no_refund", "order-offline-qrcode", "order-offline_scan", "product-add", "product-copy", "product-batch-edit", "product-batch-status", "product-export", "product-info", "product-edit", "product-reply", "product-recycle", "product-cache", "product-cache", "product-product-attrs", "product-product", "product-product-rule", "product-product-rule", "product-generate_attr", "product-product-get_rule", "product-product-get_template", "product-product-get_temp_keys", "product-product-import_card", "product-crawl", "product-copy_config", "product-crawl-save", "product-copy-6465c40d4430f", "product-cache-6465c40d44313", "product-cache-6465c40d44317", "product-product-attrs-6465c40d4431b", "product-product-6465c40d44320", "product-product-rule-6465c40d44328", "product-product-rule-6465c40d4432c", "product-product-rule-6465c40d4432f", "product-generate_attr-6465c40d44333", "product-product-get_rule-6465c40d44337", "product-product-get_template-6465c40d4433b", "product-product-get_temp_keys-6465c40d4433f", "product-batch-setting", "product-product-product_unshow", "product-product-product_show", "product-product-set_show", "export-product_list", "product-product-6465c46cedb2c", "product-cache-6465c48f616d4", "product-cache-6465c48f616dd", "product-product-attrs-6465c48f616e5", "product-product-6465c48f616eb", "product-product-6465c48f616f0", "product-product-rule-6465c48f616fb", "product-product-rule-6465c48f61700", "product-product-rule-6465c48f61705", "product-generate_attr-6465c48f6170b", "product-product-get_rule-6465c48f61710", "product-product-get_template-6465c48f61715", "product-product-get_temp_keys-6465c48f6171a", "product-product-import_card-6465c48f6171f", "product-product-6465c51a7665e", "product-cate-add", "product-cate-edit", "product-cate-delete", "product-cate-status", "product-category", "product-category-create", "product-category-6465c612c552f", "product-category-6465c612c5536", "product-category-6465c638c6d16", "product-category-set_show", "product-rule-add", "product-rule-edit", "product-rule-delete", "product-product-rule-6465d8779407b", "product-product-rule-6465d87794082", "product-product-rule-6465d88b800ca", "product-product-rule-6465d88b800d4", "product-product-rule-delete", "product-reply-add", "product-reply-reply", "product-reply-delete", "product-reply-fictitious_reply", "product-reply-save_fictitious_reply", "product-reply-set_reply", "product-reply-6465d92d1ba2d", "coupon-add", "coupon-delete", "coupon-receive", "marketing-coupon-save_coupon", "marketing-coupon-copy", "marketing-coupon-released", "marketing-coupon-released-issue_log", "marketing-coupon-user", "point-product-add", "point-product-edit", "point-product-delete", "point-product-status", "point-product-record", "marketing-integral", "marketing-integral-6465e598dac7e", "marketing-integral-6465e598dac85", "marketing-integral-6465e5a7d7e0b", "marketing-integral-set_show", "point-order-send", "point-order-info", "point-order-record", "point-order-print", "point-order-mark", "point-order-take", "marketing-integral-order-delivery", "marketing-integral-order-distribution", "marketing-integral-order-distribution", "marketing-integral-order-express_list", "marketing-integral-order-express-temp", "marketing-integral-order-delivery-list", "marketing-integral-order-sheet_info", "marketing-integral-order-express", "marketing-integral-order-info", "marketing-integral-order-status", "marketing-integral-order-print", "marketing-point_record-remark", "marketing-integral-order-take", "point-record", "marketing-point_record-remark-6465fd1a75f76", "bargain-add", "bargain-export", "bargain-edit", "bargain-delete", "bargain-statistics", "marketing-bargain", "marketing-bargain", "marketing-bargain-set_status", "export-bargain_list", "marketing-bargain-6465ff00a7c2b", "marketing-bargain-6465ff00a7c33", "marketing-bargain-set_status-6465ff00a7c39", "marketing-bargain-6465ff10afa4e", "bargain-list-info", "marketing-bargain_list", "marketing-bargain_list_info", "combination-add", "combination-export", "combination-edit", "combination-delete", "combination-statistics", "marketing-combination", "marketing-combination", "marketing-combination-set_status", "export-combination_list", "marketing-combination-6466cb2165ea1", "marketing-combination-set_status-6466cb2165ea7", "marketing-combination-6466cb2165eab", "marketing-combination-6466cb2e9b9a8", "combination-list-info", "marketing-combination-combine-list", "marketing-combination-order_pink", "seckill-add", "seckill-export", "seckill-edit", "seckill-delete", "seckill-statistics", "marketing-seckill", "marketing-seckill", "marketing-seckill-set_status", "export-seckill_list", "marketing-seckill-6466d624dd816", "marketing-seckill-6466d624dd820", "marketing-seckill-set_status-6466d624dd826", "marketing-seckill-6466d630e8c12", "marketing-seckill-statistics-order", "marketing-seckill-statistics-head", "marketing-seckill-statistics-people", "member-add", "member-edit", "member-delete", "member-status", "user-member-ship", "user-member_ship-set_ship_status", "user-member_ship-save", "user-member-ship-6466db17503ca", "user-member_ship-set_ship_status-6466db17503d2", "user-member_ship-delete", "user-member_ship-save-6466db17503dc", "user-member_ship-delete-6466db246a1e5", "user-member_ship-set_ship_status-6466db33cfbdf", "member-right-edit", "member-right-status", "user-member_right-save", "user-member_right-save-6466e10aa77e5", "member-card-add", "member-card-down-qrcode", "member-card-edit", "member-card-scan", "member-card-export", "user-member_batch-save", "user-member_card-index", "user-member_batch-set_value", "user-member_card-set_status", "user-member_scan", "user-member_batch-set_value-6466f21a4c295", "user-member_card-set_status-6466f21a4c29e", "user-member_card-index-6466f23950641", "export-member_card", "live-room-add", "live-room-sync", "live-room-info", "live-room-delete", "live-room-status", "live-room-add-64671181de1c7", "live-room-detail", "live-room-syncRoom", "live-room-detail-646711a2b8bd3", "live-room-del", "live-room-set_show", "live-room-add-product", "live-room-add_goods", "live-product-add", "live-product-info", "live-product-delete", "live-goods-create", "live-goods-add", "live-goods-detail", "live-goods-del", "live-anchor-add", "live-anchor-edit", "live-anchor-delete", "live-anchor-save", "live-anchor-add-64671b7155864", "live-anchor-save-64671b7b408c3", "live-anchor-add-64671b7b408ca", "live-anchor-del", "channel-code-add", "channel-qrcode-add", "channel-qrcode-edit", "channel-qrcode-delete", "channel-qrcode-down", "channel-qrcode-statistics", "channel-qrcode-user", "app-wechat_qrcode-cate-list", "app-wechat_qrcode-cate-create", "app-wechat_qrcode-cate-save", "app-wechat_qrcode-cate-del", "app-wechat_qrcode-save", "app-wechat_qrcode-info", "app-wechat_qrcode-list", "app-wechat_qrcode-info-64671d713f4e3", "app-wechat_qrcode-save-64671d713f4e9", "app-wechat_qrcode-del", "app-wechat_qrcode-statistic", "app-wechat_qrcode-user_list", "spread-user-list", "spread-order-list", "spread-qrcode", "spread-edit-spread", "spread-delete-spread", "spread-cancel", "spread-edit-level", "agent-stair", "agent-stair-order", "agent-look_xcx_code", "agent-look_h5_code", "agent-look_code", "agent-spread-64671e9e67c6d", "agent-stair-delete_spread", "spread-level-add", "spread-level-edit", "spread-level-delete", "spread-level-task", "agent-level-create", "agent-level", "agent-level-64671f51e1c5d", "agent-level-edit", "agent-level-set_status", "agent-level-64671f5ca12b7", "agent-level_task", "agent-level_task", "agent-level_task-create", "agent-level_task-edit", "agent-level_task", "agent-level_task", "agent-level_task-set_status", "division-add", "division-edit", "division-delete", "division-scan-agent", "agent-division-create", "agent-division-save", "agent-division-create-646720ac60991", "agent-division-save-646720ac6099a", "agent-division-del", "agent-division-down_list", "division-agent-add", "division-agent-edit", "division-agent-delete", "division-agent-staff", "agent-division-agent-create", "agent-division-agent-save", "agent-division-agent-create-64672134e5497", "agent-division-agent-save-64672134e54a1", "agent-division-del-64672140f09bd", "agent-division-down_list-6467214aa534c", "division-agent-apply", "agent-division-agent_apply-list", "agent-division-examine_apply", "agent-division-apply_agent-save", "agent-division-del_apply", "service-add", "service-edit", "service-delete", "service-in", "app-wechat-kefu-add", "app-wechat-kefu-edit", "app-wechat-kefu", "app-wechat-kefu", "app-wechat-kefu-set_status", "app-wechat-kefu-edit-646723c8147fd", "app-wechat-kefu-646723c814806", "app-wechat-kefu-set_status-646723c81480d", "app-wechat-kefu-646723d59c121", "app-wechat-kefu-login", "service-speechcraft-cate-add", "service-speechcraft-add", "service-speechcraft-edit", "service-speechcraft-delete", "app-wechat-speechcraftcate", "app-wechat-speechcraftcate", "app-wechat-speechcraftcate-create", "app-wechat-speechcraftcate-edit", "app-wechat-speechcraftcate", "app-wechat-speechcraft", "app-wechat-speechcraft-create", "app-wechat-speechcraft-edit", "app-wechat-speechcraft-646726ba5bc91", "app-wechat-speechcraft-6467272d0c6c4", "service-feedback-reply", "service-feedback-delete", "app-feedback-edit", "app-feedback", "app-feedback-64672949545ff", "extract-status", "extract-edit", "finance-extract-refuse", "finance-extract-adopt", "finance-extract", "finance-extract-edit", "invoice-edit", "invoice-order-info", "order-invoice-list", "order-invoice-set", "order-invoice_order_info", "recharge-delete", "recharge-refund", "finance-recharge", "finance-recharge", "finance-recharge-64672c72c2273", "finance-recharge-refund_edit", "capital-flow-mark", "statistic-flow-set_mark", "billing-info", "billing-down", "statistic-flow-get_record", "statistic-flow-get_list", "cms-add", "cms-edit", "cms-delete", "cms-product", "cms-cms", "cms-cms-create", "cms-cms-edit", "cms-cms-64673315dd264", "cms-cms-64673315dd287", "cms-cms-6467331dc244e", "cms-cms-unrelation", "cms-cms-relation", "cms-cate-add", "cms-cate-edit", "cms-cate-delete", "cms-cate-cms", "cms-category", "cms-category-create", "cms-category-646733862d224", "cms-category-edit", "cms-category-646733a5e8994", "pages-diy-index", "pages-diy-cate", "pages-diy-user", "pages-diy-add", "pages-diy-edit", "pages-diy-delete", "pages-diy-status", "diy-save", "diy-diy_save", "diy-get_url", "diy-get_category", "diy-get_product", "diy-get_store_status", "diy-recovery", "diy-get_by_category", "diy-set_recovery", "diy-get_product_list", "diy-get_page_link", "diy-get_page_category", "diy-create", "diy-create", "diy-get_diy_info", "diy-del", "diy-get_info", "diy-get_info-646a1a8c6b2bf", "diy-get_diy_info-646a1a8c6b2c6", "diy-set_status", "diy-create-646a1a8c6b2cf", "diy-create-646a1a8c6b2d2", "diy-save-646a1a8c6b2d6", "diy-get_url-646a1a8c6b2da", "diy-diy_save-646a1a8c6b2de", "diy-get_category-646a1a8c6b2e2", "diy-get_product-646a1a8c6b2e6", "diy-recovery-646a1a8c6b2ea", "diy-get_store_status-646a1a8c6b2ed", "diy-get_by_category-646a1a8c6b2f1", "diy-set_recovery-646a1a8c6b2f5", "diy-get_product_list-646a1a8c6b2f9", "diy-get_page_category-646a1a8c6b2fd", "diy-get_page_link-646a1a8c6b301", "diy-del-646a1a97c43d4", "diy-set_status-646a1aa862dae", "pages-diy-cate-status", "diy-get_color_change", "diy-color_change", "pages-diy-member-save", "diy-member_save", "diy-get_page_link-646a1b603f626", "diy-get_page_category-646a1b603f62f", "diy-get_member", "pages-theme-status", "diy-color_change-646a1bdde013b", "diy-get_color_change-646a1bdde0144", "system-file-cate-add", "system-file-cate-delete", "system-file-add", "system-file-delete", "file-category", "file-category", "file-category-create", "file-category-edit", "file-category", "file-category-646a1ca8e74c5", "file-file", "file-file-move", "file-file-do_move", "file-file-update", "file-upload", "file-upload_type", "file-video_upload", "file-file-delete", "wechat-menu-save", "app-wechat-menu", "app-wechat-menu", "wechat-news-add", "wechat-news-edit", "wechat-news-delete", "app-wechat-news", "app-wechat-news", "app-wechat-news", "app-wechat-news-646a1d9c1d008", "app-wechat-news-646a1d9c1d011", "app-wechat-news-646a1d9c1d017", "app-wechat-news-646a1da7936d6", "wechat-follow-save", "wechat-keyword-save", "wechat-default-save", "app-wechat-keyword", "app-wechat-keyword", "app-wechat-keyword", "app-wechat-keyword", "app-wechat-keyword-set_status", "app-wechat-keyword-646a1e33e50c2", "app-wechat-keyword-646a1e33e50cc", "app-wechat-keyword-646a1e33e50d2", "app-wechat-keyword-646a1e33e50da", "app-wechat-keyword-set_status-646a1e33e50e0", "app-wechat-keyword-646a1e43aba07", "app-wechat-keyword-646a1e43aba11", "app-wechat-keyword-set_status-646a1e43aba18", "app-wechat-keyword-646a1e43aba1e", "app-wechat-keyword-646a1e43aba24", "routine-down-qrcode", "routine-down-file", "app-routine-info", "app-routine-info-646a1ed2ee6e0", "app-routine-download", "notification-sync", "notification-setting", "notification-status", "app-routine-syncSubscribe", "app-wechat-syncSubscribe", "setting-notification-index", "setting-notification-save", "setting-notification-info", "setting-notification-set_status", "agreement-save", "setting-get_agreement", "setting-get_version", "setting-save_agreement", "system-role-add", "system-role-edit", "system-role-delete", "system-role-status", "setting-role", "setting-role-create", "setting-role", "setting-role-646a206a0dd01", "setting-role-edit", "setting-role-646a206a0dd15", "setting-role-646a2078367e6", "setting-role-646a2078367ef", "setting-role-646a2083c8f0b", "setting-role-set_status", "system-admin-add", "system-admin-edit", "system-admin-delete", "system-admin-status", "setting-admin", "setting-admin", "setting-admin-create", "setting-admin-646a213532f4c", "setting-admin-edit", "setting-admin-646a213532f5f", "setting-admin-646a2141d9a56", "setting-admin-646a2141d9a61", "setting-admin-646a214f14bed", "setting-set_status", "system-menu-edit", "setting-menus-edit", "setting-menus", "setting-menus", "delivery-service-add", "delivery-service-edit", "delivery-service-delete", "delivery-service-status", "order-delivery-index", "order-delivery-add", "order-delivery-save", "order-delivery-edit", "order-delivery-update", "order-delivery-del", "order-delivery-set_status", "merchant-add", "merchant-edit", "merchant-delete", "merchant-status", "merchant-store", "merchant-store", "merchant-store-address", "merchant-store-646a231547cf8", "merchant-store-646a231547d03", "merchant-store-get_info", "merchant-store-address-646a231547d13", "merchant-store-646a23281a6ee", "merchant-store-del", "merchant-store-646a2337c993f", "merchant-store-set_show", "merchant-staff-add", "merchant-staff-edit", "merchant-staff-delete", "merchant-staff-status", "merchant-store_staff", "merchant-store_staff-create", "merchant-store_staff-save", "merchant-store_list", "merchant-store_staff-646a23be65c88", "merchant-store_staff-edit", "merchant-store_staff-save-646a23be65c98", "merchant-store_list-646a23be65c9e", "merchant-store_staff-646a23cc0a061", "merchant-store_staff-del", "merchant-store_staff-set_show", "merchant-store_staff-646a23d9d7e44", "shipping-temp-add", "shipping-temp-edit", "shipping-temp-delete", "setting-shipping_templates-list", "setting-shipping_templates-save", "setting-shipping_templates-city_list", "setting-shipping_templates-list-646a24602a752", "setting-shipping_templates-edit", "setting-shipping_templates-save-646a24602a763", "setting-shipping_templates-city_list-646a24602a769", "setting-shipping_templates-del", "setting-shipping_templates-list-646a246c229e8", "config-tab-add", "config-tab-edit", "config-tab-delete", "config-list", "config-add", "config-edit", "config-delete", "setting-config_class", "setting-config_class-create", "setting-config_class", "setting-config_class-646a2648cbb75", "setting-config_class-edit", "setting-config_class-646a2648cbb97", "setting-config_class-646a26613d6bb", "setting-config_class-646a26613d6c6", "setting-config_class-646a267255df4", "setting-config", "setting-config-646a26874c851", "setting-config-646a26874c85b", "setting-config-create", "setting-config-646a269697340", "setting-config-edit", "setting-config-646a269697353", "setting-config-646a26a48db02", "setting-config-646a26a48db0f", "setting-config-set_status", "system-group-add", "system-group-edit", "system-group-delete", "system-group-data-list", "system-group-data-add", "system-group-data-edit", "system-group-data-delete", "setting-group", "setting-group", "setting-group-646a2784797f3", "setting-group-edit", "setting-group-646a278479805", "setting-group-646a27906b21a", "setting-group-646a27906b223", "setting-group-646a27b813076", "setting-group_data", "setting-group_data-646a27c6e19ae", "setting-group_data-646a27c6e19b8", "setting-group_data-create", "setting-group_data-646a27d6c6cbf", "setting-group_data-edit", "setting-group_data-646a27d6c6cd2", "setting-group_data-646a27e50d1c1", "setting-group_data-646a27e50d1ca", "crontab-add", "crontab-edit", "crontab-delete", "crontab-status", "system-crontab-list", "system-crontab-mark", "system-crontab-save", "system-crontab-list-646a2860d3394", "system-crontab-mark-646a2860d339f", "system-crontab-info", "system-crontab-save-646a2860d33b0", "system-crontab-list-646a286e08853", "system-crontab-del", "system-crontab-list-646a287b727dc", "system-crontab-set_open", "system-admin-role-add", "system-admin-role-edit", "system-admin-role-delete", "system-admin-role-add-menus", "system-admin-role-select", "setting-menus-646a296711f5a", "setting-menus-create", "setting-menus-646a296711f72", "setting-menus-646a297963e41", "setting-menus-edit-646a297963e4d", "setting-menus-646a297963e56", "setting-menus-646a297963e5e", "setting-menus-646a29871df51", "setting-menus-646a29871df5c", "setting-menus-646a29b28b70e", "setting-menus-646a29b28b719", "setting-menus-646a29b28b720", "code-generation-add", "code-generation-scan", "code-generation-down", "code-generation-edit", "code-generation-delete", "system-crud", "system-crud-menus", "system-crud-file_path", "system-crud-column_type", "system-crud-config", "system-crud-save_file", "system-crud-646a34d60329f", "system-crud-save_file-646a34d6032aa", "system-crud-download", "system-crud-646a34e764096", "system-crud-save_file-646a34fe884b9", "system-crud-config-646a34fe884c2", "system-crud-column_type-646a34fe884c9", "system-crud-file_path-646a34fe884d0", "system-crud-646a34fe884d6", "system-crud-646a34fe884dc", "system-crud-menus-646a34fe884e3", "system-crud-646a34fe884e9", "system-crud-646a350c5c14e", "system-crud-646a350c5c15b", "route-cate", "route-sync", "route-test", "route-edit", "system-route_cate", "system-route_cate", "system-route_cate-create", "system-route_cate-edit", "system-route_cate", "system-route_cate", "system-route-sync_route", "system-route-tree", "system-route-tree-646a35e9baaef", "system-route", "system-route", "lang-list-add", "lang-list-edit", "lang-list-delete", "lang-list-status", "setting-lang_type-list", "setting-lang_type-form", "setting-lang_type-save", "setting-lang_type-list-646a36876cfbe", "setting-lang_type-form-646a36876cfd9", "setting-lang_type-save-646a36876cfe3", "setting-lang_type-list-646a369c9741e", "setting-lang_type-del", "setting-lang_type-list-646a36a88444d", "setting-lang_type-status", "lang-info-add", "lang-info-edit", "lang-info-delete", "setting-lang_code-list", "setting-lang_code-info", "setting-lang_code-save", "setting-lang_code-translate", "setting-lang_code-list-646a3717e64bb", "setting-lang_code-info-646a3717e64c8", "setting-lang_code-save-646a3717e64d1", "setting-lang_code-translate-646a3717e64db", "setting-lang_code-list-646a372511b5e", "setting-lang_code-del", "lang-country-add", "lang-country-edit", "lang-country-delete", "setting-lang_country-list", "setting-lang_country-form", "setting-lang_country-save", "setting-lang_country-list-646a377fb08e5", "setting-lang_country-form-646a377fb08f2", "setting-lang_country-save-646a377fb08fc", "setting-lang_country-list-646a378a8e1b7", "setting-lang_country-del", "out-account-add", "out-account-edit", "out-account-delete", "out-account-setting", "setting-system_out_account-index", "setting-system_out_account-save", "setting-system_out_account-index-646a385546279", "setting-system_out_account-save-646a385546286", "setting-system_out_account-update", "setting-system_out_account-index-646a386512a5e", "setting-system_out_account", "setting-system_out_account-index-646a38719e553", "setting-system_out_account-set_up", "express-sync", "express-edit", "freight-express", "freight-express-sync_express", "freight-express-646a38ef9dbdd", "freight-express-edit", "freight-express-646a38ef9dbf3", "system-city-add", "system-city-edit", "system-city-delete", "system-city-clear-cache", "setting-city-full_list", "setting-city-list", "setting-city-add", "setting-city-save", "setting-city-full_list-646a39aa87ef0", "setting-city-list-646a39aa87efe", "setting-city-edit", "setting-city-save-646a39aa87f12", "setting-city-full_list-646a39c191645", "setting-city-list-646a39c191651", "setting-city-del", "setting-city-full_list-646a39ceb8c4c", "setting-city-list-646a39ceb8c57", "setting-city-clean_cache", "setting-y<PERSON><PERSON><PERSON><PERSON>", "setting-yih<PERSON>tong-config", "setting-lang-config", "admin-marketing-recharge", "setting-kefu-config", "setting-recharge-config", "admin-marketing-sign", "system-code-data_dictionary", "system-crud-data_dictionary", "system-crud-data_dictionary", "system-crud-data_dictionary", "system-crud-data_dictionary", "system-crud-data_dictionary-64d491f3da358", "system-crud-data_dictionary-64d491f3da391", "system-crud-data_dictionary-64d491f3da3b8", "system-crud-association_table", "system-crud-association_table", "system-model-system_config", "system-event-index", "admin-marketing-new-user-gift", "admi-setting-ticket", "admin-marketing-lottery-list", "admin-product-param-list", "admin-setting-pages-link", "admin-product-label-list", "admin-product-protection-list", "marketing-store_seckill-list", "admin-marketing-lottery-config"], "user_info": {"id": 5, "account": "demo", "head_pic": "https://v5.crmeb.net/uploads/attach/2023/09/********/f41769bef07f62a2d3d5e876aba2eb4f.png", "level": 1, "real_name": "demo"}, "logo": "https://v5.crmeb.net/statics/system_images/admin_logo_big.png", "logo_square": "https://v5.crmeb.net/statics/system_images/admin_logo_small.png", "version": "CRMEB-BZ v5.6.3", "newOrderAudioLink": "", "queue": true, "timer": true, "site_name": "CRMEB标准版", "site_func": ["seckill", "bargain", "combination"]}