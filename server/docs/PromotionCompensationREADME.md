# 推广收益补偿程序使用说明

## 概述

推广收益补偿程序用于修复系统中缺失或状态不正确的推广收益数据。该程序包含两个主要功能：

1. **补偿缺失的推广收益数据** - 为有推广关系但缺少推广收益记录的订单创建收益记录
2. **更新已确认收货订单的推广收益状态** - 将已确认收货订单的推广收益状态从"pending"更新为"confirmed"

## 功能特性

1. **自动补偿**: 自动识别并补偿缺失的推广收益数据
2. **状态更新**: 自动更新已确认收货订单的推广收益状态
3. **定时执行**: 支持定时任务自动执行补偿和状态更新
4. **手动执行**: 提供管理接口支持手动执行补偿操作
5. **安全操作**: 支持预览和统计功能，确保操作安全
6. **数据修复**: 自动修复订单表中缺失的promoter_id字段
7. **批量处理**: 支持批量处理大量数据，避免系统压力
8. **分批执行**: 大量数据分批处理，支持暂停和恢复

## 补偿范围

程序能够处理以下两种情况的订单：

1. **订单表中有promoter_id但缺少推广收益记录的订单**
2. **订单表中没有promoter_id，但用户表中有推广者关系的订单**

对于第二种情况，程序会自动从用户表获取推广者ID并更新到订单表中，然后再创建推广收益记录。

## 定时任务

系统配置了多个定时任务来自动处理推广数据问题：

1. **每日数据修复任务** - 每天凌晨2点执行
   - 批量补偿缺失的推广收益数据（每批100条）

2. **每日状态更新任务** - 每天凌晨3点执行
   - 更新已确认收货订单的推广收益状态

3. **每周全面检查任务** - 每周一凌晨4点执行
   - 全面检查并修复所有推广数据问题

4. **每小时快速检查任务** - 每小时执行一次（仅在开发/测试环境）
   - 快速检查并处理新出现的缺失数据

## 使用方法

### 方法一：通过管理接口（推荐）

#### 1. 补偿缺失的推广收益数据
```http
POST /admin/promotion/compensation/compensate-missing
```

#### 2. 批量补偿缺失的推广收益数据
```http
POST /admin/promotion/compensation/compensate-missing-batch?batchSize=100
```

#### 3. 更新已确认收货订单的推广收益状态
```http
POST /admin/promotion/compensation/update-confirmed-status
```

#### 4. 获取补偿统计信息
```http
GET /admin/promotion/compensation/stats
```

### 方法二：通过定时任务

系统已配置两个定时任务自动执行补偿操作：

1. **推广收益数据补偿任务** - 每天凌晨2点执行
2. **推广收益状态更新任务** - 每天凌晨3点执行

### 方法三：通过代码调用

可以直接调用`PromotionCompensationService`中的方法：

```java
@Autowired
private PromotionCompensationService promotionCompensationService;

// 补偿缺失的推广收益数据
Map<String, Object> result1 = promotionCompensationService.compensateMissingEarnings();

// 更新已确认收货订单的推广收益状态
Map<String, Object> result2 = promotionCompensationService.updateConfirmedEarningsStatus();
```

## 补偿逻辑

### 数据来源
- **订单表**: 查询所有有推广者ID但缺少推广收益记录的订单
- **用户表**: 获取推广者和被推广者的信息
- **订单商品表**: 获取订单商品信息用于佣金计算

### 补偿规则
1. **推广者ID**: 来自订单表的 `promoter_id` 字段
2. **被推广用户ID**: 来自订单表的 `user_id` 字段
3. **订单金额**: 使用订单的 `actual_price`（实际支付金额）
4. **佣金计算**: 根据商品是否支持阶梯推广，使用相应的佣金计算方式
5. **状态判断**: 
   - 已确认收货的订单：收益状态为 "confirmed"
   - 已发货但未确认收货的订单：收益状态为 "pending"
   - 已支付但未发货的订单：收益状态为 "pending"

### 时间设置
- **订单创建时间**: 来自订单的 `create_time`
- **确认时间**: 来自订单的 `confirm_receive_time`
- **生效时间**: 与确认时间相同（仅当状态为 confirmed 时）

## 注意事项

1. **数据备份**: 执行补偿前请备份相关数据表
2. **重复执行**: 程序会自动检查重复记录，可以安全地重复执行
3. **性能考虑**: 大量数据补偿时可能需要较长时间，建议在业务低峰期执行
4. **权限控制**: 管理员接口需要相应的权限控制
5. **日志监控**: 补偿过程会产生详细日志，请注意监控

## 错误处理

- **数据不一致**: 如果发现数据不一致，请检查用户推广关系和订单数据
- **补偿失败**: 查看日志了解具体错误原因，可能需要手动处理异常数据
- **性能问题**: 大量数据时可考虑分批处理

## 示例响应

### 批量补偿执行结果
```json
{
  "success": true,
  "message": "批量推广收益数据补偿完成",
  "totalProcessed": 1500,
  "compensatedOrders": 1200,
  "skippedOrders": 250,
  "errorOrders": 50
}
```

### 状态更新结果
```json
{
  "success": true,
  "message": "推广收益状态更新完成",
  "totalOrders": 800,
  "updatedRecords": 750,
  "errorRecords": 50
}
```

### 统计信息
```json
{
  "earningsRecordCount": 1200,
  "promotedUserCount": 150,
  "ordersWithoutEarningsCount": 50,
  "pendingEarningsCount": 200,
  "ordersWithUserPromoterOnlyCount": 30
}
```

## 批量处理优势

1. **性能优化**: 分批处理避免一次性加载大量数据到内存
2. **系统稳定**: 减少数据库压力，避免长时间锁表
3. **可恢复性**: 支持中断后继续处理，无需重新开始
4. **资源控制**: 可以根据系统负载调整批处理大小
5. **进度监控**: 可以实时查看处理进度和状态

## 维护建议

1. **定期检查**: 定期检查推广收益数据的完整性
2. **监控异常**: 监控补偿过程中的异常情况
3. **数据校验**: 定期校验推广收益数据与订单数据的一致性
4. **性能优化**: 根据数据量调整补偿策略和批处理大小