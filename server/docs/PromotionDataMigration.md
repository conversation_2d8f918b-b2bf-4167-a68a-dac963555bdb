# 推广数据迁移工具使用说明

## 概述

推广数据迁移工具用于将历史订单数据迁移到推广收益表（`weshop_promotion_earnings`）中，为推广功能提供历史数据支持。

## 功能特性

1. **自动数据迁移**: 根据用户的推广关系，自动为历史订单创建推广收益记录
2. **状态智能判断**: 根据订单状态自动判断推广收益的状态（pending/confirmed）
3. **重复检查**: 避免重复创建推广收益记录
4. **统计报告**: 提供详细的迁移统计信息
5. **安全操作**: 支持预览和清空操作

## 使用方法

### 方法一：通过管理员接口

#### 1. 预览迁移数据
```http
GET /admin/promotion/migration/preview
```

#### 2. 获取统计信息
```http
GET /admin/promotion/migration/stats
```

#### 3. 执行数据迁移
```http
POST /admin/promotion/migration/execute
```

#### 4. 清空推广收益数据（谨慎使用）
```http
DELETE /admin/promotion/migration/clear?confirm=true
```

### 方法二：通过命令行工具

启动应用时添加参数：
```bash
java -jar your-app.jar --migration.promotion.enabled=true
```

## 迁移逻辑

### 数据来源
- **用户表**: 查询所有有推广关系的用户（`promoter_id` 不为空）
- **订单表**: 查询被推广用户的所有订单

### 迁移规则
1. **推广者ID**: 来自用户表的 `promoter_id` 字段
2. **被推广用户ID**: 订单的 `user_id`
3. **订单金额**: 使用订单的 `actual_price`（实际支付金额）
4. **佣金比例**: 固定为 10%
5. **佣金金额**: 订单金额 × 佣金比例

### 状态判断
- **confirmed**: 订单已确认收货（`confirm_receive_time` 不为空）
- **pending**: 订单已支付或已发货但未确认收货

### 时间设置
- **订单创建时间**: 来自订单的 `create_time`
- **确认时间**: 来自订单的 `confirm_receive_time`
- **生效时间**: 与确认时间相同（仅当状态为 confirmed 时）

## 数据结构

### 推广收益记录字段
```sql
CREATE TABLE `weshop_promotion_earnings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `promoter_id` int NOT NULL COMMENT '推广者用户ID',
  `promoted_user_id` int NOT NULL COMMENT '被推广用户ID',
  `order_id` int NOT NULL COMMENT '订单ID',
  `order_no` varchar(50) NOT NULL COMMENT '订单编号',
  `order_amount` decimal(10,2) NOT NULL COMMENT '订单金额',
  `commission_rate` decimal(5,2) NOT NULL DEFAULT '10.00' COMMENT '佣金比例',
  `commission_amount` decimal(10,2) NOT NULL COMMENT '佣金金额',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '收益状态',
  `order_create_time` datetime NOT NULL COMMENT '订单创建时间',
  `confirm_time` datetime DEFAULT NULL COMMENT '确认收货时间',
  `effective_time` datetime DEFAULT NULL COMMENT '收益生效时间',
  `description` varchar(200) DEFAULT NULL COMMENT '描述信息',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
```

## 注意事项

1. **数据备份**: 执行迁移前请备份相关数据表
2. **重复执行**: 工具会自动检查重复记录，可以安全地重复执行
3. **性能考虑**: 大量数据迁移时可能需要较长时间，建议在业务低峰期执行
4. **权限控制**: 管理员接口需要相应的权限控制
5. **日志监控**: 迁移过程会产生详细日志，请注意监控

## 错误处理

- **数据不一致**: 如果发现数据不一致，请检查用户推广关系和订单数据
- **迁移失败**: 查看日志了解具体错误原因，可能需要手动处理异常数据
- **性能问题**: 大量数据时可考虑分批处理

## 示例响应

### 迁移执行结果
```json
{
  "success": true,
  "message": "推广数据迁移完成",
  "totalOrders": 1500,
  "migratedOrders": 1200,
  "skippedOrders": 250,
  "errorOrders": 50
}
```

### 统计信息
```json
{
  "earningsRecordCount": 1200,
  "promotedUserCount": 150,
  "totalOrderCount": 1500,
  "migrationRate": "80.00%"
}
```

## 维护建议

1. **定期检查**: 定期检查推广收益数据的完整性
2. **监控异常**: 监控迁移过程中的异常情况
3. **数据校验**: 定期校验推广收益数据与订单数据的一致性
4. **性能优化**: 根据数据量调整迁移策略和批处理大小
