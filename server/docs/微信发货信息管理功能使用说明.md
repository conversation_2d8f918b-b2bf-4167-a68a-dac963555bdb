# 微信小程序发货信息管理功能使用说明

## 功能概述

本功能实现了微信小程序发货信息管理服务的完整对接，包括：

1. **发货信息录入** - 向微信平台上传订单发货信息
2. **发货状态查询** - 查询订单在微信平台的发货状态
3. **确认收货提醒** - 提醒用户确认收货
4. **消息跳转设置** - 设置用户点击发货消息时的跳转路径
5. **服务状态检查** - 检查小程序是否已开通发货信息管理服务

## 前置条件

### 1. 微信小程序配置
确保在 `application.yml` 中正确配置了微信小程序信息：

```yaml
wx:
  miniapp:
    configs:
      - appid: your_miniapp_appid  # 替换为您的小程序appid
        secret: your_miniapp_secret # 替换为您的小程序secret
```

### 2. 数据库表结构
运行数据库迁移脚本 `V8.0__Create_wx_shipping_info_table.sql` 创建相关表：

- `weshop_wx_shipping_info` - 微信发货信息表
- `weshop_wx_shipping_config` - 微信发货配置表
- 为 `weshop_order` 表添加微信相关字段
- 为 `weshop_order_express` 表添加微信上传状态字段

### 3. 微信小程序后台设置
1. 登录微信小程序后台
2. 开通"发货信息管理服务"
3. 完成交易结算管理确认

## API接口说明

### 后端接口

#### 1. 发货信息录入
```
POST /admin/wx/shipping/upload/order/{orderId}
```
根据订单ID自动录入发货信息到微信平台。

#### 2. 手动录入发货信息
```
POST /admin/wx/shipping/upload
Content-Type: application/json

{
  "orderKey": {
    "orderNumberType": 1,  // 1-商户订单号，2-微信支付单号
    "mchid": "商户号",
    "outTradeNo": "商户订单号"
  },
  "logisticsType": 1,      // 1-实体物流，2-同城配送，3-虚拟商品，4-用户自提
  "deliveryMode": 1,       // 1-统一发货，2-分拆发货
  "shippingList": [{
    "trackingNo": "物流单号",
    "expressCompany": "物流公司编码",
    "itemDesc": "商品描述"
  }],
  "payer": {
    "openid": "用户openid"
  }
}
```

#### 3. 查询发货信息列表
```
GET /admin/wx/shipping/list?page=1&size=20&orderId=123&wxStatus=1
```

#### 4. 查询订单发货状态
```
GET /admin/wx/shipping/order/status?transactionId=微信支付单号
```

#### 5. 确认收货提醒
```
POST /admin/wx/shipping/notify/confirm-receive?transactionId=微信支付单号
```

#### 6. 设置消息跳转路径
```
POST /admin/wx/shipping/set-jump-path?path=pages/order/detail
```

#### 7. 检查服务开通状态
```
GET /admin/wx/shipping/service/status
```

### 前端页面

访问管理后台的微信发货信息管理页面：
```
/admin/order/wx-shipping
```

## 使用流程

### 1. 检查服务状态
首先检查小程序是否已开通发货信息管理服务：
```bash
curl -X GET "http://localhost:8080/admin/wx/shipping/service/status"
```

### 2. 设置消息跳转路径
设置用户点击发货消息时跳转到的小程序页面：
```bash
curl -X POST "http://localhost:8080/admin/wx/shipping/set-jump-path?path=pages/order/detail"
```

### 3. 录入发货信息
当订单发货后，调用发货信息录入接口：
```bash
curl -X POST "http://localhost:8080/admin/wx/shipping/upload/order/123"
```

### 4. 查询发货状态
查询订单在微信平台的发货状态：
```bash
curl -X GET "http://localhost:8080/admin/wx/shipping/order/status?transactionId=微信支付单号"
```

### 5. 提醒确认收货
当快递签收后，可以提醒用户确认收货：
```bash
curl -X POST "http://localhost:8080/admin/wx/shipping/notify/confirm-receive?transactionId=微信支付单号"
```

## 数据流转

1. **订单创建** → 保存微信支付单号和商户号到订单表
2. **订单发货** → 创建物流信息，调用发货信息录入接口
3. **微信处理** → 微信平台处理发货信息，向用户推送发货通知
4. **用户确认** → 用户在微信中确认收货，或系统自动确认
5. **资金结算** → 微信平台完成资金结算

## 错误处理

### 常见错误码

- `10060001` - 支付单不存在
- `10060002` - 支付单已完成发货，无法继续发货
- `10060004` - 支付单处于不可发货的状态
- `10060031` - 该笔支付单不属于指定的用户

### 错误处理建议

1. **支付单不存在** - 检查订单的微信支付单号是否正确
2. **用户openid不存在** - 确保订单关联的用户有有效的微信openid
3. **物流信息不完整** - 确保订单有完整的物流信息（快递公司、快递单号等）

## 注意事项

1. **发货时效** - 发货时效超过15天的订单需要报备为预售商品订单
2. **重新发货** - 每笔支付单仅有一次重新发货机会
3. **分拆发货** - 分拆发货仅支持物流快递发货，最多分拆成10个包裹
4. **联系方式** - 使用顺丰快递时，联系方式为必填项
5. **手机号掩码** - 联系方式需要采用掩码传输，最后4位数字不能打掩码

## 监控和日志

系统会记录所有微信API调用的日志，包括：
- 请求参数
- 响应结果
- 错误信息
- 处理时间

可以通过查看应用日志来排查问题：
```bash
tail -f logs/application.log | grep "微信发货"
```

## 扩展功能

### 1. 自动发货
可以在订单状态变更时自动调用发货信息录入接口。

### 2. 定时任务
可以创建定时任务，定期检查未上传发货信息的订单并自动上传。

### 3. 消息推送
可以集成企业微信或邮件通知，在发货信息上传失败时及时通知管理员。
