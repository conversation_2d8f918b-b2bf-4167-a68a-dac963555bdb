# 微信发货信息定时同步功能说明

## 功能概述

本功能实现了自动将已发货订单的信息同步到微信平台的定时任务。当订单发货后，系统会定期检查并将发货信息上传到微信小程序发货信息管理服务，确保用户能在微信中及时收到发货通知。

## 功能特性

### 1. 自动同步
- **定时执行**: 每30分钟自动执行一次
- **智能过滤**: 只处理已发货且未同步的订单
- **状态检查**: 避免重复同步已成功上传的订单
- **错误重试**: 失败的订单会在下次执行时重新尝试

### 2. 数据验证
- **订单状态验证**: 只同步状态为"待收货"或"已完成"的订单
- **微信支付单号检查**: 确保订单有有效的微信支付单号
- **快递信息完整性**: 验证快递单号和快递公司代码是否完整
- **用户openid验证**: 确保用户有有效的微信openid

### 3. 安全处理
- **手机号掩码**: 对联系方式进行掩码处理（保留后4位）
- **异常处理**: 完善的异常捕获和日志记录
- **数据保护**: 敏感信息安全传输

## 技术实现

### 定时任务配置
```java
@Scheduled(fixedRate = 30 * 60 * 1000) // 30分钟执行一次
public void syncShippingOrdersToWechat()
```

### 执行流程
1. **查询快递信息**: 获取所有订单的快递信息
2. **订单状态过滤**: 筛选符合条件的订单
3. **重复检查**: 跳过已成功同步的订单
4. **构建同步数据**: 组装微信API所需的数据格式
5. **调用微信API**: 上传发货信息到微信平台
6. **结果记录**: 保存同步结果到数据库

### 数据结构
```java
WxShippingInfoDTO {
    orderKey: {
        orderNumberType: 2,        // 使用微信支付单号
        transactionId: "微信支付单号",
        mchid: "商户号",
        outTradeNo: "商户订单号"
    },
    logisticsType: 1,              // 实体物流配送
    deliveryMode: 1,               // 统一发货
    shippingList: [{
        trackingNo: "快递单号",
        expressCompany: "快递公司代码",
        itemDesc: "商品描述",
        contact: {                 // 顺丰快递时必填
            receiverContact: "****5678"  // 掩码处理的手机号
        }
    }],
    payer: {
        openid: "用户openid"
    }
}
```

## 配置要求

### 1. 微信小程序配置
确保在 `application.yml` 中正确配置了微信小程序信息：
```yaml
wx:
  miniapp:
    configs:
      - appid: your_miniapp_appid
        secret: your_miniapp_secret
```

### 2. 微信后台设置
- 登录微信小程序后台
- 开通"发货信息管理服务"
- 完成交易结算管理确认

### 3. 数据库表
确保以下表存在并有正确的数据：
- `weshop_order` - 订单表（需要transactionId、mchid等字段）
- `weshop_order_express` - 快递信息表
- `weshop_wx_shipping_info` - 微信发货信息记录表
- `weshop_user` - 用户表（需要openid字段）

## 使用方法

### 1. 启动应用
确保Spring Boot应用正常启动，定时任务会自动开始运行。

### 2. 查看日志
定时任务执行时会输出详细日志：
```
开始执行定时同步发货订单到微信任务...
处理订单数: 10, 成功同步数: 8, 跳过数: 2
订单123发货信息同步到微信成功
定时同步发货订单到微信任务执行完成
```

### 3. 监控同步状态
可以通过管理后台查看微信发货信息的同步状态：
- 状态0: 待上传
- 状态1: 上传成功  
- 状态2: 上传失败

## 测试方法

### 1. 单元测试
运行测试类 `WxShippingSyncTaskTest.java`：
```bash
mvn test -Dtest=WxShippingSyncTaskTest
```

### 2. 手动测试
在测试类中调用：
```java
@Test
public void testSyncShippingOrdersToWechat() {
    taskSchedule.syncShippingOrdersToWechat();
}
```

### 3. 服务状态检查
测试微信发货服务是否正常：
```java
@Test
public void testWxShippingServiceStatus() {
    Boolean isTradeManaged = wxOrderShippingService.isTradeManaged();
    System.out.println("服务状态: " + isTradeManaged);
}
```

## 常见问题

### 1. 同步失败
**可能原因**:
- 微信小程序未开通发货信息管理服务
- 订单缺少微信支付单号
- 用户没有有效的openid
- 快递信息不完整

**解决方案**:
- 检查微信后台服务开通状态
- 确保订单是通过微信支付的
- 验证用户微信授权状态
- 补全快递公司代码和快递单号

### 2. 重复同步
系统会自动检查已同步的订单，避免重复上传。如需重新同步，可以将对应记录的wx_status设置为0。

### 3. 手机号格式
顺丰快递要求提供联系方式，系统会自动对手机号进行掩码处理，保留后4位数字。

## 性能优化

### 1. 批量处理
定时任务会批量处理多个订单，提高执行效率。

### 2. 状态缓存
已同步的订单会被标记，避免重复处理。

### 3. 异常隔离
单个订单的同步失败不会影响其他订单的处理。

## 扩展功能

### 1. 自定义同步频率
可以通过修改 `@Scheduled` 注解的参数来调整执行频率：
```java
@Scheduled(fixedRate = 15 * 60 * 1000) // 改为15分钟
```

### 2. 条件同步
可以添加更多的过滤条件，如只同步特定时间段的订单。

### 3. 通知机制
可以集成邮件或企业微信通知，在同步失败时及时提醒管理员。

## 注意事项

1. **网络稳定性**: 确保服务器网络稳定，能正常访问微信API
2. **API限制**: 注意微信API的调用频率限制
3. **数据一致性**: 确保订单和快递信息的数据一致性
4. **日志监控**: 定期检查日志，及时发现和处理异常
5. **备份机制**: 建议定期备份微信发货信息数据
