# 多规格商品Excel填写示例

## 快速上手指南

### 第一步：下载模板
通过接口 `GET /adminapi/product/product/import/template` 下载Excel模板

### 第二步：选择规格类型
- **单规格商品**：规格类型填写 `0`，只需填写基本的价格、库存等字段
- **多规格商品**：规格类型填写 `1`，需要填写规格定义和SKU列表

### 第三步：填写多规格商品数据

## 实际填写示例

### 示例1：T恤（颜色×尺寸）

**基本信息填写：**
```
商品名称*: 时尚T恤
商品分类ID*: 1002
规格类型: 1
```

**规格定义字段填写：**
```json
[{"value":"颜色","detail":[{"value":"红色","pic":""},{"value":"蓝色","pic":""}]},{"value":"尺寸","detail":[{"value":"S","pic":""},{"value":"M","pic":""},{"value":"L","pic":""}]}]
```

**SKU列表字段填写：**
```json
[{"detail":{"颜色":"红色","尺寸":"S"},"price":99,"cost":60,"stock":50,"bar_code":"TS001"},{"detail":{"颜色":"红色","尺寸":"M"},"price":99,"cost":60,"stock":30,"bar_code":"TS002"},{"detail":{"颜色":"红色","尺寸":"L"},"price":99,"cost":60,"stock":20,"bar_code":"TS003"},{"detail":{"颜色":"蓝色","尺寸":"S"},"price":109,"cost":65,"stock":40,"bar_code":"TS004"},{"detail":{"颜色":"蓝色","尺寸":"M"},"price":109,"cost":65,"stock":25,"bar_code":"TS005"},{"detail":{"颜色":"蓝色","尺寸":"L"},"price":109,"cost":65,"stock":15,"bar_code":"TS006"}]
```

### 示例2：手机（内存×颜色）

**基本信息填写：**
```
商品名称*: 智能手机
商品分类ID*: 1001
规格类型: 1
```

**规格定义字段填写：**
```json
[{"value":"内存","detail":[{"value":"64GB","pic":""},{"value":"128GB","pic":""},{"value":"256GB","pic":""}]},{"value":"颜色","detail":[{"value":"黑色","pic":""},{"value":"白色","pic":""}]}]
```

**SKU列表字段填写：**
```json
[{"detail":{"内存":"64GB","颜色":"黑色"},"price":2999,"cost":2200,"stock":100,"bar_code":"PHONE001"},{"detail":{"内存":"64GB","颜色":"白色"},"price":2999,"cost":2200,"stock":80,"bar_code":"PHONE002"},{"detail":{"内存":"128GB","颜色":"黑色"},"price":3499,"cost":2600,"stock":60,"bar_code":"PHONE003"},{"detail":{"内存":"128GB","颜色":"白色"},"price":3499,"cost":2600,"stock":50,"bar_code":"PHONE004"},{"detail":{"内存":"256GB","颜色":"黑色"},"price":3999,"cost":3000,"stock":40,"bar_code":"PHONE005"},{"detail":{"内存":"256GB","颜色":"白色"},"price":3999,"cost":3000,"stock":30,"bar_code":"PHONE006"}]
```

## 简化填写技巧

### 1. 使用Excel公式生成JSON

**步骤1：创建辅助表格**
在Excel的其他sheet中创建规格组合表：

| 颜色 | 尺寸 | 价格 | 成本 | 库存 | 编码 |
|------|------|------|------|------|------|
| 红色 | S | 99 | 60 | 50 | TS001 |
| 红色 | M | 99 | 60 | 30 | TS002 |
| 蓝色 | S | 109 | 65 | 40 | TS004 |

**步骤2：使用公式生成JSON**
```excel
="{""detail"":{""颜色"":"""&A2&""",""尺寸"":"""&B2&"""},""price"":"&C2&",""cost"":"&D2&",""stock"":"&E2&",""bar_code"":"""&F2&"""}"
```

### 2. 在线JSON工具辅助

**推荐工具：**
- https://jsonformatter.org/
- https://www.json.cn/

**使用步骤：**
1. 先在工具中构建JSON结构
2. 验证格式正确性
3. 复制到Excel中

### 3. 文本编辑器辅助

**使用VS Code或Notepad++：**
1. 新建JSON文件
2. 编写规格定义和SKU列表
3. 使用格式化功能验证
4. 复制到Excel

## 常见问题解决

### 问题1：JSON格式错误
**错误示例：**
```json
{value:"颜色",detail:[{value:"红色"}]}  // 缺少引号和外层数组
```

**正确格式：**
```json
[{"value":"颜色","detail":[{"value":"红色","pic":""}]}]
```

### 问题2：规格值不匹配
**错误：** 规格定义中是"红色"，SKU中写成"红"
**解决：** 确保规格值完全一致，包括大小写和空格

### 问题3：Excel单元格显示不全
**解决方法：**
1. 调整列宽
2. 启用自动换行
3. 调整行高

### 问题4：特殊字符处理
**注意事项：**
- JSON中的引号必须是英文双引号 `"`
- 避免使用中文引号 `""`
- 特殊字符需要转义

## 验证清单

导入前请检查：

- [ ] 规格类型设置为1
- [ ] 规格定义JSON格式正确
- [ ] SKU列表JSON格式正确
- [ ] 规格值完全匹配
- [ ] 每个SKU都有price和stock字段
- [ ] 商品编码不重复
- [ ] 价格和库存为合理数值

## 测试建议

1. **从简单开始**：先测试2×2的规格组合（如颜色×尺寸各2个值）
2. **逐步增加**：确认简单规格正常后再增加复杂度
3. **小批量测试**：每次导入1-2个商品进行验证
4. **备份数据**：重要数据导入前先备份

## 技术支持

如果遇到问题，请提供：
1. 具体的错误信息
2. 填写的JSON数据
3. 商品的规格组合说明

这样可以更快地定位和解决问题。
