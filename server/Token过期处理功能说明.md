# Token过期处理功能完善说明

## 概述

本次完善了JWT Token的过期处理逻辑，提供了更加精确的异常分类、自动刷新机制、手动刷新接口以及完整的token管理功能。

## 主要改进

### 1. 精确的异常分类

原来所有JWT相关错误都统一返回"请先登陆"，现在区分了不同的异常类型：

- `TOKEN_EXPIRED` (617): 登录已过期，请重新登录
- `TOKEN_INVALID` (618): 无效的登录凭证  
- `TOKEN_REFRESH_FAILED` (619): 登录凭证刷新失败
- `WECHAT_LOGIN_ERROR` (616): 请先登陆（原有）

### 2. 自动刷新机制

- **智能刷新**: 当token剩余有效期小于2天时，自动在响应头中返回新token
- **过期刷新**: 即使token已过期，在1小时内仍可自动刷新
- **透明处理**: 客户端无需特殊处理，拦截器自动完成刷新

### 3. Token管理接口

#### 3.1 手动刷新Token
```
POST /wechat/token/refresh
Headers: X-Weshop-Token: <current_token>
```

响应：
```json
{
  "success": true,
  "data": {
    "token": "new_jwt_token",
    "expiresIn": 604800000,
    "refreshThreshold": 172800000
  }
}
```

#### 3.2 检查Token状态
```
GET /wechat/token/status
Headers: X-Weshop-Token: <current_token>
```

响应：
```json
{
  "success": true,
  "data": {
    "valid": true,
    "userId": "123",
    "remainingTime": 345600000,
    "remainingTimeDesc": "4天0小时",
    "shouldRefresh": false,
    "expiringSoon": false,
    "canRefresh": true,
    "statusDesc": "token有效"
  }
}
```

#### 3.3 验证Token有效性
```
GET /wechat/token/validate
```

#### 3.4 撤销Token
```
POST /wechat/token/revoke
Headers: X-Weshop-Token: <current_token>
```

#### 3.5 获取配置信息
```
GET /wechat/token/config
```

### 4. 配置化管理

在 `application.yml` 中可配置：

```yaml
weshop:
  jwt:
    # JWT密钥
    secret: your_secret_key
    # Token有效期（毫秒），默认7天
    ttl: 604800000
    # Token刷新阈值（毫秒），默认2天
    refresh-threshold: 172800000
    # 是否启用自动刷新
    auto-refresh-enabled: true
    # 是否启用过期token刷新
    expired-refresh-enabled: true
    # 过期token可刷新的时间窗口（毫秒），默认1小时
    expired-refresh-window: 3600000
    # Token请求头名称
    header-name: X-Weshop-Token
    # 新Token响应头名称
    new-token-header-name: X-New-Token
    # 是否在日志中记录token操作
    log-enabled: true
    # 是否启用token状态检查接口
    status-check-enabled: true
    # 是否启用手动刷新接口
    manual-refresh-enabled: true
```

### 5. Token黑名单机制

- **撤销功能**: 支持主动撤销token，撤销后的token将加入黑名单
- **自动清理**: 定时任务每小时清理过期的黑名单条目
- **内存存储**: 当前使用内存存储，生产环境建议使用Redis

## 核心组件

### 1. JwtHelper (增强版)
- 精确的异常处理
- 支持过期token解析
- 智能刷新判断

### 2. TokenUtils (新增)
- Token操作工具类
- 提供各种便捷方法
- 统一的token处理逻辑

### 3. TokenService (新增)
- Token业务逻辑服务
- 黑名单管理
- 配置化支持

### 4. TokenConfig (新增)
- 配置属性类
- 支持动态配置
- 类型安全

### 5. LoginInterceptor (优化)
- 简化逻辑
- 使用TokenUtils
- 更好的错误处理

## 客户端集成

### 1. 响应头处理

客户端需要监听响应头中的 `X-New-Token`，如果存在则更新本地存储的token：

```javascript
// 响应拦截器示例
axios.interceptors.response.use(
  response => {
    const newToken = response.headers['x-new-token'];
    if (newToken) {
      // 更新本地存储的token
      localStorage.setItem('token', newToken);
      console.log('Token已自动刷新');
    }
    return response;
  },
  error => {
    if (error.response?.data?.code === '617') {
      // Token过期，跳转到登录页
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);
```

### 2. 主动刷新

```javascript
// 主动刷新token
async function refreshToken() {
  try {
    const response = await axios.post('/wechat/token/refresh');
    const newToken = response.data.data.token;
    localStorage.setItem('token', newToken);
    return newToken;
  } catch (error) {
    // 刷新失败，跳转到登录页
    window.location.href = '/login';
  }
}
```

### 3. 状态检查

```javascript
// 检查token状态
async function checkTokenStatus() {
  try {
    const response = await axios.get('/wechat/token/status');
    const status = response.data.data;
    
    if (!status.valid) {
      console.log('Token无效:', status.error);
      return false;
    }
    
    if (status.shouldRefresh) {
      console.log('建议刷新token，剩余时间:', status.remainingTimeDesc);
      await refreshToken();
    }
    
    return true;
  } catch (error) {
    console.error('检查token状态失败:', error);
    return false;
  }
}
```

## 日志记录

系统会记录以下token操作日志：

- Token创建
- Token刷新（自动/手动）
- Token撤销
- Token验证失败
- 黑名单清理

日志级别：
- INFO: 成功操作
- WARN: 失败操作、过期等
- ERROR: 系统错误

## 监控建议

1. **Token刷新频率**: 监控自动刷新和手动刷新的频率
2. **过期率**: 监控token过期的比例
3. **黑名单大小**: 监控黑名单条目数量
4. **异常分布**: 监控各种token异常的分布

## 安全考虑

1. **密钥管理**: JWT密钥应该定期轮换
2. **传输安全**: 确保HTTPS传输
3. **存储安全**: 客户端安全存储token
4. **日志脱敏**: 避免在日志中记录完整token

## 性能优化

1. **内存使用**: 黑名单使用内存存储，注意内存使用量
2. **定时清理**: 定时清理过期的黑名单条目
3. **缓存策略**: 可考虑使用Redis缓存token状态
4. **批量操作**: 支持批量token操作

## 升级注意事项

1. **向后兼容**: 保持与现有客户端的兼容性
2. **渐进升级**: 可以逐步启用新功能
3. **配置迁移**: 旧配置会自动使用默认值
4. **监控部署**: 部署后密切监控token相关指标

## 故障排查

### 常见问题

1. **Token频繁过期**: 检查系统时间同步
2. **刷新失败**: 检查配置和网络连接
3. **黑名单过大**: 检查清理任务是否正常运行
4. **性能问题**: 检查token验证频率和缓存策略

### 调试工具

- `/wechat/token/status`: 检查token状态
- `/wechat/token/config`: 查看当前配置
- 日志分析: 查看token操作日志
- 监控面板: 查看token相关指标
