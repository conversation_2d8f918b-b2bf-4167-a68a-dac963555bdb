@echo off
echo ========================================
echo    Spring Boot 热部署开发模式启动
echo ========================================
echo.

REM 设置环境变量
set SPRING_PROFILES_ACTIVE=dev
set JAVA_OPTS=-Xms512m -Xmx1024m -XX:+UseG1GC

echo 当前配置:
echo - 环境: %SPRING_PROFILES_ACTIVE%
echo - JVM参数: %JAVA_OPTS%
echo - 端口: 9999
echo - 热部署: 启用
echo - LiveReload: 启用 (端口35729)
echo.

echo 启动Spring Boot应用...
echo 提示: 修改Java文件后会自动重启服务
echo 提示: 按 Ctrl+C 停止服务
echo.

REM 启动应用
mvn spring-boot:run -Dspring.profiles.active=dev

pause
