# 订单信息更新工具使用说明

## 功能说明
本工具用于根据Excel文件中的数据更新订单表的支付时间和交易单号。

## Excel文件格式要求
Excel文件需要包含以下三个字段（列）：
1. 支付时间 - 订单的支付时间
2. 交易单号 - 微信支付的交易单号
3. 商户单号 - 商户系统中的订单号（对应订单表的order_sn字段）

## 使用方法

### 方法一：使用Spring Boot版本（需要运行完整的应用程序）
```bash
java -jar order-update-tool.jar "E:\project\code\myself\wjsy_shop\server\src\main\resources\导出订单_2025_08_10_20_29_39.xlsx"
```

### 方法二：交互式运行Spring Boot版本
```bash
java -jar order-update-tool.jar
```
然后根据提示输入Excel文件路径。

### 方法三：通过API调用
发送POST请求到：
```
POST /admin/order/update-from-excel?filePath=E:\project\code\myself\wjsy_shop\server\src\main\resources\导出订单_2025_08_10_20_29_39.xlsx
```

### 方法四：使用独立工具版本（不需要运行完整的应用程序）
1. 首先构建独立工具jar包：
```bash
mvn clean package -P order-update-tool
```

2. 运行独立工具：
```bash
java -jar target/wjsy-1.0-jar-with-dependencies.jar "E:\project\code\myself\wjsy_shop\server\src\main\resources\导出订单_2025_08_10_20_29_39.xlsx"
```

**注意：** 使用独立工具版本时，需要在 `SimpleOrderUpdateUtil.java` 文件中修改数据库连接配置：
```java
// 数据库连接配置 - 请根据实际情况修改
private static final String DB_URL = "*****************************************";
private static final String DB_USER = "your_username";
private static final String DB_PASSWORD = "your_password";
```

## 注意事项
1. Excel文件必须是.xlsx格式
2. 第一行为标题行，数据从第二行开始
3. 商户单号是必须的，用于匹配数据库中的订单
4. 支付时间和交易单号为空时不会更新对应字段
5. 程序会跳过无法解析的行并记录错误日志

## 构建可执行jar包

### 构建Spring Boot版本：
```bash
mvn clean package
```
生成的jar包位于 target/ 目录下。

### 构建独立工具版本：
```bash
mvn clean package -P order-update-tool
```
生成的jar包位于 target/ 目录下，文件名为 `wjsy-1.0-jar-with-dependencies.jar`。