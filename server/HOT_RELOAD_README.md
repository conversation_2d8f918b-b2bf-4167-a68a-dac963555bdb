# Spring Boot 热部署配置说明

## 🚀 快速启动

### Windows 用户
```bash
# 进入server目录
cd server

# 运行热部署启动脚本
start-dev.bat
```

### Linux/Mac 用户
```bash
# 进入server目录
cd server

# 给脚本执行权限
chmod +x start-dev.sh

# 运行热部署启动脚本
./start-dev.sh
```

### 手动启动
```bash
# 使用Maven启动开发环境
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

## 📋 功能特性

### ✅ 已配置的热部署功能

1. **自动重启**
   - 修改Java文件后自动重启应用
   - 监控 `src/main/java` 和 `src/main/resources` 目录
   - 排除静态资源文件，避免不必要的重启

2. **LiveReload**
   - 浏览器自动刷新功能
   - 端口：35729
   - 需要浏览器安装LiveReload插件

3. **开发环境优化**
   - 端口：9999（避免与生产环境冲突）
   - 禁用SSL（简化开发）
   - 显示SQL语句和参数
   - 详细的调试日志

## 🔧 配置文件说明

### application-dev.yml
开发环境专用配置，包含：
- 热部署设置
- 数据库连接池优化
- 日志级别配置
- 调试模式启用

### pom.xml
添加了 `spring-boot-devtools` 依赖：
```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-devtools</artifactId>
    <scope>runtime</scope>
    <optional>true</optional>
</dependency>
```

## 🎯 使用说明

### 1. 启动服务
使用上述任一方式启动服务后，控制台会显示：
```
Spring Boot 热部署开发模式启动
- 环境: dev
- 端口: 9999
- 热部署: 启用
- LiveReload: 启用 (端口35729)
```

### 2. 测试热部署
1. 修改任意Java文件（如Controller、Service等）
2. 保存文件
3. 观察控制台，应该会显示重启信息
4. 无需手动重启，修改会自动生效

### 3. 前端LiveReload（可选）
1. 安装浏览器LiveReload插件
2. 访问 http://localhost:9999
3. 启用LiveReload插件
4. 修改前端文件时浏览器会自动刷新

## 🛠️ IDE 配置

### IntelliJ IDEA
1. 已创建运行配置：`Spring Boot Dev`
2. 启用自动编译：
   - File → Settings → Build → Compiler → Build project automatically
   - File → Settings → Advanced Settings → Allow auto-make to start even if developed application is currently running

### Eclipse/STS
1. 项目右键 → Run As → Spring Boot App
2. 在Run Configurations中设置Active profiles为 `dev`

## 📊 性能优化

### JVM参数
```
-Xms512m -Xmx1024m -XX:+UseG1GC
```

### 数据库连接池
- 最大连接数：5（开发环境够用）
- 最小空闲连接：2
- 连接超时：20秒

## 🚨 注意事项

1. **仅开发环境使用**
   - 热部署功能仅在开发环境启用
   - 生产环境会自动禁用

2. **重启触发条件**
   - 修改Java源文件
   - 修改配置文件
   - 添加/删除依赖

3. **不会重启的情况**
   - 修改静态资源（CSS、JS、图片等）
   - 修改模板文件（如果配置了模板缓存禁用）

4. **内存使用**
   - 热部署会增加内存使用
   - 如果内存不足，可以调整JVM参数

## 🔍 故障排除

### 热部署不工作
1. 检查DevTools依赖是否正确添加
2. 确认使用dev配置启动
3. 检查IDE的自动编译设置

### 重启过于频繁
1. 检查exclude配置
2. 调整poll-interval和quiet-period参数

### 端口冲突
1. 修改application-dev.yml中的端口配置
2. 检查LiveReload端口35729是否被占用

## 📞 技术支持

如有问题，请检查：
1. 控制台日志输出
2. application-dev.yml配置
3. IDE自动编译设置
4. 防火墙和端口占用情况
