# 邮件发送功能使用说明

## 概述

本系统提供了完整的邮件发送功能，支持发送文本邮件、HTML邮件、带附件邮件等多种类型。特别针对生成的文件（如Excel报表、PDF文档等）提供了专门的发送方法。

## 配置说明

### 1. 邮件服务器配置

在 `application.yml` 中配置邮件服务器信息：

```yaml
spring:
  mail:
    host: smtp.qq.com  # 邮件服务器地址
    port: 587  # 邮件服务器端口
    username: <EMAIL>  # 发送方邮箱地址
    password: your-email-password  # 邮箱密码或授权码
    default-encoding: UTF-8
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
```

### 2. 应用邮件配置

```yaml
app:
  email:
    default-from: <EMAIL>  # 默认发送方邮箱
    default-from-name: 五金商城系统  # 默认发送方名称
    default-recipients:  # 默认收件人列表（系统通知）
      - <EMAIL>
      - <EMAIL>
    attachment:
      max-size: 25  # 附件最大大小（MB）
```

## 功能特性

### 1. 基础邮件发送

- **简单文本邮件**：发送纯文本内容
- **HTML邮件**：发送富文本格式邮件
- **带附件邮件**：支持多个附件发送
- **批量发送**：支持多个收件人

### 2. 专门的文件发送功能

- **生成文件发送**：专门用于发送系统生成的文件
- **报表文件发送**：针对各种报表文件的发送
- **自动格式化**：自动生成美观的邮件内容
- **文件信息展示**：自动显示文件大小、生成时间等信息

## 使用方法

### 1. 注入服务

```java
@Autowired
private EmailService emailService;
```

### 2. 发送简单邮件

```java
// 发送给单个收件人
boolean success = emailService.sendSimpleEmail(
    "<EMAIL>", 
    "邮件主题", 
    "邮件内容"
);

// 发送给多个收件人
String[] recipients = {"<EMAIL>", "<EMAIL>"};
boolean success = emailService.sendSimpleEmail(recipients, "邮件主题", "邮件内容");
```

### 3. 发送HTML邮件

```java
String htmlContent = emailService.buildHtmlContent("邮件标题", "<p>HTML内容</p>");
boolean success = emailService.sendHtmlEmail("<EMAIL>", "邮件主题", htmlContent);
```

### 4. 发送带附件的邮件

```java
String[] attachments = {"/path/to/file1.xlsx", "/path/to/file2.pdf"};
boolean success = emailService.sendEmailWithAttachment(
    "<EMAIL>", 
    "邮件主题", 
    "邮件内容", 
    attachments
);
```

### 5. 发送生成的文件（推荐）

```java
// 发送订单报表
String[] recipients = {"<EMAIL>"};
boolean success = emailService.sendOrderReport(
    recipients, 
    "/path/to/order_report.xlsx", 
    "2025-07-01 至 2025-07-13", 
    150  // 记录数量
);

// 发送商品统计
boolean success = emailService.sendProductStatistics(
    recipients, 
    "/path/to/product_stats.xlsx", 
    "2025-07-01 至 2025-07-13", 
    200
);

// 发送用户统计
boolean success = emailService.sendUserStatistics(
    recipients, 
    "/path/to/user_stats.xlsx", 
    "2025-07-01 至 2025-07-13", 
    300
);
```

### 6. 发送系统通知

```java
// 发送给默认收件人
boolean success = emailService.sendSystemNotification("系统通知", "通知内容");

// 带附件的系统通知
String[] attachments = {"/path/to/system_report.xlsx"};
boolean success = emailService.sendSystemNotificationWithAttachment(
    "系统报告", 
    "请查看附件中的系统报告", 
    attachments
);
```

## 实际应用示例

### 1. 在订单导出后发送邮件

```java
@Service
public class OrderExportService {
    
    @Autowired
    private EmailService emailService;
    
    public void exportAndSendOrderReport(String dateRange) {
        try {
            // 1. 生成Excel文件
            String filePath = generateOrderExcel(dateRange);
            
            // 2. 发送邮件
            String[] recipients = {"<EMAIL>", "<EMAIL>"};
            boolean success = emailService.sendOrderReport(
                recipients, 
                filePath, 
                dateRange, 
                getOrderCount(dateRange)
            );
            
            if (success) {
                log.info("订单报表邮件发送成功：{}", filePath);
            } else {
                log.error("订单报表邮件发送失败：{}", filePath);
            }
        } catch (Exception e) {
            log.error("导出并发送订单报表失败", e);
        }
    }
}
```

### 2. 在定时任务中使用

```java
@Component
public class ReportScheduler {
    
    @Autowired
    private EmailService emailService;
    
    @Scheduled(cron = "0 0 14 * * ?") // 每天14:00执行
    public void sendDailyReport() {
        try {
            // 生成日报
            String reportPath = generateDailyReport();
            
            // 发送邮件
            boolean success = emailService.sendGeneratedFileToDefault(
                "日报", 
                reportPath, 
                "系统自动生成的日报，包含昨日业务数据统计"
            );
            
            log.info("日报邮件发送结果：{}", success ? "成功" : "失败");
        } catch (Exception e) {
            log.error("发送日报失败", e);
        }
    }
}
```

## API接口

系统提供了REST API接口用于邮件发送：

- `POST /adminapi/email/send/simple` - 发送简单邮件
- `POST /adminapi/email/send/html` - 发送HTML邮件
- `POST /adminapi/email/send/attachment` - 发送带附件邮件
- `POST /adminapi/email/send/generated-file` - 发送生成的文件
- `POST /adminapi/email/send/system-notification` - 发送系统通知
- `GET /adminapi/email/test` - 测试邮件配置

## 测试

运行测试类 `EmailServiceTest` 来验证邮件功能：

```bash
# 运行所有邮件测试
mvn test -Dtest=EmailServiceTest

# 运行特定测试方法
mvn test -Dtest=EmailServiceTest#testSendGeneratedFile
```

## 故障排除

### 1. 邮件认证失败（535错误）

如果遇到邮件认证失败的问题，请按以下步骤排查：

#### QQ邮箱配置步骤：
1. 登录QQ邮箱网页版 (mail.qq.com)
2. 点击"设置" -> "账户"
3. 找到"POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务"
4. 开启"POP3/SMTP服务"
5. 点击"生成授权码"
6. 将生成的授权码填入配置文件的password字段

#### 使用诊断工具：
```bash
# 访问诊断接口
GET /adminapi/email/diagnose

# 发送测试邮件
POST /adminapi/email/test-native?toEmail=<EMAIL>

# 获取QQ邮箱配置信息
GET /adminapi/email/config/qq
```

#### 运行诊断测试：
```bash
# 运行邮件配置诊断
mvn test -Dtest=EmailConfigTest#testDiagnoseEmailConfig

# 测试发送邮件
mvn test -Dtest=EmailConfigTest#testSendEmail
```

### 2. 配置文件示例

正确的application.yml配置：
```yaml
spring:
  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    password: your-qq-auth-code  # 这里是授权码，不是登录密码
    default-encoding: UTF-8
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
          ssl:
            enable: false
        debug: true  # 开启调试模式
```

## 注意事项

1. **邮箱配置**：确保邮箱服务器配置正确，QQ邮箱需要使用授权码而不是登录密码
2. **附件大小**：默认附件最大25MB，可在配置中调整
3. **邮箱格式**：系统会自动验证邮箱地址格式
4. **错误处理**：所有邮件发送方法都会返回boolean结果，建议检查返回值
5. **日志记录**：系统会自动记录邮件发送的成功和失败日志
6. **文件清理**：发送完成后建议清理临时文件

## 常见问题

1. **邮件发送失败**：检查邮箱配置、网络连接、授权码是否正确
2. **附件过大**：调整配置中的max-size参数或压缩文件
3. **HTML格式问题**：使用buildHtmlContent方法生成标准HTML格式
4. **中文乱码**：确保编码设置为UTF-8
5. **535认证失败**：确保使用的是授权码而不是登录密码
6. **连接超时**：检查网络连接和防火墙设置

## 扩展功能

可以根据需要扩展以下功能：

1. **邮件模板**：支持自定义邮件模板
2. **异步发送**：使用@Async注解实现异步邮件发送
3. **发送队列**：实现邮件发送队列，提高系统性能
4. **发送记录**：记录邮件发送历史和状态
5. **重试机制**：实现邮件发送失败重试功能
