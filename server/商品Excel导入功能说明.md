# 商品Excel导入功能说明

## 功能概述

本功能为商品管理系统提供了完整的Excel导入解决方案，包括：
1. **Excel模板生成** - 自动生成包含所有必要字段的标准化Excel模板
2. **Excel数据导入** - 批量导入商品数据到系统
3. **数据验证** - 完整的数据验证和错误处理机制
4. **导入结果反馈** - 详细的导入结果统计和错误报告

## API接口

### 1. 下载导入模板

**接口地址：** `GET /adminapi/product/product/import/template`

**功能：** 下载商品导入Excel模板文件

**响应：** 直接下载Excel文件（.xlsx格式）

**使用示例：**
```javascript
// 前端下载模板
window.open('/adminapi/product/product/import/template');
```

### 2. 导入商品数据

**接口地址：** `POST /adminapi/product/product/import`

**功能：** 上传Excel文件并导入商品数据

**请求参数：**
- `file` (MultipartFile) - Excel文件，支持.xlsx和.xls格式，最大10MB

**响应格式：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "totalRows": 100,
    "successCount": 95,
    "failureCount": 5,
    "skipCount": 0,
    "allSuccess": false,
    "duration": 5000,
    "successList": [...],
    "failureList": [...],
    "errorMessage": null,
    "detailErrors": [...]
  }
}
```

## Excel模板格式

### 必填字段（标记*）
1. **商品名称*** - 商品的显示名称
2. **商品分类ID*** - 必须是系统中存在的分类ID
3. **商品价格*** - 必须大于0的数值
4. **库存数量*** - 不能为负数

### 可选字段
- 商品分类名称 - 用于显示和验证，系统会自动填充
- 商品简介 - 商品的简短描述
- 商品单位 - 如：台、个、件等
- 关键词 - 用于搜索的关键词
- 商品描述 - 详细的商品描述
- 是否上架 - 1表示上架，0表示下架，默认1
- 是否热门 - 1表示热门，0表示普通，默认0
- 是否新品 - 1表示新品，0表示普通，默认0
- 排序值 - 数值越小排序越靠前，默认100
- 虚拟销量 - 显示的销量数字，默认0
- 商品主图URL - 商品主要展示图片的URL
- 商品轮播图URL - 多个图片URL用逗号分隔
- 单价 - 商品单价
- 详情标签 - 多个标签用逗号分隔
- 展示系统ID - 多个系统ID用逗号分隔

### 规格相关字段
- 规格类型 - 0表示单规格，1表示多规格，默认0
- 成本价 - 商品成本价格
- 划线价 - 原价或市场价
- 商品编码 - 商品的唯一编码
- 商品重量 - 单位：KG
- 商品体积 - 单位：m³
- 规格图片URL - 规格对应的图片
- 一级分销佣金 - 分销佣金金额
- 二级分销佣金 - 二级分销佣金金额
- 限购数量 - 每个用户限购数量
- 是否显示限购 - 1表示显示，0表示不显示
- 商品属性 - JSON格式的属性信息

## 使用步骤

### 1. 下载模板
1. 访问模板下载接口获取Excel模板
2. 模板包含所有字段的说明和示例数据
3. 根据示例数据格式填写商品信息

### 2. 填写数据
1. 在模板中填写商品信息
2. 必填字段不能为空
3. 数值字段请填写正确的数字格式
4. 多个值用逗号分隔（如图片URL、标签等）

### 3. 上传导入
1. 保存Excel文件
2. 通过导入接口上传文件
3. 系统会自动验证数据并返回结果

### 4. 查看结果
1. 检查导入结果统计
2. 查看成功导入的商品列表
3. 处理失败的商品数据（查看错误信息）

## 数据验证规则

### 基本验证
- 商品名称不能为空
- 商品分类ID必须存在于系统中
- 商品价格必须大于0
- 库存数量不能为负数

### 格式验证
- 数值字段必须是有效的数字格式
- URL字段建议使用有效的URL格式
- JSON字段必须是有效的JSON格式

### 业务验证
- 分类ID必须在系统中存在
- 展示系统ID必须是有效的系统ID
- 商品编码建议唯一（重复会提示警告）

## 错误处理

### 常见错误类型
1. **数据格式错误** - 数值字段填写了非数字内容
2. **必填字段缺失** - 必填字段为空或未填写
3. **业务规则违反** - 如分类ID不存在、价格为负数等
4. **文件格式错误** - 上传了非Excel格式文件

### 错误信息说明
- 每个错误都会标明具体的行号和错误原因
- 系统会跳过错误行，继续处理其他正确的数据
- 导入完成后会提供详细的错误报告

## 性能说明

- 支持大批量数据导入（建议单次不超过1000条）
- 导入过程中会显示进度和耗时
- 系统会自动处理事务，确保数据一致性
- 临时文件会在处理完成后自动清理

## 注意事项

1. **文件大小限制**：单个文件不超过10MB
2. **数据量建议**：单次导入建议不超过1000条商品
3. **网络稳定性**：大文件上传请确保网络稳定
4. **数据备份**：重要数据导入前建议先备份
5. **权限要求**：需要商品管理相关权限

## 技术实现

### 核心组件
- `GoodsExcelUtils` - Excel处理工具类
- `GoodsImportDTO` - 导入数据传输对象
- `GoodsImportResultDTO` - 导入结果对象
- `GoodsService.importGoodsFromExcel()` - 核心导入方法

### 依赖库
- Apache POI 5.2.4 - Excel文件处理
- Spring Boot - Web框架和文件上传
- MyBatis Plus - 数据库操作

### 扩展性
- 支持自定义验证规则
- 支持扩展字段映射
- 支持自定义错误处理逻辑
- 支持异步导入处理（可扩展）
